import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// GET /api/auth/check-role - Check user role
export async function GET(request: NextRequest) {
  try {
    // Check if we're in build mode
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      return NextResponse.json(
        { success: false, error: 'Service unavailable during build' },
        { status: 503 }
      );
    }

    const supabase = createSupabaseServerClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user role from database
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role, full_name, email')
      .eq('id', user.id)
      .single();

    if (userError) {
      console.error('Error fetching user role:', userError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch user role' },
        { status: 500 }
      );
    }

    // Default role if not set
    const role = userData?.role || 'user';

    return NextResponse.json({
      success: true,
      role,
      user: {
        id: user.id,
        email: user.email,
        full_name: userData?.full_name || user.user_metadata?.full_name,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/auth/check-role:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
