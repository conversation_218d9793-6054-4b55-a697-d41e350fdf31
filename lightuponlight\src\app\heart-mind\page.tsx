import { <PERSON>, <PERSON>, <PERSON>bulb, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Target, Users, Compass } from 'lucide-react'

const wisdomPrinciples = [
  {
    title: 'Emotional Intelligence & Contentment' ,
    description: 'Develop gentle emotional awareness, find natural contentment, and cultivate inner peace through understanding and accepting natural wisdom.',
    logic: 'Emotions provide valuable data about our environment and relationships. True contentment comes from accepting what <PERSON> has decreed while working towards improvement. This balance creates emotional stability.',
    practices: ['Daily gratitude reflection', 'Emotional awareness journaling', 'Contentment meditation', 'Heart-centered breathing', 'Acceptance training'],
    benefits: ['Enhanced emotional stability', 'Increased life satisfaction', 'Reduced anxiety levels', 'Improved relationships'],
    facts: ['Naturally reduces stress hormones', 'Increases positive emotions', 'Improves heart rate variability', 'Enhances overall well-being'],
    icon: Heart,
    color: 'text-red-600'
  },
  {
    title: 'Enhanced Consciousness & Clarity',
    description: 'Develop higher levels of consciousness, achieve crystal-clear mental focus, and expand awareness through mindful practices.',
    logic: 'Consciousness is the foundation of all experience. By expanding awareness and maintaining mental clarity, we can perceive reality more accurately and make better decisions.',
    practices: ['Consciousness expansion meditation', 'Mindfulness training', 'Present-moment awareness', 'Cognitive clarity exercises', 'Awareness journaling'],
    benefits: ['Improved cognitive function', 'Increased mindfulness', 'Better decision-making', 'Enhanced creativity'],
    facts: ['Supports brain health', 'Improves attention span', 'Enhances memory function', 'Increases mental flexibility'],
    icon: Brain,
    color: 'text-blue-600'
  },
  {
    title: 'Sound Heart  Development',
    description: 'Cultivate a pure, sound heart free from spiritual diseases like envy, pride, and hatred through spiritual purification practices.',
    logic: 'The heart is the center of spiritual and emotional life. A sound heart leads to sound decisions, pure intentions, and divine connection. This creates lasting happiness and success.',
    practices: ['Heart purification dhikr', 'Self-accountability (Muhasabah)', 'Spiritual reflection', 'Character development', 'Divine remembrance'],
    benefits: ['Increased spiritual awareness', 'Enhanced inner peace', 'Reduced negative emotions', 'Improved moral clarity'],
    facts: ['Reduces stress naturally', 'Increases positive emotions', 'Improves life satisfaction', 'Enhances spiritual connection'],
    icon: Sun,
    color: 'text-yellow-600'
  },
  {
    title: 'Wisdom Integration & Life Purpose',
    description: 'Combine knowledge, experience, and divine guidance to discover life purpose and achieve meaningful existence through integrated wisdom.',
    logic: 'True wisdom integrates intellectual knowledge, emotional intelligence, spiritual insight, and practical experience. This creates a clear sense of purpose and direction in life.',
    practices: ['Purpose discovery meditation', 'Values clarification', 'Life mission development', 'Wisdom integration exercises', 'Meaningful goal setting'],
    benefits: ['Clearer life direction', 'Increased motivation', 'Better goal achievement', 'Enhanced meaning in life'],
    facts: ['Increases life satisfaction', 'Improves resilience', 'Enhances decision quality', 'Strengthens sense of purpose'],
    icon: Star,
    color: 'text-purple-600'
  },
  {
    title: 'Stress Resilience & Inner Strength',
    description: 'Build unshakeable inner strength, develop immunity to stress, and maintain emotional balance under pressure through resilience training.',
    logic: 'Resilience comes from combining emotional regulation, cognitive flexibility, spiritual grounding, and practical coping skills. This creates unshakeable inner strength.',
    practices: ['Stress inoculation training', 'Resilience building exercises', 'Inner strength meditation', 'Emotional regulation techniques', 'Spiritual grounding'],
    benefits: ['Reduced stress response', 'Increased resilience', 'Better emotional balance', 'Improved coping abilities'],
    facts: ['Naturally reduces cortisol', 'Improves stress recovery', 'Increases mental toughness', 'Enhances emotional stability'],
    icon: Shield,
    color: 'text-green-600'
  }
]

const ancientWisdom = [
  {
    source: 'Greek Philosophy',
    principle: 'Know Thyself',
    explanation: 'Self-knowledge is the foundation of wisdom and personal growth.',
    modernApplication: 'Regular self-reflection and honest self-assessment lead to better decision-making and personal development.'
  },
  {
    source: 'Eastern Philosophy',
    principle: 'Balance and Harmony',
    explanation: 'Life needs balance between different parts - action and rest, thinking and feeling.',
    modernApplication: 'Work-life balance, emotional regulation, and holistic health approaches reflect this ancient wisdom.'
  },
  {
    source: 'Stoic Philosophy',
    principle: 'Focus on What You Control',
    explanation: 'Distinguish between what is within your control and what is not.',
    modernApplication: 'Modern psychology confirms that focusing on controllable factors reduces stress and increases effectiveness.'
  },
  {
    source: 'Universal Wisdom',
    principle: 'Continuous Learning',
    explanation: 'The wise person never stops learning and growing throughout life.',
    modernApplication: 'Lifelong learning and growth mindset are essential for adapting to our rapidly changing world.'
  }
]

const practicalExercises = [
  {
    name: 'Daily Reflection',
    duration: '10-15 minutes',
    description: 'End each day by reflecting on experiences, emotions, and lessons learned.',
    steps: [
      'Find a quiet space without distractions',
      'Review the day\'s events objectively',
      'Identify emotions and their triggers',
      'Extract lessons and insights',
      'Set intentions for tomorrow'
    ]
  },
  {
    name: 'Wisdom-Based Logical Analysis',
    duration: '15-20 minutes',
    description: 'Practice breaking down complex problems through the lens of wisdom, faith, and intelligence.',
    steps: [
      'Begin with seeking guidance through prayer or reflection',
      'Choose a current situation or decision to think about',
      'List all relevant facts and information ',
      'Identify and potential biases',
      'Consider multiple perspectives, including wisdom ',
      'Apply natural law and universal principles',
      'Integrate logical reasoning with spiritual insight',
      'Develop solutions that align with wisdom, faith, and intelligence',
      'Verify conclusions against natural wisdom'
    ]
  },
  {
    name: 'Mindfulness Practice',
    duration: '10-30 minutes',
    description: 'Develop present-moment awareness through focused attention.',
    steps: [
      'Sit comfortably with eyes closed',
      'Focus on natural breathing',
      'Notice thoughts without judgment',
      'Return attention to breath when distracted',
      'End with gratitude reflection'
    ]
  }
]

export default function HeartMindPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 dark:from-pink-900/20 dark:via-purple-900/20 dark:to-blue-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6 space-x-4">
              <Heart className="h-16 w-16 text-red-600" />
              <Brain className="h-16 w-16 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Heart & Mind Development
            </h1>
            <div className="max-w-4xl mx-auto space-y-4">
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Integrate emotional intelligence with logical thinking to develop wisdom,
                clarity, and effective decision-making through time-tested principles and modern insights.
              </p>

            </div>
          </div>
        </div>
      </div>

      {/* Wisdom Quote */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="verse-container text-center">
          <div className="flex justify-center mb-4">
            <Lightbulb className="h-8 w-8 text-yellow-600" />
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4">
            "The heart that feels deeply and the mind that thinks clearly together 
            create the wisdom needed to navigate life's complexities with grace and understanding."
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Universal Wisdom Tradition
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* Intellectual Wisdom & Development */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Logic, Reflection & Intellectual Development
          </h2>
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-8 border border-blue-200 dark:border-blue-800">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">�</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">Natural Thinking</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Good thinking means using both your logical mind and your feelings together. When you think clearly
                  and feel deeply, you make better choices. This helps you understand problems better and find
                  solutions that really work.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🧠</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">Learning Every Day</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Every day brings new chances to learn something useful. When you stay curious and open to new ideas,
                  your mind grows stronger. Reading, asking questions, and trying new things all help build your
                  intelligence naturally.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌟</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">Thinking and Looking Back</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Good understanding comes when you think carefully and look back at what happened. When you notice
                  how things work in nature and life, think about what causes what, and remember your experiences,
                  you learn to think clearly and make good choices.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚖️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">Sound Heart Development</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  A sound heart is one who is aligned to pure truth. When you
                  develop a pure, peaceful heart through good thoughts and kind actions, your mind becomes clearer,
                  your decisions become wiser, and your relationships become stronger.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">�</span>
                  </div>
                </div>
                <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">Deep Understanding</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Real understanding is more than just remembering things. It means seeing how ideas connect to each
                  other, knowing how things work together, and using what you know in good ways. This deep knowing
                  helps you solve problems and make choices that help everyone.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💎</span>
                  </div>
                </div>
                <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">Enlightenment & Development</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Enlightenment comes from combining knowledge with wisdom, understanding with compassion. As you
                  develop both your mind and heart together, you gain insights that help you live better, treat
                  others with kindness, and contribute positively to the world.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Core Principles */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Core Development Principles
          </h2>
          <div className="space-y-8">
            {wisdomPrinciples.map((principle, index) => {
              const Icon = principle.icon
              return (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden"
                >
                  <div className="p-8">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        <Icon className={`h-8 w-8 ${principle.color}`} />
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                            {principle.title}
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400">
                            {principle.description}
                          </p>
                        </div>
                      </div>
                      <Lightbulb className="h-6 w-6 text-yellow-500" />
                    </div>

                    {/* Logic */}
                    <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <h4 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2">
                        Logical Foundation
                      </h4>
                      <p className="text-blue-700 dark:text-blue-300">
                        {principle.logic}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Practices */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Development Practices
                        </h4>
                        <ul className="space-y-2">
                          {principle.practices.map((practice, practiceIndex) => (
                            <li key={practiceIndex} className="flex items-center">
                              <Zap className="h-4 w-4 text-yellow-500 mr-2" />
                              <span className="text-gray-700 dark:text-gray-300">{practice}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Benefits */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Key Benefits
                        </h4>
                        <ul className="space-y-2">
                          {principle.benefits.map((benefit, benefitIndex) => (
                            <li key={benefitIndex} className="flex items-center">
                              <Shield className="h-4 w-4 text-green-500 mr-2" />
                              <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Ancient Wisdom */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Timeless Wisdom Principles
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {ancientWisdom.map((wisdom, index) => (
              <div key={index} className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <Star className="h-6 w-6 text-purple-600 mr-3" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {wisdom.principle}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {wisdom.source}
                    </p>
                  </div>
                </div>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  {wisdom.explanation}
                </p>
                <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded">
                  <p className="text-purple-800 dark:text-purple-300 text-sm">
                    <strong>Modern Application:</strong> {wisdom.modernApplication}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Awe-Inspiring Development Facts */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Amazing Facts About Human Development Potential
          </h2>
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-8 border border-purple-200 dark:border-purple-800">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🧠</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">Unlimited Learning Capacity</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Your brain can create new connections throughout your entire life. Every time you learn something
                  new, your brain physically changes and grows. There's no limit to how much wisdom and knowledge
                  you can gain when you approach learning with patience and humility.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💎</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">Heart-Mind Alignment</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  When your heart and mind work together in harmony, you can access levels of understanding that
                  neither can reach alone. This natural alignment creates a state of clear thinking combined with
                  deep wisdom, leading to decisions that benefit both yourself and others.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌟</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">Natural Intelligence Growth</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Intelligence grows naturally when you remain curious, humble, and open to learning. Like a tree
                  that grows stronger with each season, your understanding deepens with each experience when you
                  approach life with wonder and gratitude.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚖️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">Perfect Balance Living</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Living with balance between thinking and feeling, action and rest, learning and reflecting creates
                  a harmonious life. This natural balance allows you to respond to situations with both wisdom and
                  compassion, making choices that create peace and benefit.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🔄</span>
                  </div>
                </div>
                <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">Continuous Renewal</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Every moment offers a chance for renewal and growth. Your capacity for positive change never ends.
                  With each breath, each thought, each choice, you can align yourself more closely with wisdom,
                  kindness, and understanding.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌱</span>
                  </div>
                </div>
                <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">Infinite Potential</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Your potential for growth, understanding, and positive impact is limitless. Every person has within
                  them the capacity for great wisdom, deep compassion, and meaningful contribution to the world when
                  they nurture their natural gifts with patience and care.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Integration Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Heart-Mind Integration
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <div className="flex items-center mb-4">
                  <Heart className="h-8 w-8 text-red-600 mr-3" />
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Heart Wisdom
                  </h3>
                </div>
                <ul className="space-y-3 text-gray-700 dark:text-gray-300">
                  <li>• Emotional awareness and regulation</li>
                  <li>• Empathy and compassion development</li>
                  <li>• Intuitive understanding and insight</li>
                  <li>• Connection with others and nature</li>
                  <li>• Values-based decision making</li>
                </ul>
              </div>
              <div>
                <div className="flex items-center mb-4">
                  <Brain className="h-8 w-8 text-blue-600 mr-3" />
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Mind Clarity
                  </h3>
                </div>
                <ul className="space-y-3 text-gray-700 dark:text-gray-300">
                  <li>• Logical analysis and reasoning</li>
                  <li>• Gentle thinking and understanding</li>
                  <li>• Problem-solving and planning</li>
                  <li>• Knowledge integration and synthesis</li>
                  <li>• Clear communication and expression</li>
                </ul>
              </div>
            </div>
            <div className="mt-8 text-center">
              <div className="bg-gradient-to-r from-red-50 to-blue-50 dark:from-red-900/20 dark:to-blue-900/20 p-6 rounded-lg">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Integrated Wisdom
                </h4>
                <p className="text-gray-700 dark:text-gray-300">
                  When heart and mind work together, we achieve balanced wisdom that combines 
                  emotional intelligence with logical clarity, leading to better decisions, 
                  deeper relationships, and a more fulfilling life.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Begin Your Development Journey
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Heart and mind development is a lifelong journey that requires consistent practice, 
              self-reflection, and the integration of wisdom from multiple sources and traditions.
            </p>
            <div className="flex justify-center space-x-8">
              <div className="text-center">
                <Heart className="h-8 w-8 text-red-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Emotional Intelligence</p>
              </div>
              <div className="text-center">
                <Brain className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Logical Thinking</p>
              </div>
              <div className="text-center">
                <Star className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Integrated Wisdom</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
