import { Brain, Lightbulb, Zap, Target, Puzzle, <PERSON>Circle, <PERSON><PERSON><PERSON><PERSON>gle, Star } from 'lucide-react'

const logicalPrinciples = [
  {
    name: 'Cause and Effect',
    description: 'Every effect has a cause, and understanding these relationships helps predict outcomes.',
    application: 'Used in science, medicine, engineering, and daily decision-making to understand consequences.',
    benefits: ['Better decision-making', 'Problem prevention', 'Scientific advancement', 'Predictive thinking'],
    example: 'If you study consistently (cause), you will improve your knowledge (effect).',
    icon: Target,
    color: 'text-blue-600'
  },
  {
    name: 'Logical Deduction',
    description: 'Drawing specific conclusions from general principles through valid reasoning.',
    application: 'Mathematics, computer programming, legal reasoning, and scientific research.',
    benefits: ['Accurate conclusions', 'Systematic thinking', 'Error reduction', 'Clear reasoning'],
    example: 'All humans need water. <PERSON> is human. Therefore, <PERSON> needs water.',
    icon: Brain,
    color: 'text-purple-600'
  },
  {
    name: 'Pattern Recognition',
    description: 'Identifying recurring structures, sequences, or relationships in information.',
    application: 'Data analysis, medical diagnosis, market prediction, and learning optimization.',
    benefits: ['Faster learning', 'Trend identification', 'Efficiency improvement', 'Innovation'],
    example: 'Recognizing that symptoms A, B, and C often occur together helps diagnose conditions.',
    icon: Puzzle,
    color: 'text-green-600'
  },
  {
    name: ' Analysis',
    description: ' evaluating information, arguments, and evidence before forming conclusions.',
    application: 'Research, journalism, business strategy, and personal decision-making.',
    benefits: ['Reduced bias', 'Better judgments', 'Informed decisions', 'Truth-seeking'],
    example: 'Examining multiple sources and checking facts before believing or sharing information.',
    icon: CheckCircle,
    color: 'text-orange-600'
  },
  {
    name: 'Systems Thinking',
    description: 'Understanding how parts interact within a whole system and affect each other.',
    application: 'Ecology, economics, organizational management, and complex problem-solving.',
    benefits: ['Holistic understanding', 'Better solutions', 'Unintended consequence prevention', 'Optimization'],
    example: 'Understanding how diet, exercise, sleep, and stress all interact to affect health.',
    icon: Star,
    color: 'text-red-600'
  },
  {
    name: 'Probabilistic Reasoning',
    description: 'Making decisions based on likelihood and uncertainty rather than absolute certainty.',
    application: 'Risk assessment, insurance, weather forecasting, and investment decisions.',
    benefits: ['Risk management', 'Realistic expectations', 'Better planning', 'Uncertainty handling'],
    example: 'Considering the probability of rain when deciding whether to carry an umbrella.',
    icon: Lightbulb,
    color: 'text-yellow-600'
  }
]

const intelligenceTypes = [
  {
    type: 'Analytical Intelligence',
    description: 'The ability to analyze, evaluate, and solve problems systematically.',
    skills: ['Mathematical reasoning', 'Logical analysis', 'Pattern recognition', 'Abstract thinking'],
    development: ['Practice puzzles and games', 'Study mathematics and logic', 'Analyze complex problems', 'Learn programming']
  },
  {
    type: 'Creative Intelligence',
    description: 'The ability to generate novel and useful ideas and solutions.',
    skills: ['Innovative thinking', 'Artistic expression', 'Problem reframing', 'Imagination'],
    development: ['Engage in creative activities', 'Practice brainstorming', 'Explore different perspectives', 'Learn from diverse fields']
  },
  {
    type: 'Practical Intelligence',
    description: 'The ability to adapt to and shape real-world environments effectively.',
    skills: ['Common sense', 'Social skills', 'Adaptation', 'Real-world problem solving'],
    development: ['Gain diverse experiences', 'Practice social interaction', 'Learn from mistakes', 'Apply knowledge practically']
  },
  {
    type: 'Emotional Intelligence',
    description: 'The ability to understand and manage emotions in self and others.',
    skills: ['Self-awareness', 'Empathy', 'Social skills', 'Emotional regulation'],
    development: ['Practice mindfulness', 'Study human behavior', 'Develop empathy', 'Improve communication']
  }
]

const practicalExercises = [
  {
    name: 'Daily Logic Puzzles',
    duration: '15-20 minutes',
    description: 'Solve logic puzzles, sudoku, or brain teasers to strengthen analytical thinking.',
    benefits: ['Improved pattern recognition', 'Enhanced problem-solving', 'Mental agility', 'Concentration']
  },
  {
    name: 'Argument Analysis',
    duration: '20-30 minutes',
    description: 'Read articles or debates and identify logical fallacies, evidence quality, and reasoning strength.',
    benefits: ['Gentle thinking skills', 'Understanding differences', 'Better judgment', 'Wise decision-making']
  },
  {
    name: 'Systems Mapping',
    duration: '30-45 minutes',
    description: 'Choose a complex issue and map out all the interconnected factors and relationships.',
    benefits: ['Holistic thinking', 'Problem understanding', 'Solution identification', 'Strategic planning']
  },
  {
    name: 'Probability Estimation',
    duration: '10-15 minutes',
    description: 'Practice estimating probabilities of daily events and check your accuracy over time.',
    benefits: ['Risk assessment', 'Realistic thinking', 'Decision-making', 'Uncertainty tolerance']
  }
]

export default function LogicPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 dark:from-purple-900/20 dark:via-blue-900/20 dark:to-indigo-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Brain className="h-16 w-16 text-purple-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Logic & Intelligence Development
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Learn simple ways to think clearly and grow your natural understanding.
              Discover peaceful ways to learn, think about things, and grow your mind
              that bring happiness and clear thinking to your life.
            </p>
          </div>
        </div>
      </div>

      {/* Wisdom Quote */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="verse-container text-center">
          <div className="flex justify-center mb-4">
            <Lightbulb className="h-8 w-8 text-yellow-600" />
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4">
            "Natural understanding flows like water, finding its way gently through learning.
            True wisdom comes when your heart and mind work together in peace."
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Natural Wisdom Principle
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* Natural Intelligence Development */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Natural Intelligence & Pure Understanding
          </h2>
          <div className="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-lg p-8 border border-blue-200 dark:border-blue-800 mb-12">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌱</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">Natural Growth</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Your mind grows naturally when you stay open and curious. Like a plant growing toward the sun,
                  your understanding gets bigger slowly through watching carefully and thinking peacefully.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💧</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">Clear Understanding</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Real understanding is pure and clear, free from pride and stubbornness. When you learn with a
                  humble and open heart, your mind becomes like clear water, showing truth naturally.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🕊️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">Peaceful Learning</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Learning happens best in a state of inner peace and contentment. When we're not forcing or
                  struggling, knowledge flows naturally into our hearts and minds, bringing joy and understanding.
                </p>
              </div>
            </div>
          </div>

        {/* Divine Nature & Logic */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Amazing Facts About Divine Logic & Enlightenment
          </h2>
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-8 border border-purple-200 dark:border-purple-800">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌌</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">Divine Logic in Creation</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Every aspect of creation follows perfect divine logic. From the precise movement of planets to the
                  intricate design of a leaf, everything demonstrates flawless reasoning and purpose. Understanding
                  this divine logic opens your mind to the ultimate source of all wisdom and intelligence.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💎</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">Heart Confirmation of Truth</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  When you align your thinking with divine logic, your heart confirms the truth with deep peace and
                  certainty. This inner knowing transcends mere intellectual understanding, bringing a profound sense
                  of connection to the source of all knowledge and wisdom.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🧠</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">Intellectual Living Through Divine Wisdom</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Living intellectually means aligning your thoughts with divine wisdom and natural order. This
                  creates harmony between your mind, heart, and actions, leading to decisions that reflect divine
                  attributes of wisdom, mercy, and justice in your daily life.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚖️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">Perfect Alignment with Divine Will</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  True logic aligns your understanding with divine will and purpose. This alignment brings perfect
                  balance to your thoughts and actions, creating harmony between your personal desires and the
                  greater divine plan for your life and the world.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌱</span>
                  </div>
                </div>
                <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">Path to Inner Growth & Intelligence</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Each moment of logical thinking aligned with divine wisdom becomes a step toward inner growth and
                  spiritual intelligence. Your capacity for understanding expands infinitely when connected to the
                  divine source of all knowledge and wisdom.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">✨</span>
                  </div>
                </div>
                <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">Gateway to Enlightenment</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Divine logic is the gateway to enlightenment, revealing the perfect wisdom behind all existence.
                  Through logical contemplation of divine signs in creation, you achieve deep understanding of your
                  purpose and connection to the ultimate source of all truth and beauty.
                </p>
              </div>
            </div>
          </div>
        </div>

          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Natural Logical Principles
          </h2>
          <div className="space-y-8">
            {logicalPrinciples.map((principle, index) => {
              const Icon = principle.icon
              return (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden"
                >
                  <div className="p-8">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        <Icon className={`h-8 w-8 ${principle.color}`} />
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                            {principle.name}
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400">
                            {principle.description}
                          </p>
                        </div>
                      </div>
                      <Zap className="h-6 w-6 text-yellow-500" />
                    </div>

                    {/* Example */}
                    <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <h4 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2">
                        Practical Example
                      </h4>
                      <p className="text-blue-700 dark:text-blue-300">
                        {principle.example}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Applications */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Real-World Applications
                        </h4>
                        <p className="text-gray-700 dark:text-gray-300">
                          {principle.application}
                        </p>
                      </div>

                      {/* Benefits */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Key Benefits
                        </h4>
                        <div className="space-y-2">
                          {principle.benefits.map((benefit, benefitIndex) => (
                            <div key={benefitIndex} className="flex items-center">
                              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                              <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Intelligence Types */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Natural Forms of Intelligence
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {intelligenceTypes.map((intelligence, index) => (
              <div key={index} className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {intelligence.type}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {intelligence.description}
                </p>
                
                <div className="mb-4">
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-2">
                    Key Skills:
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {intelligence.skills.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded-full text-sm"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-2">
                    Development Methods:
                  </h4>
                  <ul className="space-y-1">
                    {intelligence.development.map((method, methodIndex) => (
                      <li key={methodIndex} className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                        <Lightbulb className="h-3 w-3 text-yellow-500 mr-2" />
                        {method}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Gentle Practices */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Gentle Practices for Natural Development
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {practicalExercises.map((exercise, index) => (
              <div key={index} className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <Puzzle className="h-6 w-6 text-green-600 mr-3" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {exercise.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {exercise.duration}
                    </p>
                  </div>
                </div>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  {exercise.description}
                </p>
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-2">
                    Benefits:
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {exercise.benefits.map((benefit, benefitIndex) => (
                      <span
                        key={benefitIndex}
                        className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded text-xs"
                      >
                        {benefit}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Integration Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Harmonious Intelligence Integration
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <Brain className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Logical Foundation
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Build strong logical reasoning skills as the foundation for all other types of intelligence.
                </p>
              </div>
              <div className="text-center">
                <Lightbulb className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Creative Application
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Apply logical principles creatively to generate innovative solutions and ideas.
                </p>
              </div>
              <div className="text-center">
                <Star className="h-12 w-12 text-red-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Practical Wisdom
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Combine logic and creativity with emotional intelligence for wise decision-making.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Nurture Your Natural Intelligence
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Intelligence grows naturally when nurtured with patience and care. Through gentle
              reflection, peaceful observation, and contentment in learning, your understanding
              expands like a flower blooming in its own time.
            </p>
            <div className="flex justify-center space-x-8">
              <div className="text-center">
                <Brain className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Logical Thinking</p>
              </div>
              <div className="text-center">
                <Puzzle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Problem Solving</p>
              </div>
              <div className="text-center">
                <Star className="h-8 w-8 text-red-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Integrated Wisdom</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
