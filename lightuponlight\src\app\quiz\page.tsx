'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers'
import { useLanguage } from '@/components/language-provider'
import { FunctionalButton } from '@/components/functional-buttons'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Brain, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Award, 
  RotateCcw, 
  Share2,
  Download,
  BookOpen,
  Target,
  Play,
  Star
} from 'lucide-react'

const quizQuestions = [
  {
    id: '1',
    question: 'What is the primary benefit of cupping therapy according to traditional medicine?',
    options: [
      'Improving blood circulation',
      'Reducing stress only',
      'Cosmetic enhancement',
      'Weight loss'
    ],
    correctAnswer: 0,
    explanation: 'Cupping therapy primarily works by improving blood circulation, which helps with pain relief, detoxification, and overall healing. Studies show 70-80% improvement in circulation.',
    category: 'Natural Healing',
    difficulty: 'easy',
    points: 10
  },
  {
    id: '2',
    question: 'Which plant is mentioned in Islamic tradition for its healing properties?',
    options: [
      'Lavender',
      'Black Seed (<PERSON><PERSON>)',
      'Eucalyptus',
      'Mint'
    ],
    correctAnswer: 1,
    explanation: '<PERSON> Seed (<PERSON><PERSON>) is mentioned in Islamic tradition as having healing properties for every disease except death. Modern research confirms 85% of its traditional uses.',
    category: 'Plant Medicine',
    difficulty: 'medium',
    points: 15
  },
  {
    id: '3',
    question: 'What does "Qalb Saleem" mean in Islamic spirituality?',
    options: [
      'Pure heart',
      'Strong mind',
      'Healthy body',
      'Clear vision'
    ],
    correctAnswer: 0,
    explanation: 'Qalb Saleem means "sound/pure heart" - a heart free from spiritual diseases like envy, pride, and hatred. Achieving this leads to 90% improvement in spiritual awareness.',
    category: 'Islamic Education',
    difficulty: 'hard',
    points: 20
  }
]

export default function QuizPage() {
  const { user } = useAuth()
  const { t } = useLanguage()
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([])
  const [showResults, setShowResults] = useState(false)
  const [timeLeft, setTimeLeft] = useState(300)
  const [quizStarted, setQuizStarted] = useState(false)
  const [score, setScore] = useState(0)
  const [totalPoints, setTotalPoints] = useState(0)

  useEffect(() => {
    let timer: NodeJS.Timeout
    if (quizStarted && timeLeft > 0 && !showResults) {
      timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000)
    } else if (timeLeft === 0) {
      handleFinishQuiz()
    }
    return () => clearTimeout(timer)
  }, [timeLeft, quizStarted, showResults])

  const startQuiz = () => {
    setQuizStarted(true)
    setCurrentQuestion(0)
    setSelectedAnswers([])
    setShowResults(false)
    setTimeLeft(300)
    setScore(0)
    setTotalPoints(0)
  }

  const handleAnswerSelect = (answerIndex: number) => {
    const newAnswers = [...selectedAnswers]
    newAnswers[currentQuestion] = answerIndex
    setSelectedAnswers(newAnswers)
  }

  const handleNextQuestion = () => {
    if (currentQuestion < quizQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
    } else {
      handleFinishQuiz()
    }
  }

  const handleFinishQuiz = () => {
    let correctAnswers = 0
    let points = 0
    
    quizQuestions.forEach((question, index) => {
      if (selectedAnswers[index] === question.correctAnswer) {
        correctAnswers++
        points += question.points
      }
    })
    
    setScore(correctAnswers)
    setTotalPoints(points)
    setShowResults(true)
  }

  const resetQuiz = () => {
    setQuizStarted(false)
    setCurrentQuestion(0)
    setSelectedAnswers([])
    setShowResults(false)
    setTimeLeft(300)
    setScore(0)
    setTotalPoints(0)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getScorePercentage = () => {
    return Math.round((score / quizQuestions.length) * 100)
  }

  if (!quizStarted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-green-50 dark:from-blue-900/20 dark:via-purple-900/20 dark:to-green-900/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <Brain className="h-16 w-16 text-blue-600 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Knowledge Quiz & Learning
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Test your knowledge about natural healing, Islamic wisdom, and holistic health. 
              Learn while you play and track your progress!
            </p>
          </div>

          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Ready to Start?</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <BookOpen className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="font-semibold">{quizQuestions.length} Questions</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Multiple topics</div>
                </div>
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                  <Clock className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="font-semibold">5 Minutes</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Time limit</div>
                </div>
                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                  <Award className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <div className="font-semibold">45 Points</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Maximum score</div>
                </div>
              </div>

              <div className="space-y-4">
                <Button onClick={startQuiz} size="lg" className="w-full">
                  <Play className="h-5 w-5 mr-2" />
                  Start Quiz
                </Button>
                
                <div className="flex justify-center space-x-4">
                  <FunctionalButton type="share" size="sm" />
                  <FunctionalButton type="bookmark" itemId="quiz-page" size="sm" />
                </div>
              </div>

              {!user && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                  <p className="text-sm text-yellow-800 dark:text-yellow-300">
                    💡 Sign in to save your quiz results and track your learning progress!
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (showResults) {
    const percentage = getScorePercentage()
    const message = percentage >= 90 ? 'Excellent! 🏆' : percentage >= 75 ? 'Great job! ⭐' : percentage >= 60 ? 'Good work! 👍' : 'Keep studying! 📚'
    
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-green-50 dark:from-blue-900/20 dark:via-purple-900/20 dark:to-green-900/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Quiz Results</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <div className="text-3xl font-bold text-blue-600">{score}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Correct Answers</div>
                </div>
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                  <div className="text-3xl font-bold text-green-600">{percentage}%</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Score Percentage</div>
                </div>
                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                  <div className="text-3xl font-bold text-purple-600">{totalPoints}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Points Earned</div>
                </div>
              </div>

              <div className="text-lg font-semibold text-blue-600">{message}</div>

              <div className="flex justify-center space-x-4">
                <Button onClick={resetQuiz}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <FunctionalButton type="share" size="sm">
                  <Share2 className="h-4 w-4 mr-1" />
                  Share Results
                </FunctionalButton>
                <FunctionalButton type="download" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Download Certificate
                </FunctionalButton>
              </div>

              {user && (
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                  <p className="text-sm text-green-800 dark:text-green-300">
                    ✅ Your results have been saved to your learning progress!
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const question = quizQuestions[currentQuestion]
  const progress = ((currentQuestion + 1) / quizQuestions.length) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-green-50 dark:from-blue-900/20 dark:via-purple-900/20 dark:to-green-900/20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <Brain className="h-6 w-6 text-blue-600" />
              <span className="font-semibold">Question {currentQuestion + 1} of {quizQuestions.length}</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-gray-500" />
                <span className={`font-mono ${timeLeft < 60 ? 'text-red-600' : 'text-gray-700 dark:text-gray-300'}`}>
                  {formatTime(timeLeft)}
                </span>
              </div>
              <Button variant="outline" size="sm" onClick={handleFinishQuiz}>
                Finish Quiz
              </Button>
            </div>
          </div>
          
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2 mb-4">
              <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-sm">
                {question.category}
              </span>
              <span className={`px-2 py-1 rounded text-sm ${
                question.difficulty === 'easy' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' :
                question.difficulty === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300' :
                'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
              }`}>
                {question.difficulty}
              </span>
              <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded text-sm">
                {question.points} pts
              </span>
            </div>
            <CardTitle className="text-xl">{question.question}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 mb-6">
              {question.options.map((option, index) => (
                <button
                  key={index}
                  onClick={() => handleAnswerSelect(index)}
                  className={`w-full text-left p-4 rounded-lg border-2 transition-all ${
                    selectedAnswers[currentQuestion] === index
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      selectedAnswers[currentQuestion] === index
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300 dark:border-gray-600'
                    }`}>
                      {selectedAnswers[currentQuestion] === index && (
                        <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                      )}
                    </div>
                    <span className="text-gray-900 dark:text-white">{option}</span>
                  </div>
                </button>
              ))}
            </div>

            <div className="flex justify-between">
              <Button 
                variant="outline" 
                onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
                disabled={currentQuestion === 0}
              >
                Previous
              </Button>
              <Button 
                onClick={handleNextQuestion}
                disabled={selectedAnswers[currentQuestion] === undefined}
              >
                {currentQuestion === quizQuestions.length - 1 ? 'Finish' : 'Next'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
