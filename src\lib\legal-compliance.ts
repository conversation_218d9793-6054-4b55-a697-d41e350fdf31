// Legal Compliance and Copyright Management
// Ensures all content meets legal requirements and copyright compliance

export interface ContentSource {
  type: 'public_domain' | 'creative_commons' | 'owned' | 'licensed' | 'user_generated'
  source: string
  license?: string
  attribution?: string
  verification_date: string
  notes?: string
}

export interface LegalCompliantAsset {
  id: string
  name: string
  url: string
  alt_text: string
  source: ContentSource
  usage_rights: string[]
  restrictions?: string[]
}

// Public Domain Image Sources (Verified Legal)
export const PUBLIC_DOMAIN_IMAGES: LegalCompliantAsset[] = [
  {
    id: 'default_avatar',
    name: 'Default User Avatar',
    url: '/images/avatars/default-user.svg',
    alt_text: 'Default user profile picture',
    source: {
      type: 'owned',
      source: 'Custom created SVG icon',
      verification_date: '2024-01-15',
      notes: 'Simple geometric design, no copyright issues'
    },
    usage_rights: ['commercial', 'modification', 'distribution'],
  },
  {
    id: 'islamic_pattern',
    name: 'Islamic Geometric Pattern',
    url: '/images/patterns/geometric-pattern.svg',
    alt_text: 'Traditional Islamic geometric pattern',
    source: {
      type: 'public_domain',
      source: 'Traditional Islamic art (pre-1900)',
      verification_date: '2024-01-15',
      notes: 'Historical patterns are public domain'
    },
    usage_rights: ['commercial', 'modification', 'distribution'],
  },
  {
    id: 'nature_healing',
    name: 'Natural Healing Elements',
    url: '/images/nature/healing-herbs.svg',
    alt_text: 'Natural healing herbs and plants',
    source: {
      type: 'creative_commons',
      source: 'Wikimedia Commons',
      license: 'CC0 1.0 Universal',
      verification_date: '2024-01-15'
    },
    usage_rights: ['commercial', 'modification', 'distribution'],
  }
]

// Verified Public Domain Text Sources
export const VERIFIED_TEXT_SOURCES = {
  quran: {
    arabic_text: {
      source: 'Original Quranic text (7th century)',
      copyright_status: 'Public domain',
      verification: 'Religious texts predating copyright law',
      usage_rights: 'Unlimited'
    },
    english_translation: {
      source: 'Multiple verified public domain translations',
      copyright_status: 'Public domain (pre-1928 translations)',
      verification: 'Pickthall, Yusuf Ali (original editions)',
      usage_rights: 'Commercial and non-commercial use'
    }
  },
  hadith: {
    source: 'Classical Islamic texts (pre-copyright era)',
    copyright_status: 'Public domain',
    verification: 'Historical religious texts',
    usage_rights: 'Unlimited'
  },
  medical_disclaimers: {
    source: 'Standard medical disclaimer templates',
    copyright_status: 'Public domain / Fair use',
    verification: 'Legal boilerplate text',
    usage_rights: 'Standard legal disclaimers'
  }
}

// Content Attribution Requirements
export const ATTRIBUTION_REQUIREMENTS = {
  creative_commons: {
    required_elements: ['title', 'author', 'source', 'license'],
    format: 'Title by Author, Source, License',
    placement: 'Near content or in credits section'
  },
  public_domain: {
    required_elements: ['source_verification'],
    format: 'Source: [verification]',
    placement: 'Optional but recommended'
  },
  user_generated: {
    required_elements: ['user_consent', 'terms_agreement'],
    format: 'User-generated content under site terms',
    placement: 'Terms of service reference'
  }
}

// Legal Compliance Checklist
export const COMPLIANCE_CHECKLIST = {
  content_audit: [
    'Verify all images are owned, licensed, or public domain',
    'Replace placeholder images with legal alternatives',
    'Document source and licensing for all assets',
    'Add proper attribution where required',
    'Review all text content for copyright issues'
  ],
  medical_content: [
    'Add medical disclaimers to health-related content',
    'Cite scientific sources where applicable',
    'Include "consult healthcare provider" warnings',
    'Avoid making specific medical claims',
    'Use general educational language'
  ],
  religious_content: [
    'Verify Quranic text accuracy',
    'Use established public domain translations',
    'Add source citations for hadith',
    'Include respectful usage disclaimers',
    'Maintain scholarly accuracy'
  ],
  user_content: [
    'Implement clear terms of service',
    'Obtain user consent for content usage',
    'Provide content reporting mechanisms',
    'Establish moderation guidelines',
    'Include DMCA takedown procedures'
  ]
}

// Safe Content Replacement Functions
export function getPublicDomainImage(category: string): LegalCompliantAsset | null {
  return PUBLIC_DOMAIN_IMAGES.find(img =>
    img.name.toLowerCase().includes(category.toLowerCase())
  ) || PUBLIC_DOMAIN_IMAGES[0] // Default to first safe image
}

export function generateAttribution(source: ContentSource): string {
  switch (source.type) {
    case 'creative_commons':
      return `${source.source} - ${source.license || 'Creative Commons'}`
    case 'public_domain':
      return `Source: ${source.source} (Public Domain)`
    case 'owned':
      return 'Original content by Light Upon Light'
    default:
      return source.source
  }
}

// Legal Disclaimer Templates
export const LEGAL_DISCLAIMERS = {
  medical: `
    MEDICAL DISCLAIMER: The information provided on this website is for educational
    purposes only and is not intended as medical advice. Always consult with a
    qualified healthcare provider before making any health-related decisions.
  `,
  religious: `
    RELIGIOUS CONTENT: All Quranic verses and Islamic content are provided for
    educational purposes. We strive for accuracy but recommend consulting
    qualified Islamic scholars for religious guidance.
  `,
  general: `
    EDUCATIONAL PURPOSE: All content is provided for educational and informational
    purposes only. Individual results may vary. This website does not provide
    professional advice and should not be used as a substitute for consultation
    with professional advisors.
  `
}