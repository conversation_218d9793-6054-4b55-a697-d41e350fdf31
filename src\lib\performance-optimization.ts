/**
 * Performance Optimization Utilities
 * Comprehensive performance enhancements for Light Upon Light website
 */

// Core Web Vitals Optimization
export interface CoreWebVitals {
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  fcp: number; // First Contentful Paint
  ttfb: number; // Time to First Byte
}

// Performance thresholds (Google recommendations)
export const PERFORMANCE_THRESHOLDS = {
  lcp: { good: 2500, needsImprovement: 4000 },
  fid: { good: 100, needsImprovement: 300 },
  cls: { good: 0.1, needsImprovement: 0.25 },
  fcp: { good: 1800, needsImprovement: 3000 },
  ttfb: { good: 800, needsImprovement: 1800 }
} as const;

// Image Optimization Configuration
export const IMAGE_OPTIMIZATION = {
  formats: ['webp', 'avif', 'jpeg', 'png'] as const,
  quality: {
    high: 90,
    medium: 75,
    low: 60
  },
  sizes: {
    thumbnail: { width: 320, height: 180 },
    card: { width: 400, height: 300 },
    hero: { width: 1200, height: 630 },
    avatar: { width: 48, height: 48 }
  },
  lazyLoading: {
    rootMargin: '50px',
    threshold: 0.1
  }
} as const;

// Font Loading Optimization
export const FONT_OPTIMIZATION = {
  preload: [
    { href: '/fonts/inter-var.woff2', as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' },
    { href: '/fonts/amiri-var.woff2', as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' }
  ],
  display: 'swap' as const,
  fallbacks: {
    inter: 'system-ui, -apple-system, sans-serif',
    amiri: 'serif'
  }
};

// Script Loading Optimization
export const SCRIPT_OPTIMIZATION = {
  defer: ['analytics', 'chat', 'social-widgets'],
  async: ['non-critical-features'],
  preload: ['critical-polyfills'],
  modulePreload: ['app-bundle', 'vendor-bundle']
};

// CSS Optimization
export const CSS_OPTIMIZATION = {
  critical: [
    'layout',
    'navigation',
    'hero-section',
    'above-fold-content'
  ],
  defer: [
    'animations',
    'modal-styles',
    'print-styles',
    'dark-mode-extras'
  ],
  purge: {
    enabled: true,
    safelist: ['dark', 'light', 'rtl', 'ltr']
  }
};

// Resource Hints
export const RESOURCE_HINTS = {
  preconnect: [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
    'https://eyffbsjpeyahhqbjfybe.supabase.co'
  ],
  dnsPrefetch: [
    'https://www.google-analytics.com',
    'https://cdn.jsdelivr.net'
  ],
  prefetch: [
    '/images/icons/common-icons.svg',
    '/api/settings'
  ]
};

// Lazy Loading Configuration
export const LAZY_LOADING = {
  images: {
    loading: 'lazy' as const,
    decoding: 'async' as const,
    rootMargin: '50px 0px',
    threshold: 0.01
  },
  components: {
    forum: () => import('@/app/forum/page'),
    community: () => import('@/app/community/page'),
    live: () => import('@/app/live/page')
  },
  routes: [
    '/forum',
    '/community',
    '/live',
    '/admin'
  ]
};

// Bundle Optimization
export const BUNDLE_OPTIMIZATION = {
  splitChunks: {
    vendor: ['react', 'react-dom', 'next'],
    ui: ['@/components/ui'],
    utils: ['@/lib'],
    pages: 'async'
  },
  compression: {
    gzip: true,
    brotli: true
  },
  minification: {
    terser: true,
    css: true,
    html: true
  }
};

// Performance Monitoring
export interface PerformanceMetrics {
  timestamp: number;
  url: string;
  metrics: CoreWebVitals;
  userAgent: string;
  connection?: string;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];

  // Measure Core Web Vitals
  measureCoreWebVitals(): Promise<CoreWebVitals> {
    return new Promise((resolve) => {
      const metrics: Partial<CoreWebVitals> = {};

      // LCP - Largest Contentful Paint
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        metrics.lcp = lastEntry.startTime;
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // FID - First Input Delay
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          metrics.fid = entry.processingStart - entry.startTime;
        });
      }).observe({ entryTypes: ['first-input'] });

      // CLS - Cumulative Layout Shift
      let clsValue = 0;
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        metrics.cls = clsValue;
      }).observe({ entryTypes: ['layout-shift'] });

      // FCP - First Contentful Paint
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            metrics.fcp = entry.startTime;
          }
        });
      }).observe({ entryTypes: ['paint'] });

      // TTFB - Time to First Byte
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
      }

      // Resolve after a short delay to collect all metrics
      setTimeout(() => {
        resolve(metrics as CoreWebVitals);
      }, 1000);
    });
  }

  // Record performance metrics
  recordMetrics(metrics: CoreWebVitals): void {
    const record: PerformanceMetrics = {
      timestamp: Date.now(),
      url: window.location.href,
      metrics,
      userAgent: navigator.userAgent,
      connection: (navigator as any).connection?.effectiveType
    };

    this.metrics.push(record);

    // Keep only last 100 records
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }
  }

  // Get performance report
  getPerformanceReport(): {
    current: PerformanceMetrics | null;
    average: CoreWebVitals;
    recommendations: string[];
  } {
    if (this.metrics.length === 0) {
      return {
        current: null,
        average: { lcp: 0, fid: 0, cls: 0, fcp: 0, ttfb: 0 },
        recommendations: ['No performance data available']
      };
    }

    const current = this.metrics[this.metrics.length - 1];
    const average = this.calculateAverageMetrics();
    const recommendations = this.generateRecommendations(average);

    return { current, average, recommendations };
  }

  private calculateAverageMetrics(): CoreWebVitals {
    const sum = this.metrics.reduce(
      (acc, record) => ({
        lcp: acc.lcp + record.metrics.lcp,
        fid: acc.fid + record.metrics.fid,
        cls: acc.cls + record.metrics.cls,
        fcp: acc.fcp + record.metrics.fcp,
        ttfb: acc.ttfb + record.metrics.ttfb
      }),
      { lcp: 0, fid: 0, cls: 0, fcp: 0, ttfb: 0 }
    );

    const count = this.metrics.length;
    return {
      lcp: sum.lcp / count,
      fid: sum.fid / count,
      cls: sum.cls / count,
      fcp: sum.fcp / count,
      ttfb: sum.ttfb / count
    };
  }

  private generateRecommendations(metrics: CoreWebVitals): string[] {
    const recommendations: string[] = [];

    if (metrics.lcp > PERFORMANCE_THRESHOLDS.lcp.needsImprovement) {
      recommendations.push('Optimize Largest Contentful Paint: Compress images, use WebP format, implement lazy loading');
    }

    if (metrics.fid > PERFORMANCE_THRESHOLDS.fid.needsImprovement) {
      recommendations.push('Reduce First Input Delay: Minimize JavaScript execution time, use code splitting');
    }

    if (metrics.cls > PERFORMANCE_THRESHOLDS.cls.needsImprovement) {
      recommendations.push('Improve Cumulative Layout Shift: Set image dimensions, avoid dynamic content insertion');
    }

    if (metrics.fcp > PERFORMANCE_THRESHOLDS.fcp.needsImprovement) {
      recommendations.push('Optimize First Contentful Paint: Minimize render-blocking resources, optimize CSS delivery');
    }

    if (metrics.ttfb > PERFORMANCE_THRESHOLDS.ttfb.needsImprovement) {
      recommendations.push('Reduce Time to First Byte: Optimize server response time, use CDN, enable caching');
    }

    return recommendations.length > 0 ? recommendations : ['Performance metrics are within acceptable ranges'];
  }
}

// Initialize performance monitor
export const performanceMonitor = new PerformanceMonitor();

// Utility functions
export const optimizeImage = (src: string, options: {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'avif' | 'jpeg' | 'png';
} = {}) => {
  const { width, height, quality = IMAGE_OPTIMIZATION.quality.medium, format = 'webp' } = options;
  
  // For Next.js Image component optimization
  return {
    src,
    width,
    height,
    quality,
    format,
    loading: 'lazy' as const,
    decoding: 'async' as const
  };
};

export const preloadCriticalResources = () => {
  if (typeof window === 'undefined') return;

  // Preload critical fonts
  FONT_OPTIMIZATION.preload.forEach(font => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = font.href;
    link.as = font.as;
    link.type = font.type;
    link.crossOrigin = font.crossOrigin;
    document.head.appendChild(link);
  });

  // Preconnect to external domains
  RESOURCE_HINTS.preconnect.forEach(url => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = url;
    document.head.appendChild(link);
  });
};
