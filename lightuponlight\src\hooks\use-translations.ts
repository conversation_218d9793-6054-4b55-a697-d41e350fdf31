'use client'

import { useLanguage } from '@/components/language-provider'

export function useTranslations() {
  const { t, language, setLanguage } = useLanguage()

  // Helper function to translate page content
  const translatePage = (pageKey: string, contentKey: string) => {
    return t(`${pageKey}.${contentKey}`)
  }

  // Helper function to translate common elements
  const translateCommon = (key: string) => {
    return t(`common.${key}`)
  }

  // Helper function to translate navigation
  const translateNav = (key: string) => {
    return t(`nav.${key}`)
  }

  // Helper function to translate forms
  const translateForm = (key: string) => {
    return t(`form.${key}`)
  }

  // Helper function to translate cookies
  const translateCookies = (key: string) => {
    return t(`cookies.${key}`)
  }

  // Helper function to translate specific sections
  const translateSection = (section: string, key: string) => {
    return t(`${section}.${key}`)
  }

  return {
    t,
    language,
    setLanguage,
    translatePage,
    translateCommon,
    translateNav,
    translateForm,
    translateCookies,
    translateSection
  }
}

// Specific page translation hooks
export function useCuppingTranslations() {
  const { translateSection } = useTranslations()
  
  return {
    title: () => translateSection('cupping', 'title'),
    subtitle: () => translateSection('cupping', 'subtitle'),
    benefits: () => translateSection('cupping', 'benefits'),
    circulation: () => translateSection('cupping', 'circulation'),
    detox: () => translateSection('cupping', 'detox'),
    stress: () => translateSection('cupping', 'stress'),
    immune: () => translateSection('cupping', 'immune'),
    factsTitle: () => translateSection('cupping', 'facts.title'),
    factsDivine: () => translateSection('cupping', 'facts.divine'),
    factsConfirmation: () => translateSection('cupping', 'facts.confirmation'),
    factsIntellectual: () => translateSection('cupping', 'facts.intellectual'),
    factsAlignment: () => translateSection('cupping', 'facts.alignment'),
    factsGrowth: () => translateSection('cupping', 'facts.growth'),
    factsEnlightenment: () => translateSection('cupping', 'facts.enlightenment')
  }
}

export function useHeartTranslations() {
  const { translateSection } = useTranslations()
  
  return {
    title: () => translateSection('heart', 'title'),
    subtitle: () => translateSection('heart', 'subtitle'),
    contentment: () => translateSection('heart', 'contentment'),
    consciousness: () => translateSection('heart', 'consciousness'),
    soundheart: () => translateSection('heart', 'soundheart'),
    resilience: () => translateSection('heart', 'resilience'),
    purpose: () => translateSection('heart', 'purpose'),
    wisdom: () => translateSection('heart', 'wisdom'),
    factsTitle: () => translateSection('heart', 'facts.title'),
    factsLearning: () => translateSection('heart', 'facts.learning'),
    factsAlignment: () => translateSection('heart', 'facts.alignment'),
    factsIntelligence: () => translateSection('heart', 'facts.intelligence'),
    factsBalance: () => translateSection('heart', 'facts.balance'),
    factsRenewal: () => translateSection('heart', 'facts.renewal'),
    factsPotential: () => translateSection('heart', 'facts.potential')
  }
}

export function usePatienceTranslations() {
  const { translateSection } = useTranslations()
  
  return {
    title: () => translateSection('patience', 'title'),
    subtitle: () => translateSection('patience', 'subtitle'),
    factsTitle: () => translateSection('patience', 'facts.title'),
    factsTiming: () => translateSection('patience', 'facts.timing'),
    factsConfirmation: () => translateSection('patience', 'facts.confirmation'),
    factsIntellectual: () => translateSection('patience', 'facts.intellectual'),
    factsAlignment: () => translateSection('patience', 'facts.alignment'),
    factsGrowth: () => translateSection('patience', 'facts.growth'),
    factsEnlightenment: () => translateSection('patience', 'facts.enlightenment')
  }
}

export function useLogicTranslations() {
  const { translateSection } = useTranslations()
  
  return {
    title: () => translateSection('logic', 'title'),
    subtitle: () => translateSection('logic', 'subtitle'),
    factsTitle: () => translateSection('logic', 'facts.title'),
    factsDivine: () => translateSection('logic', 'facts.divine'),
    factsConfirmation: () => translateSection('logic', 'facts.confirmation'),
    factsIntellectual: () => translateSection('logic', 'facts.intellectual'),
    factsAlignment: () => translateSection('logic', 'facts.alignment'),
    factsGrowth: () => translateSection('logic', 'facts.growth'),
    factsEnlightenment: () => translateSection('logic', 'facts.enlightenment')
  }
}
