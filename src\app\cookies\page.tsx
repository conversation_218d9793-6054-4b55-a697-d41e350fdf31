import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Globe } from "lucide-react";

const cookieTypes = [
  {
    name: "Essential Cookies",
    purpose: "Required for basic website functionality",
    examples: [
      "Authentication",
      "Security",
      "Language preferences",
      "Session management",
    ],

    retention: "Session or up to 1 year",
    canDisable: false,
    icon: Lock,
    color: "text-red-600",
  },
  {
    name: "Functional Cookies",
    purpose: "Enhance user experience and remember preferences",
    examples: [
      "Theme preferences",
      "Learning progress",
      "Community settings",
      "Accessibility options",
    ],

    retention: "Up to 2 years",
    canDisable: true,
    icon: Settings,
    color: "text-blue-600",
  },
  {
    name: "Analytics Cookies",
    purpose: "Help us understand how visitors use our website",
    examples: [
      "Page views",
      "User interactions",
      "Performance metrics",
      "Error tracking",
    ],

    retention: "Up to 2 years",
    canDisable: true,
    icon: Eye,
    color: "text-green-600",
  },
  {
    name: "Marketing Cookies",
    purpose: "Deliver relevant content and track campaign effectiveness",
    examples: [
      "Content personalization",
      "Social media integration",
      "Newsletter preferences",
    ],

    retention: "Up to 1 year",
    canDisable: true,
    icon: Globe,
    color: "text-purple-600",
  },
];

const cookieManagement = [
  {
    browser: "Chrome",
    instructions:
      "Settings > Privacy and security > Cookies and other site data",
  },
  {
    browser: "Firefox",
    instructions: "Settings > Privacy & Security > Cookies and Site Data",
  },
  {
    browser: "Safari",
    instructions: "Preferences > Privacy > Manage Website Data",
  },
  {
    browser: "Edge",
    instructions:
      "Settings > Cookies and site permissions > Cookies and site data",
  },
];

export default function CookiePolicyPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Cookie className="h-16 w-16 text-amber-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Cookie Policy
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Learn how Light Upon Light uses cookies to enhance your experience
              while respecting your privacy and providing transparency about
              data collection.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Introduction */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            What Are Cookies?
          </h2>
          <div className="prose prose-lg dark:prose-invert">
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              Cookies are small text files that are stored on your device when
              you visit our website. They help us provide you with a better
              experience by remembering your preferences, keeping you logged in,
              and helping us understand how you use our platform.
            </p>
            <p className="text-gray-700 dark:text-gray-300">
              We are committed to transparency about our cookie usage and give
              you control over your cookie preferences in compliance with GDPR
              and other privacy regulations.
            </p>
          </div>
        </div>

        {/* Cookie Types */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">
            Types of Cookies We Use
          </h2>
          <div className="space-y-6">
            {cookieTypes.map((cookie, index) => {
              const Icon = cookie.icon;
              return (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <Icon className={`h-6 w-6 ${cookie.color}`} />

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {cookie.name}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                          {cookie.purpose}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span
                        className={`px-2 py-1 rounded text-xs font-medium ${
                          cookie.canDisable
                            ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
                            : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
                        }`}
                      >
                        {cookie.canDisable ? "Optional" : "Required"}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                        Examples:
                      </h4>
                      <ul className="space-y-1">
                        {cookie.examples.map((example, exampleIndex) => (
                          <li
                            key={exampleIndex}
                            className="text-sm text-gray-600 dark:text-gray-300"
                          >
                            • {example}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                        Retention:
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {cookie.retention}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Cookie Management */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Managing Your Cookie Preferences
          </h2>

          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mb-6">
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-3">
              Cookie Consent Manager
            </h3>
            <p className="text-blue-700 dark:text-blue-300 mb-4">
              You can manage your cookie preferences at any time using our
              cookie consent manager. Click the button below to open your
              preferences.
            </p>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Manage Cookie Preferences
            </button>
          </div>

          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Browser Settings
          </h3>
          <p className="text-gray-700 dark:text-gray-300 mb-4">
            You can also manage cookies through your browser settings:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {cookieManagement.map((browser, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
              >
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  {browser.browser}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {browser.instructions}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Third-Party Cookies */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Third-Party Cookies
          </h2>
          <div className="prose prose-lg dark:prose-invert">
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              Some cookies on our website are set by third-party services that
              we use to enhance your experience:
            </p>
            <ul className="text-gray-700 dark:text-gray-300 space-y-2">
              <li>
                <strong>Supabase:</strong> Authentication and database services
              </li>
              <li>
                <strong>Analytics Services:</strong> Website performance and
                usage analytics
              </li>
              <li>
                <strong>Content Delivery Networks:</strong> Faster content
                delivery
              </li>
              <li>
                <strong>Social Media Platforms:</strong> Social sharing
                functionality
              </li>
            </ul>
            <p className="text-gray-700 dark:text-gray-300 mt-4">
              These third parties have their own privacy policies and cookie
              practices. We encourage you to review their policies for more
              information.
            </p>
          </div>
        </div>

        {/* Updates */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Updates to This Policy
          </h2>
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              We may update this Cookie Policy from time to time to reflect
              changes in our practices or for other operational, legal, or
              regulatory reasons.
            </p>
            <p className="text-gray-700 dark:text-gray-300">
              <strong>Last updated:</strong> {new Date().toLocaleDateString()}
            </p>
          </div>
        </div>

        {/* Contact */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Questions About Cookies?
          </h2>
          <p className="text-gray-700 dark:text-gray-300 mb-4">
            If you have any questions about our use of cookies or this Cookie
            Policy, please contact us:
          </p>
          <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
            <Shield className="h-5 w-5" />
            <span>Email: <EMAIL></span>
          </div>
        </div>
      </div>
    </div>
  );
}
