/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['eyffbsjpeyahhqbjfybe.supabase.co'],
  },
  webpack: (config, { isServer }) => {
    // Suppress Supabase realtime-js dependency warning
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      }
    }

    // Ignore specific warnings
    config.ignoreWarnings = [
      {
        module: /node_modules\/@supabase\/realtime-js/,
        message: /Critical dependency: the request of a dependency is an expression/,
      },
    ]

    return config
  },
}

module.exports = nextConfig
