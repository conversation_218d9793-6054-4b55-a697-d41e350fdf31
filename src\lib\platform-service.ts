// Professional Platform Service
// High-quality unified system for live streaming, forum, and community learning with comprehensive admin/moderator controls

import { supabase } from './supabase'

// ===== ROLE DEFINITIONS =====
export type UserRole = 'admin' | 'moderator' | 'guest_admin' | 'user'

export interface UserPermissions {
  // Stream permissions
  canStartStream: boolean
  canEndStream: boolean
  canModerateChat: boolean
  canTimeoutUsers: boolean
  canBanUsers: boolean
  canDeleteMessages: boolean
  canPinMessages: boolean
  canAssignModerators: boolean
  canMarkAsQuestion: boolean

  // Forum permissions
  canModerateForum: boolean
  canDeletePosts: boolean
  canPinPosts: boolean
  canLockThreads: boolean
  canFeaturePosts: boolean

  // Admin permissions
  canManageUsers: boolean
  canAccessSettings: boolean
  canViewAnalytics: boolean
  canDownloadStreams: boolean
}

// ===== CORE INTERFACES =====
export interface LiveStream {
  id: string
  title: string
  description?: string
  streamer_id: string
  guest_admin_id?: string
  is_live: boolean
  viewer_count: number
  chat_mode: 'open' | 'members_only' | 'approval_required' | 'disabled'
  access_type: 'public' | 'members_only' | 'paid'
  started_at?: string
  ended_at?: string
  recording_url?: string
}

export interface ChatMessage {
  id: string
  stream_id: string
  user_id: string
  message: string
  message_type: 'chat' | 'question' | 'announcement' | 'system'
  is_deleted: boolean
  is_pinned: boolean
  is_question: boolean
  created_at: string
  user?: {
    id: string
    full_name: string
    email: string
    role: UserRole
  }
}

export interface ForumPost {
  id: string
  title: string
  content: string
  author_id: string
  category_id: string
  is_pinned: boolean
  is_locked: boolean
  is_featured: boolean
  reply_count: number
  views: number
  likes: number
  created_at: string
  author?: {
    id: string
    full_name: string
    email: string
    role: UserRole
  }
}

export class PlatformService {
  
  // ===== PERMISSION SYSTEM =====
  
  static getPermissions(userRole: UserRole): UserPermissions {
    const basePermissions: UserPermissions = {
      canStartStream: false,
      canEndStream: false,
      canModerateChat: false,
      canTimeoutUsers: false,
      canBanUsers: false,
      canDeleteMessages: false,
      canPinMessages: false,
      canAssignModerators: false,
      canMarkAsQuestion: false,
      canModerateForum: false,
      canDeletePosts: false,
      canPinPosts: false,
      canLockThreads: false,
      canFeaturePosts: false,
      canManageUsers: false,
      canAccessSettings: false,
      canViewAnalytics: false,
      canDownloadStreams: false
    }

    switch (userRole) {
      case 'admin':
        return {
          ...basePermissions,
          canStartStream: true,
          canEndStream: true,
          canModerateChat: true,
          canTimeoutUsers: true,
          canBanUsers: true,
          canDeleteMessages: true,
          canPinMessages: true,
          canAssignModerators: true,
          canMarkAsQuestion: true,
          canModerateForum: true,
          canDeletePosts: true,
          canPinPosts: true,
          canLockThreads: true,
          canFeaturePosts: true,
          canManageUsers: true,
          canAccessSettings: true,
          canViewAnalytics: true,
          canDownloadStreams: true
        }
      
      case 'moderator':
        return {
          ...basePermissions,
          canModerateChat: true,
          canTimeoutUsers: true,
          canDeleteMessages: true,
          canPinMessages: true,
          canMarkAsQuestion: true,
          canModerateForum: true,
          canDeletePosts: true,
          canPinPosts: true,
          canLockThreads: true
        }
      
      case 'guest_admin':
        return {
          ...basePermissions,
          canStartStream: true,
          canEndStream: true,
          canModerateChat: true,
          canTimeoutUsers: true,
          canDeleteMessages: true,
          canPinMessages: true,
          canMarkAsQuestion: true
        }
      
      default:
        return basePermissions
    }
  }

  static async getUserRole(email: string): Promise<UserRole> {
    // <EMAIL> has full admin access by default
    if (email === '<EMAIL>') {
      return 'admin'
    }

    // Check database for user role
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('email', email)
      .single()

    return user?.role || 'user'
  }

  // Check if user has both admin and guest admin access
  static hasMultipleRoles(email: string): boolean {
    return email === '<EMAIL>'
  }

  // Get permissions for specific context (full admin vs guest admin mode)
  static getContextPermissions(email: string, context: 'full_admin' | 'guest_admin'): UserPermissions {
    if (email !== '<EMAIL>') {
      // Regular users get their normal role permissions
      const role = 'user' // Will be determined by database
      return this.getPermissions(role)
    }

    // <EMAIL> permissions based on context
    if (context === 'full_admin') {
      return this.getPermissions('admin')
    } else {
      // guest_admin context - live stream moderation only
      return {
        ...this.getPermissions('user'),
        canStartStream: true,
        canEndStream: true,
        canModerateChat: true,
        canTimeoutUsers: true,
        canDeleteMessages: true,
        canPinMessages: true,
        canMarkAsQuestion: true
      }
    }
  }

  // Assign guest admin role to a user
  static async assignGuestAdmin(email: string): Promise<void> {
    const { error } = await supabase
      .from('users')
      .update({ role: 'guest_admin' })
      .eq('email', email)

    if (error) throw error
  }

  // Remove guest admin role (revert to user)
  static async removeGuestAdmin(email: string): Promise<void> {
    const { error } = await supabase
      .from('users')
      .update({ role: 'user' })
      .eq('email', email)

    if (error) throw error
  }

  // ===== LIVE STREAMING =====
  
  static async createStream(data: {
    title: string
    description?: string
    streamer_id: string
    guest_admin_email?: string
  }): Promise<LiveStream> {
    let guest_admin_id = null
    
    // If guest admin email provided, get their user ID
    if (data.guest_admin_email) {
      const { data: guestAdmin } = await supabase
        .from('users')
        .select('id')
        .eq('email', data.guest_admin_email)
        .single()
      
      guest_admin_id = guestAdmin?.id
    }

    const { data: stream, error } = await supabase
      .from('live_streams')
      .insert([{
        title: data.title,
        description: data.description,
        streamer_id: data.streamer_id,
        guest_admin_id,
        chat_mode: 'open',
        access_type: 'public',
        is_live: false,
        viewer_count: 0
      }])
      .select()
      .single()

    if (error) throw error
    return stream
  }

  static async startStream(streamId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('live_streams')
      .update({
        is_live: true,
        started_at: new Date().toISOString()
      })
      .eq('id', streamId)
      .eq('streamer_id', userId)

    if (error) throw error

    // Add system message
    await this.addChatMessage({
      stream_id: streamId,
      user_id: userId,
      message: '🔴 Live stream started! Welcome everyone!',
      message_type: 'system'
    })
  }

  static async endStream(streamId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('live_streams')
      .update({
        is_live: false,
        ended_at: new Date().toISOString()
      })
      .eq('id', streamId)
      .eq('streamer_id', userId)

    if (error) throw error
  }

  static async getActiveStream(): Promise<LiveStream | null> {
    const { data, error } = await supabase
      .from('live_streams')
      .select('*')
      .eq('is_live', true)
      .order('started_at', { ascending: false })
      .limit(1)
      .single()

    if (error) return null
    return data
  }

  // ===== CHAT SYSTEM =====
  
  static async addChatMessage(data: {
    stream_id: string
    user_id: string
    message: string
    message_type?: 'chat' | 'question' | 'announcement' | 'system'
  }): Promise<ChatMessage> {
    const { data: message, error } = await supabase
      .from('chat_messages')
      .insert([{
        ...data,
        message_type: data.message_type || 'chat',
        is_deleted: false,
        is_pinned: false,
        is_question: data.message_type === 'question'
      }])
      .select(`
        *,
        user:users(id, full_name, email, role)
      `)
      .single()

    if (error) throw error
    return message
  }

  static async getChatMessages(streamId: string): Promise<ChatMessage[]> {
    const { data, error } = await supabase
      .from('chat_messages')
      .select(`
        *,
        user:users(id, full_name, email, role)
      `)
      .eq('stream_id', streamId)
      .eq('is_deleted', false)
      .order('created_at', { ascending: true })
      .limit(100)

    if (error) throw error
    return data || []
  }

  static async deleteMessage(messageId: string, moderatorId: string): Promise<void> {
    const { error } = await supabase
      .from('chat_messages')
      .update({
        is_deleted: true,
        deleted_by: moderatorId,
        deleted_at: new Date().toISOString()
      })
      .eq('id', messageId)

    if (error) throw error
  }

  static async pinMessage(messageId: string, pinned: boolean): Promise<void> {
    const { error } = await supabase
      .from('chat_messages')
      .update({ is_pinned: pinned })
      .eq('id', messageId)

    if (error) throw error
  }

  static async markAsQuestion(messageId: string): Promise<void> {
    const { error } = await supabase
      .from('chat_messages')
      .update({ 
        is_question: true,
        message_type: 'question'
      })
      .eq('id', messageId)

    if (error) throw error
  }

  // ===== USER MODERATION =====
  
  static async timeoutUser(streamId: string, userId: string, duration: number, reason?: string): Promise<void> {
    const expiresAt = new Date(Date.now() + duration * 1000).toISOString()

    const { error } = await supabase
      .from('user_timeouts')
      .insert([{
        user_id: userId,
        stream_id: streamId,
        timeout_duration: duration,
        reason,
        expires_at: expiresAt,
        is_active: true
      }])

    if (error) throw error
  }

  static async banUser(streamId: string, userId: string, _reason?: string): Promise<void> {
    // Get current banned users and add new user
    const { data: stream } = await supabase
      .from('live_streams')
      .select('banned_users')
      .eq('id', streamId)
      .single()

    const currentBanned = stream?.banned_users || []
    const updatedBanned = [...currentBanned, userId]

    const { error } = await supabase
      .from('live_streams')
      .update({
        banned_users: updatedBanned
      })
      .eq('id', streamId)

    if (error) throw error
  }

  static async isUserTimedOut(streamId: string, userId: string): Promise<boolean> {
    const { data } = await supabase
      .from('user_timeouts')
      .select('*')
      .eq('stream_id', streamId)
      .eq('user_id', userId)
      .eq('is_active', true)
      .gt('expires_at', new Date().toISOString())
      .single()

    return !!data
  }
}
