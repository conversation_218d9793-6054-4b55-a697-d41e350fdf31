'use client'

import { supabase } from './supabase'

// Security service for authentication and authorization
export class SecurityService {
  // Admin email whitelist - stored securely
  private static readonly ADMIN_EMAILS = [
    '<EMAIL>',
    '<EMAIL>'
  ]

  // Validate UUID format
  static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    return uuidRegex.test(uuid)
  }

  // Validate email format
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Sanitize input to prevent XSS
  static sanitizeInput(input: string): string {
    if (typeof input !== 'string') return ''
    
    return input
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim()
      .slice(0, 1000) // Limit length
  }

  // Sanitize HTML content
  static sanitizeHTML(html: string): string {
    if (typeof html !== 'string') return ''
    
    // Basic HTML sanitization - remove dangerous elements and attributes
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
      .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/javascript:/gi, '')
      .trim()
  }

  // Verify admin status with database check
  static async verifyAdmin(userId: string): Promise<boolean> {
    try {
      if (!userId || !this.isValidUUID(userId)) {
        return false
      }

      const { data: user, error } = await supabase
        .from('users')
        .select('role, email, is_active')
        .eq('id', userId)
        .single()

      if (error || !user) {
        return false
      }

      // Check if user is active, has admin role, and email is in whitelist
      return user.is_active === true && 
             user.role === 'admin' && 
             this.ADMIN_EMAILS.includes(user.email)
    } catch (error) {
      console.error('Error verifying admin status:', error)
      return false
    }
  }

  // Verify moderator status
  static async verifyModerator(userId: string): Promise<boolean> {
    try {
      if (!userId || !this.isValidUUID(userId)) {
        return false
      }

      const { data: user, error } = await supabase
        .from('users')
        .select('role, is_active')
        .eq('id', userId)
        .single()

      if (error || !user) {
        return false
      }

      return user.is_active === true && 
             (user.role === 'admin' || user.role === 'moderator')
    } catch (error) {
      console.error('Error verifying moderator status:', error)
      return false
    }
  }

  // Rate limiting check (simple in-memory implementation)
  private static rateLimitMap = new Map<string, { count: number; resetTime: number }>()

  static checkRateLimit(identifier: string, maxRequests = 10, windowMs = 60000): boolean {
    const now = Date.now()
    const key = identifier
    const record = this.rateLimitMap.get(key)

    if (!record || now > record.resetTime) {
      this.rateLimitMap.set(key, { count: 1, resetTime: now + windowMs })
      return true
    }

    if (record.count >= maxRequests) {
      return false
    }

    record.count++
    return true
  }

  // Validate message content
  static validateMessageContent(message: string): { isValid: boolean; error?: string } {
    if (!message || typeof message !== 'string') {
      return { isValid: false, error: 'Message is required' }
    }

    const sanitized = this.sanitizeInput(message)
    
    if (sanitized.length === 0) {
      return { isValid: false, error: 'Message cannot be empty' }
    }

    if (sanitized.length > 1000) {
      return { isValid: false, error: 'Message is too long (max 1000 characters)' }
    }

    // Check for spam patterns
    const spamPatterns = [
      /(.)\1{10,}/, // Repeated characters
      /https?:\/\/[^\s]+/gi, // URLs (could be spam)
      /\b(buy|sell|cheap|free|click|visit)\b/gi // Common spam words
    ]

    for (const pattern of spamPatterns) {
      if (pattern.test(sanitized)) {
        return { isValid: false, error: 'Message contains inappropriate content' }
      }
    }

    return { isValid: true }
  }

  // Validate form data
  static validateContactForm(data: {
    name: string
    email: string
    subject: string
    message: string
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // Validate name
    if (!data.name || typeof data.name !== 'string') {
      errors.push('Name is required')
    } else if (data.name.trim().length < 2) {
      errors.push('Name must be at least 2 characters')
    } else if (data.name.trim().length > 100) {
      errors.push('Name is too long (max 100 characters)')
    }

    // Validate email
    if (!data.email || typeof data.email !== 'string') {
      errors.push('Email is required')
    } else if (!this.isValidEmail(data.email)) {
      errors.push('Invalid email format')
    }

    // Validate subject
    if (!data.subject || typeof data.subject !== 'string') {
      errors.push('Subject is required')
    } else if (data.subject.trim().length < 5) {
      errors.push('Subject must be at least 5 characters')
    } else if (data.subject.trim().length > 200) {
      errors.push('Subject is too long (max 200 characters)')
    }

    // Validate message
    const messageValidation = this.validateMessageContent(data.message)
    if (!messageValidation.isValid) {
      errors.push(messageValidation.error || 'Invalid message')
    }

    return { isValid: errors.length === 0, errors }
  }

  // Log security events
  static logSecurityEvent(event: string, details: any) {
    console.warn(`[SECURITY] ${event}:`, {
      timestamp: new Date().toISOString(),
      ...details
    })
  }

  // Check if user can perform action
  static async canPerformAction(
    userId: string, 
    action: 'delete_message' | 'delete_stream' | 'moderate_user' | 'start_stream'
  ): Promise<boolean> {
    try {
      switch (action) {
        case 'delete_message':
        case 'delete_stream':
        case 'start_stream':
          return await this.verifyAdmin(userId)
        
        case 'moderate_user':
          return await this.verifyModerator(userId)
        
        default:
          return false
      }
    } catch (error) {
      this.logSecurityEvent('Permission check failed', { userId, action, error })
      return false
    }
  }

  // Get user permissions
  static async getUserPermissions(userId: string): Promise<{
    isAdmin: boolean
    isModerator: boolean
    canModerate: boolean
    canStream: boolean
  }> {
    const isAdmin = await this.verifyAdmin(userId)
    const isModerator = await this.verifyModerator(userId)

    return {
      isAdmin,
      isModerator,
      canModerate: isAdmin || isModerator,
      canStream: isAdmin
    }
  }
}
