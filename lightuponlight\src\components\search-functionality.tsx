'use client'

import { useState, useEffect } from 'react'
import { useLanguage } from './language-provider'
import { useData } from './data-provider'
import { Button } from './ui/button'
import { Search, X, Filter, SortAsc, SortDesc, Calendar, User, Tag } from 'lucide-react'

interface SearchResult {
  id: string
  type: 'post' | 'stream' | 'event' | 'achievement'
  title: string
  content: string
  author?: string
  category?: string
  date?: Date
  relevance: number
}

interface SearchFilters {
  type: string[]
  category: string[]
  dateRange: 'all' | 'week' | 'month' | 'year'
  sortBy: 'relevance' | 'date' | 'title'
  sortOrder: 'asc' | 'desc'
}

export function SearchFunctionality() {
  const { t } = useLanguage()
  const { forumPosts, streamArchive } = useData()
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<SearchFilters>({
    type: [],
    category: [],
    dateRange: 'all',
    sortBy: 'relevance',
    sortOrder: 'desc'
  })

  const searchData = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    setIsSearching(true)
    
    // Simulate search delay
    await new Promise(resolve => setTimeout(resolve, 300))

    const searchResults: SearchResult[] = []

    // Search forum posts
    forumPosts.forEach(post => {
      const titleMatch = post.title.toLowerCase().includes(searchQuery.toLowerCase())
      const contentMatch = post.content.toLowerCase().includes(searchQuery.toLowerCase())
      const authorMatch = post.author.toLowerCase().includes(searchQuery.toLowerCase())
      
      if (titleMatch || contentMatch || authorMatch) {
        let relevance = 0
        if (titleMatch) relevance += 3
        if (contentMatch) relevance += 2
        if (authorMatch) relevance += 1

        searchResults.push({
          id: post.id,
          type: 'post',
          title: post.title,
          content: post.content,
          author: post.author,
          category: post.category,
          date: post.createdAt,
          relevance
        })
      }
    })

    // Search stream archive
    streamArchive.forEach(stream => {
      const titleMatch = stream.title.toLowerCase().includes(searchQuery.toLowerCase())
      const descMatch = stream.description.toLowerCase().includes(searchQuery.toLowerCase())
      
      if (titleMatch || descMatch) {
        let relevance = 0
        if (titleMatch) relevance += 3
        if (descMatch) relevance += 2

        searchResults.push({
          id: stream.id,
          type: 'stream',
          title: stream.title,
          content: stream.description,
          date: stream.date,
          relevance
        })
      }
    })

    // Apply filters
    let filteredResults = searchResults

    if (filters.type.length > 0) {
      filteredResults = filteredResults.filter(result => filters.type.includes(result.type))
    }

    if (filters.category.length > 0) {
      filteredResults = filteredResults.filter(result => 
        result.category && filters.category.includes(result.category)
      )
    }

    // Apply date filter
    if (filters.dateRange !== 'all') {
      const now = new Date()
      const cutoffDate = new Date()
      
      switch (filters.dateRange) {
        case 'week':
          cutoffDate.setDate(now.getDate() - 7)
          break
        case 'month':
          cutoffDate.setMonth(now.getMonth() - 1)
          break
        case 'year':
          cutoffDate.setFullYear(now.getFullYear() - 1)
          break
      }
      
      filteredResults = filteredResults.filter(result => 
        result.date && result.date >= cutoffDate
      )
    }

    // Sort results
    filteredResults.sort((a, b) => {
      let comparison = 0
      
      switch (filters.sortBy) {
        case 'relevance':
          comparison = b.relevance - a.relevance
          break
        case 'date':
          comparison = (b.date?.getTime() || 0) - (a.date?.getTime() || 0)
          break
        case 'title':
          comparison = a.title.localeCompare(b.title)
          break
      }
      
      return filters.sortOrder === 'asc' ? -comparison : comparison
    })

    setResults(filteredResults)
    setIsSearching(false)
  }

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      searchData(query)
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [query, filters])

  const clearSearch = () => {
    setQuery('')
    setResults([])
  }

  const toggleFilter = (filterType: keyof SearchFilters, value: string) => {
    setFilters(prev => {
      const currentArray = prev[filterType] as string[]
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value]
      
      return { ...prev, [filterType]: newArray }
    })
  }

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'post': return '📝'
      case 'stream': return '🎥'
      case 'event': return '📅'
      case 'achievement': return '🏆'
      default: return '📄'
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Search Input */}
      <div className="relative mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder={t('common.search') || 'Search posts, streams, events...'}
            className="w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
          {query && (
            <button
              onClick={clearSearch}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>
        
        {/* Filter Toggle */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className="mt-2"
        >
          <Filter className="h-4 w-4 mr-2" />
          {t('common.filter') || 'Filters'}
        </Button>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 mb-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Content Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Content Type
              </label>
              <div className="space-y-2">
                {['post', 'stream', 'event'].map(type => (
                  <label key={type} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.type.includes(type)}
                      onChange={() => toggleFilter('type', type)}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-300 capitalize">
                      {type}s
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Category
              </label>
              <div className="space-y-2">
                {['natural-healing', 'islamic-education', 'plant-medicine'].map(category => (
                  <label key={category} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.category.includes(category)}
                      onChange={() => toggleFilter('category', category)}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-300 capitalize">
                      {category.replace('-', ' ')}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Date Range
              </label>
              <select
                value={filters.dateRange}
                onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value as any }))}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white"
              >
                <option value="all">All Time</option>
                <option value="week">Past Week</option>
                <option value="month">Past Month</option>
                <option value="year">Past Year</option>
              </select>
            </div>

            {/* Sort */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Sort By
              </label>
              <div className="flex space-x-2">
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}
                  className="flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white"
                >
                  <option value="relevance">Relevance</option>
                  <option value="date">Date</option>
                  <option value="title">Title</option>
                </select>
                <button
                  onClick={() => setFilters(prev => ({ 
                    ...prev, 
                    sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc' 
                  }))}
                  className="p-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-600"
                >
                  {filters.sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search Results */}
      {query && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {isSearching ? 'Searching...' : `${results.length} results for "${query}"`}
            </h3>
          </div>
          
          <div className="max-h-96 overflow-y-auto">
            {results.length === 0 && !isSearching ? (
              <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                No results found. Try adjusting your search terms or filters.
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {results.map((result) => (
                  <div key={`${result.type}-${result.id}`} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <div className="flex items-start space-x-3">
                      <span className="text-2xl">{getResultIcon(result.type)}</span>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-lg font-medium text-gray-900 dark:text-white truncate">
                          {result.title}
                        </h4>
                        <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-2 mt-1">
                          {result.content}
                        </p>
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                          {result.author && (
                            <div className="flex items-center">
                              <User className="h-3 w-3 mr-1" />
                              {result.author}
                            </div>
                          )}
                          {result.category && (
                            <div className="flex items-center">
                              <Tag className="h-3 w-3 mr-1" />
                              {result.category}
                            </div>
                          )}
                          {result.date && (
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {result.date.toLocaleDateString()}
                            </div>
                          )}
                          <span className="capitalize">{result.type}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
