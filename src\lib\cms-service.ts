/**
 * Comprehensive CMS Service
 * Handles all content management operations with caching, loading optimization, and multi-language support
 */

import { supabase } from './supabase';
import { cache } from 'react';

// ===== INTERFACES =====

export interface CMSContent {
  id: string;
  content_type_id: string;
  slug: string;
  title: string;
  excerpt?: string;
  content: any; // JSONB content
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  featured_image_id?: string;
  featured_image?: CMSMedia;
  author_id: string;
  author?: any;
  status: 'draft' | 'published' | 'archived' | 'scheduled';
  published_at?: string;
  scheduled_at?: string;
  language: string;
  parent_id?: string;
  sort_order: number;
  view_count: number;
  is_featured: boolean;
  is_sticky: boolean;
  created_at: string;
  updated_at: string;
  categories?: CMSCategory[];
  tags?: CMSTag[];
  versions?: CMSContentVersion[];
}

export interface CMSContentVersion {
  id: string;
  content_id: string;
  version_number: number;
  title: string;
  content: any;
  meta_title?: string;
  meta_description?: string;
  author_id: string;
  created_at: string;
  change_summary?: string;
}

export interface CMSMedia {
  id: string;
  filename: string;
  original_filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  file_type: 'image' | 'video' | 'audio' | 'document';
  width?: number;
  height?: number;
  duration?: number;
  alt_text?: string;
  caption?: string;
  description?: string;
  uploaded_by: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export interface CMSCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parent_id?: string;
  color?: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
  language: string;
  created_at: string;
  updated_at: string;
  children?: CMSCategory[];
  content_count?: number;
}

export interface CMSTag {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  usage_count: number;
  language: string;
  created_at: string;
  updated_at: string;
}

export interface CMSContentType {
  id: string;
  name: string;
  description?: string;
  schema: any;
  template?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CMSSettings {
  [key: string]: any;
}

export interface ContentListOptions {
  content_type?: string;
  status?: string;
  language?: string;
  category?: string;
  tag?: string;
  author?: string;
  featured?: boolean;
  limit?: number;
  offset?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
}

// ===== CACHE CONFIGURATION =====
const CACHE_DURATION = 3600; // 1 hour in seconds
const contentCache = new Map<string, { data: any; timestamp: number }>();

function getCacheKey(operation: string, params: any = {}): string {
  return `cms_${operation}_${JSON.stringify(params)}`;
}

function isCacheValid(timestamp: number): boolean {
  return Date.now() - timestamp < CACHE_DURATION * 1000;
}

function getFromCache<T>(key: string): T | null {
  const cached = contentCache.get(key);
  if (cached && isCacheValid(cached.timestamp)) {
    return cached.data as T;
  }
  return null;
}

function setCache<T>(key: string, data: T): void {
  contentCache.set(key, { data, timestamp: Date.now() });
}

function clearCache(pattern?: string): void {
  if (pattern) {
    const keys = Array.from(contentCache.keys());
    for (const key of keys) {
      if (key.includes(pattern)) {
        contentCache.delete(key);
      }
    }
  } else {
    contentCache.clear();
  }
}

// ===== CORE CMS SERVICE =====

export class CMSService {
  
  // ===== CONTENT OPERATIONS =====

  /**
   * Get content by slug with caching and loading optimization
   */
  static async getContentBySlug(
    slug: string, 
    language: string = 'en',
    includeRelations: boolean = true
  ): Promise<CMSContent | null> {
    const cacheKey = getCacheKey('content_by_slug', { slug, language, includeRelations });
    const cached = getFromCache<CMSContent>(cacheKey);
    if (cached) return cached;

    try {
      let query = supabase
        .from('cms_content')
        .select(`
          *,
          ${includeRelations ? `
            featured_image:cms_media!featured_image_id(*),
            author:users!author_id(id, full_name, email),
            categories:cms_content_categories(
              category:cms_categories(*)
            ),
            tags:cms_content_tags(
              tag:cms_tags(*)
            )
          ` : ''}
        `)
        .eq('slug', slug)
        .eq('language', language)
        .eq('status', 'published')
        .single();

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching content by slug:', error);
        return null;
      }

      // Transform relations
      if (includeRelations && data && typeof data === 'object') {
        const contentData = data as any;
        contentData.categories = contentData.categories?.map((c: any) => c.category) || [];
        contentData.tags = contentData.tags?.map((t: any) => t.tag) || [];
      }

      // Increment view count asynchronously
      if (data && typeof data === 'object' && 'id' in data) {
        CMSService.incrementViewCount((data as any).id);
      }

      const contentData = data as unknown as CMSContent | null;
      setCache(cacheKey, contentData);
      return contentData;
    } catch (error) {
      console.error('Error in getContentBySlug:', error);
      return null;
    }
  }

  /**
   * Get content list with filtering, pagination, and caching
   */
  static async getContentList(options: ContentListOptions = {}): Promise<{
    content: CMSContent[];
    total: number;
    hasMore: boolean;
  }> {
    const cacheKey = getCacheKey('content_list', options);
    const cached = getFromCache<any>(cacheKey);
    if (cached) return cached;

    try {
      const {
        content_type,
        status = 'published',
        language = 'en',
        category,
        tag,
        author,
        featured,
        limit = 20,
        offset = 0,
        sort_by = 'published_at',
        sort_order = 'desc',
        search
      } = options;

      let query = supabase
        .from('cms_content')
        .select(`
          *,
          featured_image:cms_media!featured_image_id(*),
          author:users!author_id(id, full_name, email),
          categories:cms_content_categories(
            category:cms_categories(*)
          ),
          tags:cms_content_tags(
            tag:cms_tags(*)
          )
        `, { count: 'exact' })
        .eq('status', status)
        .eq('language', language);

      // Apply filters
      if (content_type) query = query.eq('content_type_id', content_type);
      if (author) query = query.eq('author_id', author);
      if (featured !== undefined) query = query.eq('is_featured', featured);
      if (search) {
        query = query.or(`title.ilike.%${search}%,excerpt.ilike.%${search}%`);
      }

      // Apply sorting
      query = query.order(sort_by, { ascending: sort_order === 'asc' });

      // Apply pagination
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching content list:', error);
        return { content: [], total: 0, hasMore: false };
      }

      // Transform relations
      const transformedData = data?.map((item: any) => ({
        ...item,
        categories: item.categories?.map((c: any) => c.category) || [],
        tags: item.tags?.map((t: any) => t.tag) || []
      })) || [];

      const result = {
        content: transformedData,
        total: count || 0,
        hasMore: (offset + limit) < (count || 0)
      };

      setCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('Error in getContentList:', error);
      return { content: [], total: 0, hasMore: false };
    }
  }

  /**
   * Get featured content with caching
   */
  static async getFeaturedContent(
    contentType?: string,
    language: string = 'en',
    limit: number = 5
  ): Promise<CMSContent[]> {
    return (await CMSService.getContentList({
      content_type: contentType,
      language,
      featured: true,
      limit,
      sort_by: 'sort_order',
      sort_order: 'asc'
    })).content;
  }

  /**
   * Get related content based on categories and tags
   */
  static async getRelatedContent(
    contentId: string,
    limit: number = 5,
    language: string = 'en'
  ): Promise<CMSContent[]> {
    const cacheKey = getCacheKey('related_content', { contentId, limit, language });
    const cached = getFromCache<CMSContent[]>(cacheKey);
    if (cached) return cached;

    try {
      // First get the current content's categories and tags
      const { data: currentContent } = await supabase
        .from('cms_content')
        .select(`
          categories:cms_content_categories(category_id),
          tags:cms_content_tags(tag_id)
        `)
        .eq('id', contentId)
        .single();

      if (!currentContent) return [];

      const categoryIds = currentContent.categories?.map((c: any) => c.category_id) || [];
      const tagIds = currentContent.tags?.map((t: any) => t.tag_id) || [];

      // Find related content by categories and tags
      let relatedContent: CMSContent[] = [];

      if (categoryIds.length > 0 || tagIds.length > 0) {
        const { data } = await supabase
          .from('cms_content')
          .select(`
            *,
            featured_image:cms_media!featured_image_id(*),
            categories:cms_content_categories(category_id),
            tags:cms_content_tags(tag_id)
          `)
          .eq('status', 'published')
          .eq('language', language)
          .neq('id', contentId)
          .limit(limit * 2); // Get more to filter and sort

        if (data) {
          // Score content by relevance
          const scoredContent = data.map((content: any) => {
            let score = 0;
            const contentCategoryIds = content.categories?.map((c: any) => c.category_id) || [];
            const contentTagIds = content.tags?.map((t: any) => t.tag_id) || [];

            // Score by matching categories (higher weight)
            score += categoryIds.filter(id => contentCategoryIds.includes(id)).length * 3;
            // Score by matching tags
            score += tagIds.filter(id => contentTagIds.includes(id)).length * 1;

            return { ...content, relevanceScore: score };
          });

          // Sort by relevance and take top results
          relatedContent = scoredContent
            .filter(c => c.relevanceScore > 0)
            .sort((a, b) => b.relevanceScore - a.relevanceScore)
            .slice(0, limit);
        }
      }

      setCache(cacheKey, relatedContent);
      return relatedContent;
    } catch (error) {
      console.error('Error getting related content:', error);
      return [];
    }
  }

  // ===== UTILITY FUNCTIONS =====

  /**
   * Increment view count for content
   */
  static async incrementViewCount(contentId: string): Promise<void> {
    try {
      await supabase.rpc('increment_content_views', { content_id: contentId });
      
      // Clear related caches
      clearCache('content_by_slug');
      clearCache('content_list');
    } catch (error) {
      console.error('Error incrementing view count:', error);
    }
  }

  /**
   * Get content types
   */
  static async getContentTypes(): Promise<CMSContentType[]> {
    const cacheKey = getCacheKey('content_types');
    const cached = getFromCache<CMSContentType[]>(cacheKey);
    if (cached) return cached;

    try {
      const { data, error } = await supabase
        .from('cms_content_types')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) {
        console.error('Error fetching content types:', error);
        return [];
      }

      setCache(cacheKey, data || []);
      return data || [];
    } catch (error) {
      console.error('Error in getContentTypes:', error);
      return [];
    }
  }

  /**
   * Get categories with hierarchy
   */
  static async getCategories(language: string = 'en'): Promise<CMSCategory[]> {
    const cacheKey = getCacheKey('categories', { language });
    const cached = getFromCache<CMSCategory[]>(cacheKey);
    if (cached) return cached;

    try {
      const { data, error } = await supabase
        .from('cms_categories')
        .select('*')
        .eq('language', language)
        .eq('is_active', true)
        .order('sort_order');

      if (error) {
        console.error('Error fetching categories:', error);
        return [];
      }

      // Build hierarchy
      const categories = data || [];
      const categoryMap = new Map(categories.map(cat => [cat.id, { ...cat, children: [] }]));
      const rootCategories: CMSCategory[] = [];

      categories.forEach(category => {
        const cat = categoryMap.get(category.id)!;
        if (category.parent_id && categoryMap.has(category.parent_id)) {
          categoryMap.get(category.parent_id)!.children!.push(cat);
        } else {
          rootCategories.push(cat);
        }
      });

      setCache(cacheKey, rootCategories);
      return rootCategories;
    } catch (error) {
      console.error('Error in getCategories:', error);
      return [];
    }
  }

  /**
   * Get popular tags
   */
  static async getPopularTags(language: string = 'en', limit: number = 20): Promise<CMSTag[]> {
    const cacheKey = getCacheKey('popular_tags', { language, limit });
    const cached = getFromCache<CMSTag[]>(cacheKey);
    if (cached) return cached;

    try {
      const { data, error } = await supabase
        .from('cms_tags')
        .select('*')
        .eq('language', language)
        .gt('usage_count', 0)
        .order('usage_count', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching popular tags:', error);
        return [];
      }

      setCache(cacheKey, data || []);
      return data || [];
    } catch (error) {
      console.error('Error in getPopularTags:', error);
      return [];
    }
  }

  /**
   * Clear all caches
   */
  static clearAllCaches(): void {
    clearCache();
  }

  /**
   * Get CMS settings
   */
  static async getSettings(isPublic: boolean = true): Promise<CMSSettings> {
    const cacheKey = getCacheKey('settings', { isPublic });
    const cached = getFromCache<CMSSettings>(cacheKey);
    if (cached) return cached;

    try {
      let query = supabase.from('cms_settings').select('key, value');
      
      if (isPublic) {
        query = query.eq('is_public', true);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching settings:', error);
        return {};
      }

      const settings: CMSSettings = {};
      data?.forEach(setting => {
        settings[setting.key] = setting.value;
      });

      setCache(cacheKey, settings);
      return settings;
    } catch (error) {
      console.error('Error in getSettings:', error);
      return {};
    }
  }
}

// ===== REACT CACHE WRAPPERS =====

// Cached functions for React Server Components
export const getCachedContentBySlug = cache(CMSService.getContentBySlug);
export const getCachedContentList = cache(CMSService.getContentList);
export const getCachedFeaturedContent = cache(CMSService.getFeaturedContent);
export const getCachedRelatedContent = cache(CMSService.getRelatedContent);
export const getCachedContentTypes = cache(CMSService.getContentTypes);
export const getCachedCategories = cache(CMSService.getCategories);
export const getCachedPopularTags = cache(CMSService.getPopularTags);
export const getCachedSettings = cache(CMSService.getSettings);

export default CMSService;
