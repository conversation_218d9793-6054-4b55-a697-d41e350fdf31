"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "./providers";
import { Button } from "./ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/card";
import {
  Save,
  Eye,
  Calendar,
  Globe,
  Tag,
  Folder,
  Image,
  Link,
  Bold,
  Italic,
  List,
  ListOrdered,
  Quote,
  Code,
  Heading1,
  Heading2,
  Heading3,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Undo,
  Redo,
  Upload,
} from "lucide-react";

interface CMSContent {
  id?: string;
  title: string;
  slug: string;
  content_type_id: string;
  content: any;
  excerpt?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  featured_image_id?: string;
  status: 'draft' | 'published' | 'archived' | 'scheduled';
  published_at?: string;
  scheduled_at?: string;
  language: string;
  is_featured: boolean;
  categories?: string[];
  tags?: string[];
}

interface CMSContentEditorProps {
  content?: CMSContent | null;
  onSave: (content: CMSContent) => void;
  onCancel: () => void;
}

export function CMSContentEditor({ content, onSave, onCancel }: CMSContentEditorProps) {
  const { user } = useAuth();
  
  // Form state
  const [formData, setFormData] = useState<CMSContent>({
    title: '',
    slug: '',
    content_type_id: 'page',
    content: { body: '' },
    excerpt: '',
    meta_title: '',
    meta_description: '',
    meta_keywords: '',
    status: 'draft',
    language: 'en',
    is_featured: false,
    categories: [],
    tags: [],
  });

  // Editor state
  const [activeTab, setActiveTab] = useState<'content' | 'seo' | 'settings'>('content');
  const [saving, setSaving] = useState(false);
  const [preview, setPreview] = useState(false);
  const [contentTypes, setContentTypes] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [availableTags, setAvailableTags] = useState<any[]>([]);

  useEffect(() => {
    if (content) {
      setFormData({
        ...content,
        categories: content.categories || [],
        tags: content.tags || [],
      });
    }
    loadEditorData();
  }, [content]);

  const loadEditorData = async () => {
    try {
      const [typesRes, categoriesRes, tagsRes] = await Promise.all([
        fetch('/api/cms/content-types'),
        fetch('/api/cms/categories'),
        fetch('/api/cms/tags'),
      ]);

      if (typesRes.ok) {
        const typesData = await typesRes.json();
        setContentTypes(typesData.data || []);
      }

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json();
        setCategories(categoriesData.data || []);
      }

      if (tagsRes.ok) {
        const tagsData = await tagsRes.json();
        setAvailableTags(tagsData.data || []);
      }
    } catch (error) {
      console.error('Error loading editor data:', error);
    }
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title),
      meta_title: prev.meta_title || title,
    }));
  };

  const handleContentChange = (newContent: string) => {
    setFormData(prev => ({
      ...prev,
      content: { ...prev.content, body: newContent },
    }));
  };

  const handleSave = async (status?: string) => {
    if (!formData.title.trim()) {
      alert('Title is required');
      return;
    }

    setSaving(true);
    try {
      const saveData = {
        ...formData,
        status: status || formData.status,
        published_at: status === 'published' ? new Date().toISOString() : formData.published_at,
      };

      const method = content?.id ? 'PUT' : 'POST';
      const response = await fetch('/api/cms/content', {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(saveData),
      });

      if (response.ok) {
        const result = await response.json();
        onSave(result.data);
      } else {
        const error = await response.json();
        alert(`Error saving content: ${error.error}`);
      }
    } catch (error) {
      console.error('Error saving content:', error);
      alert('Error saving content');
    } finally {
      setSaving(false);
    }
  };

  const insertTextAtCursor = (before: string, after: string = '') => {
    const textarea = document.getElementById('content-editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    const newText = before + selectedText + after;
    
    const newValue = textarea.value.substring(0, start) + newText + textarea.value.substring(end);
    handleContentChange(newValue);
    
    // Set cursor position
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + before.length, start + before.length + selectedText.length);
    }, 0);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold">
            {content ? 'Edit Content' : 'Create New Content'}
          </h3>
          <div className="flex space-x-1">
            {['content', 'seo', 'settings'].map((tab) => (
              <Button
                key={tab}
                variant={activeTab === tab ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveTab(tab as any)}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Button>
            ))}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPreview(!preview)}
          >
            <Eye className="h-4 w-4 mr-1" />
            {preview ? 'Edit' : 'Preview'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleSave('draft')}
            disabled={saving}
          >
            <Save className="h-4 w-4 mr-1" />
            Save Draft
          </Button>
          <Button
            size="sm"
            onClick={() => handleSave('published')}
            disabled={saving}
            className="bg-green-600 hover:bg-green-700"
          >
            {saving ? 'Publishing...' : 'Publish'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Content Area */}
        <div className="lg:col-span-3 space-y-6">
          {/* Content Tab */}
          {activeTab === 'content' && (
            <div className="space-y-4">
              {/* Title */}
              <div>
                <label className="block text-sm font-medium mb-2">Title *</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  placeholder="Enter content title..."
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Slug */}
              <div>
                <label className="block text-sm font-medium mb-2">URL Slug</label>
                <div className="flex items-center">
                  <span className="text-gray-400 mr-2">/</span>
                  <input
                    type="text"
                    value={formData.slug}
                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                    placeholder="url-slug"
                    className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Content Editor */}
              <div>
                <label className="block text-sm font-medium mb-2">Content *</label>
                
                {/* Toolbar */}
                <div className="flex flex-wrap items-center gap-1 p-2 bg-gray-700 border border-gray-600 rounded-t-md">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => insertTextAtCursor('**', '**')}
                    title="Bold"
                  >
                    <Bold className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => insertTextAtCursor('*', '*')}
                    title="Italic"
                  >
                    <Italic className="h-4 w-4" />
                  </Button>
                  <div className="w-px h-6 bg-gray-600 mx-1" />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => insertTextAtCursor('# ', '')}
                    title="Heading 1"
                  >
                    <Heading1 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => insertTextAtCursor('## ', '')}
                    title="Heading 2"
                  >
                    <Heading2 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => insertTextAtCursor('### ', '')}
                    title="Heading 3"
                  >
                    <Heading3 className="h-4 w-4" />
                  </Button>
                  <div className="w-px h-6 bg-gray-600 mx-1" />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => insertTextAtCursor('- ', '')}
                    title="Bullet List"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => insertTextAtCursor('1. ', '')}
                    title="Numbered List"
                  >
                    <ListOrdered className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => insertTextAtCursor('> ', '')}
                    title="Quote"
                  >
                    <Quote className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => insertTextAtCursor('`', '`')}
                    title="Code"
                  >
                    <Code className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => insertTextAtCursor('[Link Text](', ')')}
                    title="Link"
                  >
                    <Link className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => insertTextAtCursor('![Alt Text](', ')')}
                    title="Image"
                  >
                    <Image className="h-4 w-4" aria-label="Insert image" />
                  </Button>
                </div>

                {/* Content Textarea */}
                {preview ? (
                  <div 
                    className="w-full min-h-96 p-4 bg-gray-700 border border-gray-600 border-t-0 rounded-b-md text-white prose prose-invert max-w-none"
                    dangerouslySetInnerHTML={{ 
                      __html: formData.content.body.replace(/\n/g, '<br>') 
                    }}
                  />
                ) : (
                  <textarea
                    id="content-editor"
                    value={formData.content.body}
                    onChange={(e) => handleContentChange(e.target.value)}
                    placeholder="Write your content here... You can use Markdown formatting."
                    className="w-full min-h-96 p-4 bg-gray-700 border border-gray-600 border-t-0 rounded-b-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-y font-mono"
                  />
                )}
              </div>

              {/* Excerpt */}
              <div>
                <label className="block text-sm font-medium mb-2">Excerpt</label>
                <textarea
                  value={formData.excerpt}
                  onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                  placeholder="Brief description or excerpt..."
                  rows={3}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          )}

          {/* SEO Tab */}
          {activeTab === 'seo' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Meta Title</label>
                <input
                  type="text"
                  value={formData.meta_title}
                  onChange={(e) => setFormData(prev => ({ ...prev, meta_title: e.target.value }))}
                  placeholder="SEO title for search engines..."
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-400 mt-1">
                  {formData.meta_title?.length || 0}/60 characters
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Meta Description</label>
                <textarea
                  value={formData.meta_description}
                  onChange={(e) => setFormData(prev => ({ ...prev, meta_description: e.target.value }))}
                  placeholder="SEO description for search engines..."
                  rows={3}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-400 mt-1">
                  {formData.meta_description?.length || 0}/160 characters
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Meta Keywords</label>
                <input
                  type="text"
                  value={formData.meta_keywords}
                  onChange={(e) => setFormData(prev => ({ ...prev, meta_keywords: e.target.value }))}
                  placeholder="keyword1, keyword2, keyword3..."
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Content Type</label>
                <select
                  value={formData.content_type_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, content_type_id: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {contentTypes.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Language</label>
                <select
                  value={formData.language}
                  onChange={(e) => setFormData(prev => ({ ...prev, language: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="en">English</option>
                  <option value="sv">Swedish</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Status</label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              {formData.status === 'scheduled' && (
                <div>
                  <label className="block text-sm font-medium mb-2">Scheduled Date</label>
                  <input
                    type="datetime-local"
                    value={formData.scheduled_at ? new Date(formData.scheduled_at).toISOString().slice(0, 16) : ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, scheduled_at: e.target.value ? new Date(e.target.value).toISOString() : undefined }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="featured"
                  checked={formData.is_featured}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_featured: e.target.checked }))}
                  className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="featured" className="text-sm font-medium">
                  Featured Content
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Categories */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-sm flex items-center">
                <Folder className="h-4 w-4 mr-2" />
                Categories
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {categories.map((category) => (
                <div key={category.id} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={`cat-${category.id}`}
                    checked={formData.categories?.includes(category.id) || false}
                    onChange={(e) => {
                      const newCategories = e.target.checked
                        ? [...(formData.categories || []), category.id]
                        : (formData.categories || []).filter(id => id !== category.id);
                      setFormData(prev => ({ ...prev, categories: newCategories }));
                    }}
                    className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor={`cat-${category.id}`} className="text-sm">
                    {category.name}
                  </label>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Tags */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-sm flex items-center">
                <Tag className="h-4 w-4 mr-2" />
                Tags
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {availableTags.slice(0, 10).map((tag) => (
                  <div key={tag.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`tag-${tag.id}`}
                      checked={formData.tags?.includes(tag.id) || false}
                      onChange={(e) => {
                        const newTags = e.target.checked
                          ? [...(formData.tags || []), tag.id]
                          : (formData.tags || []).filter(id => id !== tag.id);
                        setFormData(prev => ({ ...prev, tags: newTags }));
                      }}
                      className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor={`tag-${tag.id}`} className="text-sm">
                      {tag.name}
                    </label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-sm flex items-center">
                <Image className="h-4 w-4 mr-2" aria-label="Featured image icon" />
                Featured Image
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Button variant="outline" size="sm" className="w-full">
                <Upload className="h-4 w-4 mr-2" />
                Upload Image
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Footer Actions */}
      <div className="flex items-center justify-between pt-6 border-t border-gray-700">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => handleSave('draft')}
            disabled={saving}
          >
            Save Draft
          </Button>
          <Button
            onClick={() => handleSave('published')}
            disabled={saving}
            className="bg-green-600 hover:bg-green-700"
          >
            {saving ? 'Publishing...' : 'Publish'}
          </Button>
        </div>
      </div>
    </div>
  );
}
