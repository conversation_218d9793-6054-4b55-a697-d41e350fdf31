import Link from 'next/link'
import { Heart, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Youtube } from 'lucide-react'

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* About Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Light Upon Light</h3>
            <p className="text-gray-300 text-sm leading-relaxed mb-4">
              A place to learn wisdom, natural healing, and grow your mind through simple thinking and nature's lessons.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
                aria-label="Follow us on Facebook"
                title="Follow Light Upon Light on Facebook"
              >
                <Facebook className="h-5 w-5" aria-hidden="true" />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
                aria-label="Follow us on Twitter"
                title="Follow Light Upon Light on Twitter"
              >
                <Twitter className="h-5 w-5" aria-hidden="true" />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
                aria-label="Follow us on Instagram"
                title="Follow Light Upon Light on Instagram"
              >
                <Instagram className="h-5 w-5" aria-hidden="true" />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
                aria-label="Subscribe to our YouTube channel"
                title="Subscribe to Light Upon Light on YouTube"
              >
                <Youtube className="h-5 w-5" aria-hidden="true" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link href="/cupping" className="text-gray-300 hover:text-white transition-colors text-sm">Cupping Therapy</Link></li>
              <li><Link href="/plants" className="text-gray-300 hover:text-white transition-colors text-sm">Medicinal Plants</Link></li>
              <li><Link href="/heart-mind" className="text-gray-300 hover:text-white transition-colors text-sm">Heart Development</Link></li>
              <li><Link href="/quran" className="text-gray-300 hover:text-white transition-colors text-sm">Quran Verses</Link></li>
              <li><Link href="/wisdom" className="text-gray-300 hover:text-white transition-colors text-sm">Ancient Wisdom</Link></li>
            </ul>
          </div>

          {/* Community */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Community</h3>
            <ul className="space-y-2">
              <li><Link href="/forum" className="text-gray-300 hover:text-white transition-colors text-sm">Forum</Link></li>
              <li><Link href="/live" className="text-gray-300 hover:text-white transition-colors text-sm">Live Stream</Link></li>
              <li><Link href="/community" className="text-gray-300 hover:text-white transition-colors text-sm">Learning Hub</Link></li>
              <li><Link href="/quiz" className="text-gray-300 hover:text-white transition-colors text-sm">Quiz & Learning</Link></li>
              <li><Link href="/contact" className="text-gray-300 hover:text-white transition-colors text-sm">Contact Us</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-gray-400" />
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-gray-400" />
                <span className="text-gray-300 text-sm">Available via email</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-gray-400" />
                <span className="text-gray-300 text-sm">Global Online Community</span>
              </div>
            </div>
          </div>
        </div>

        {/* Legal Links */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-wrap justify-center md:justify-start space-x-6">
              <Link href="/terms" className="text-gray-400 hover:text-white text-sm transition-colors">Terms of Service</Link>
              <Link href="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors">Privacy Policy</Link>
              <Link href="/gdpr" className="text-gray-400 hover:text-white text-sm transition-colors">GDPR</Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white text-sm transition-colors">Cookies</Link>
              <Link href="/legal-compliance" className="text-gray-400 hover:text-white text-sm transition-colors">Legal Compliance</Link>
            </div>
            <div className="text-gray-400 text-sm">
              © 2024 Light Upon Light. All rights reserved.
            </div>
          </div>
        </div>

        {/* Quranic Verse - In Remembrance of God */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="bg-gradient-to-r from-blue-900/30 to-purple-900/30 border border-blue-800 rounded-lg p-6">
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center mb-4">
                <Heart className="h-5 w-5 text-blue-400 mr-2" />
                <span className="text-lg font-semibold text-blue-300">Divine Wisdom</span>
              </div>

              {/* Arabic Text */}
              <div className="text-center">
                <p className="text-2xl text-blue-200 font-arabic leading-relaxed mb-2" dir="rtl" lang="ar">
                  أَلَا بِذِكْرِ اللَّهِ تَطْمَئِنُّ الْقُلُوبُ
                </p>
                <p className="text-sm text-blue-300 italic">
                  "Verily, in the remembrance of Allah do hearts find rest"
                </p>
                <p className="text-xs text-blue-400 mt-1">
                  - Quran 13:28
                </p>
              </div>

              {/* English Translation & Reflection */}
              <div className="border-t border-blue-700 pt-4">
                <p className="text-blue-200 text-sm leading-relaxed">
                  <strong>Reflection:</strong> In the remembrance of God does the heart find rest. True peace comes from
                  connecting with the Divine through reflection, gratitude, and mindful awareness. This eternal wisdom
                  guides us toward inner tranquility and spiritual contentment.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Educational Purposes Legal Disclaimer */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="bg-amber-900/20 border border-amber-800 rounded-lg p-6">
            <div className="flex items-center justify-center mb-4">
              <Heart className="h-5 w-5 text-amber-400 mr-2" />
              <span className="text-lg font-semibold text-amber-300">Inner Peace & Educational Disclaimer</span>
            </div>
            <div className="text-center space-y-3">
              <p className="text-amber-200 text-sm leading-relaxed">
                <strong>Inner Peace Focus:</strong> Death is not curable. Kindly remember to care for your inner safety and awaken to the return of inner life —
                life filled with consciousness and understanding. Journey towards straight guidance and home of peace and light.
              </p>
              <p className="text-amber-200 text-sm leading-relaxed">
                <strong>Educational Purposes Only:</strong> All content on this website is provided for educational and informational purposes only,
                designed to promote inner peace, reflection, and spiritual growth. This information should not replace professional medical advice,
                diagnosis, or treatment. Always consult with qualified healthcare providers before starting any new therapy or making health-related decisions.
              </p>
              <p className="text-amber-200 text-sm leading-relaxed">
                <strong>Copyright & Content:</strong> All educational materials, interpretations, and content are original works created for peaceful
                reflection and learning. Quranic verses and ancient wisdom quotes are from public domain sources. All interpretations are original
                educational content designed to inspire inner peace and beneficial knowledge.
              </p>
              <p className="text-amber-200 text-xs">
                Explore more on <Link href="/heart-mind" className="underline hover:text-amber-100">heart development</Link>,
                <Link href="/cupping" className="underline hover:text-amber-100"> cupping</Link>,
                and <Link href="/privacy" className="underline hover:text-amber-100"> legal disclaimers</Link>.
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}