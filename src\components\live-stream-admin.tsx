"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Users,
  MessageCircle,
  Eye,
  Settings,
  Shield,
  Trash2,
  Clock,
  VolumeX,
  Star,
  Download,
  Save
} from "lucide-react";
import { DatabaseService } from "@/lib/database";

interface AdminPanelProps {
  isAdmin: boolean;
  user: any;
  onlineUsers: any[];
  blockedUsers: string[];
  mutedUsers: string[];
  timedOutUsers: { [key: string]: number };
  bannedWords: string[];
  slowModeEnabled: boolean;
  slowModeDelay: number;
  chatVisible: boolean;
  savedStreams: any[];
  onUserAction: (action: string, userId: string, data?: any) => void;
  onChatAction: (action: string, data?: any) => void;
  onStreamAction: (action: string, streamId?: string) => void;
}

export function LiveStreamAdminPanel({
  isAdmin,
  user,
  onlineUsers,
  blockedUsers,
  mutedUsers,
  timedOutUsers,
  bannedWords,
  slowModeEnabled,
  slowModeDelay,
  chatVisible,
  savedStreams,
  onUserAction,
  onChatAction,
  onStreamAction
}: AdminPanelProps) {
  const [showUserManagement, setShowUserManagement] = useState(false);
  const [showModerationPanel, setShowModerationPanel] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!isAdmin || !mounted) {
    return null;
  }

  return (
    <Card className="mt-6 bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="h-5 w-5 mr-2 text-red-400" />
          Admin Controls
          <span className="ml-2 px-2 py-1 bg-red-600 text-xs rounded-full">
            ADMIN
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
          <Button
            variant="outline"
            onClick={() => setShowUserManagement(!showUserManagement)}
          >
            <Users className="h-4 w-4 mr-2" />
            Manage Users ({onlineUsers.length})
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowModerationPanel(!showModerationPanel)}
          >
            <MessageCircle className="h-4 w-4 mr-2" />
            Moderate Chat
          </Button>
          <Button
            variant="outline"
            onClick={() => onStreamAction('analytics')}
          >
            <Eye className="h-4 w-4 mr-2" />
            View Analytics
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowSettings(!showSettings)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Stream Settings
          </Button>
          <Button
            variant="outline"
            onClick={() => onStreamAction('blocked-users')}
          >
            <Shield className="h-4 w-4 mr-2" />
            Blocked Users ({blockedUsers.length})
          </Button>
        </div>

        {/* User Management Panel */}
        {showUserManagement && (
          <Card className="mb-6 bg-gray-700 border-gray-600">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Users className="h-5 w-5 mr-2" />
                User Management & Moderation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-600 rounded-lg">
                    <div className="text-2xl font-bold text-green-400">
                      {onlineUsers.filter((u) => u.role === "viewer").length}
                    </div>
                    <div className="text-sm text-gray-300">Viewers</div>
                  </div>
                  <div className="text-center p-3 bg-gray-600 rounded-lg">
                    <div className="text-2xl font-bold text-blue-400">
                      {onlineUsers.filter((u) => u.role === "moderator").length}
                    </div>
                    <div className="text-sm text-gray-300">Moderators</div>
                  </div>
                  <div className="text-center p-3 bg-gray-600 rounded-lg">
                    <div className="text-2xl font-bold text-red-400">
                      {blockedUsers.length}
                    </div>
                    <div className="text-sm text-gray-300">Blocked</div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-semibold text-white">Online Users</h4>
                  {onlineUsers.map((user) => (
                    <div
                      key={user.userId}
                      className="flex items-center justify-between p-3 bg-gray-600 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-white">
                              {user.username}
                            </span>
                            {user.role === "admin" && (
                              <Shield className="h-4 w-4 text-red-400" />
                            )}
                            {user.role === "moderator" && (
                              <Star className="h-4 w-4 text-blue-400" />
                            )}
                            <span
                              className={`px-2 py-1 rounded text-xs ${
                                user.role === "admin"
                                  ? "bg-red-600 text-white"
                                  : user.role === "moderator"
                                    ? "bg-blue-600 text-white"
                                    : "bg-gray-500 text-white"
                              }`}
                            >
                              {user.role.toUpperCase()}
                            </span>
                          </div>
                          <div className="text-sm text-gray-300">{user.email}</div>
                          <div className="text-xs text-gray-400">
                            Joined: {mounted ? user.joinedAt.toLocaleTimeString() : "--:--:--"}
                          </div>
                        </div>
                      </div>

                      {user.role !== "admin" && (
                        <div className="flex flex-wrap gap-2">
                          {user.role === "viewer" ? (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onUserAction('assign-moderator', user.userId)}
                              className="text-blue-400 border-blue-400 hover:bg-blue-400 hover:text-white"
                            >
                              Make Moderator
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onUserAction('remove-moderator', user.userId)}
                              className="text-yellow-400 border-yellow-400 hover:bg-yellow-400 hover:text-black"
                            >
                              Remove Moderator
                            </Button>
                          )}

                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => onUserAction('timeout', user.userId, 300)}
                            className="text-purple-400 border-purple-400 hover:bg-purple-400 hover:text-white"
                            title="Timeout for 5 minutes"
                          >
                            Timeout
                          </Button>

                          {mutedUsers.includes(user.email) ? (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onUserAction('unmute', user.email)}
                              className="text-green-400 border-green-400 hover:bg-green-400 hover:text-white"
                            >
                              Unmute
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onUserAction('mute', user.email)}
                              className="text-yellow-400 border-yellow-400 hover:bg-yellow-400 hover:text-black"
                            >
                              Mute
                            </Button>
                          )}

                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => onUserAction('block', user.username)}
                            className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                          >
                            Block
                          </Button>

                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => onUserAction('kick', user.userId)}
                            className="text-orange-400 border-orange-400 hover:bg-orange-400 hover:text-white"
                          >
                            Kick
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Blocked Users */}
                {blockedUsers.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="font-semibold text-white">Blocked Users</h4>
                    {blockedUsers.map((username) => (
                      <div
                        key={username}
                        className="flex items-center justify-between p-3 bg-red-900/20 border border-red-600 rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                          <span className="text-red-300">{username}</span>
                          <span className="px-2 py-1 bg-red-600 text-white rounded text-xs">
                            BLOCKED
                          </span>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onUserAction('unblock', username)}
                          className="text-green-400 border-green-400 hover:bg-green-400 hover:text-white"
                        >
                          Unblock
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Chat Moderation Panel */}
        {showModerationPanel && (
          <Card className="mb-6 bg-gray-700 border-gray-600">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <MessageCircle className="h-5 w-5 mr-2" />
                Chat Moderation & Controls
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Chat Controls */}
                <div>
                  <h4 className="font-semibold text-white mb-3">Chat Controls</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <Button
                      variant="outline"
                      onClick={() => onChatAction('clear')}
                      className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear Chat
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => onChatAction('toggle-slow-mode')}
                      className={slowModeEnabled ? "text-green-400 border-green-400" : "text-gray-400 border-gray-400"}
                    >
                      <Clock className="h-4 w-4 mr-2" />
                      Slow Mode {slowModeEnabled ? 'ON' : 'OFF'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => onChatAction('toggle-visibility')}
                      className={chatVisible ? "text-blue-400 border-blue-400" : "text-gray-400 border-gray-400"}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Chat {chatVisible ? 'Visible' : 'Hidden'}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}
