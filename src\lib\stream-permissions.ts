/**
 * SECURE LIVE STREAM PERMISSION CONFIGURATION
 * 
 * This file controls who can broadcast and moderate live streams.
 * Only change the GUEST_BROADCASTER_EMAIL when needed.
 * 
 * SECURITY LEVELS:
 * 1. STREAM_ADMIN_EMAIL - Full live stream control (NEVER CHANGE)
 * 2. GUEST_BROADCASTER_EMAIL - Temporary broadcast access (CHANGEABLE)
 * 3. SITE_ADMIN_EMAIL - Full platform control (NEVER CHANGE)
 * 4. PUBLIC_CONTACT_EMAIL - Public contact/feedback (NEVER CHANGE)
 */

// ========================================
// SECURE CONFIGURATION - DO NOT MODIFY THESE
// ========================================

/**
 * FULL SITE ADMINISTRATOR
 * Has COMPLETE control over everything: site admin + live streaming + broadcast + moderation
 * Can grant guest access, manage entire platform, broadcast publicly
 * ⚠️ SECURITY: NEVER CHANGE THIS EMAIL
 */
export const FULL_ADMIN_EMAIL = '<EMAIL>';

/** 
 * Public Contact & Feedback Email
 * Used for contact forms, feedback, and public communications
 * ⚠️ SECURITY: NEVER CHANGE THIS EMAIL
 */
export const PUBLIC_CONTACT_EMAIL = '<EMAIL>';

// ========================================
// CONFIGURABLE GUEST ACCESS - CHANGE ONLY THIS
// ========================================

/**
 * Guest Broadcaster Email
 * LIVE STREAM ONLY access - change this when granting access to someone else
 *
 * TO GRANT ACCESS: Replace the email below with the guest's email
 * TO REVOKE ACCESS: Set to empty string '' or null
 *
 * PERMISSIONS GRANTED (LIVE STREAM ONLY):
 * - Start/stop live streams and broadcast publicly
 * - Camera/microphone controls
 * - YouTube iframe integration
 * - Stream moderation (timeout, delete messages, mute, kick)
 * - Stream title editing
 * - Chat moderation with YouTube-style options
 *
 * PERMISSIONS NOT GRANTED:
 * - Cannot grant access to others
 * - Cannot change site settings
 * - Cannot manage user accounts
 * - Cannot access other admin panels
 * - Only live stream related permissions
 */
export const GUEST_BROADCASTER_EMAIL: string | null = '';

// EXAMPLE: To grant access to someone, change the line above to:
// export const GUEST_BROADCASTER_EMAIL: string | null = '<EMAIL>';
//
// To revoke access, change it back to:
// export const GUEST_BROADCASTER_EMAIL: string | null = '';

// ========================================
// PERMISSION CHECKER FUNCTIONS
// ========================================

export interface UserPermissions {
  canBroadcast: boolean;
  canModerateChat: boolean;
  canManageUsers: boolean;
  canManageSite: boolean;
  canGrantAccess: boolean;
  canAccessAllAdminPanels: boolean;
  role: 'full_admin' | 'guest_broadcaster' | 'viewer';
  displayName: string;
}

/**
 * Get user permissions based on email
 */
export function getUserPermissions(email: string | null | undefined): UserPermissions {
  if (!email) {
    return {
      canBroadcast: false,
      canModerateChat: false,
      canManageUsers: false,
      canManageSite: false,
      canGrantAccess: false,
      canAccessAllAdminPanels: false,
      role: 'viewer',
      displayName: '👁️ Viewer'
    };
  }

  // Full Admin (<EMAIL>) - EVERYTHING
  if (email === FULL_ADMIN_EMAIL) {
    return {
      canBroadcast: true,
      canModerateChat: true,
      canManageUsers: true,
      canManageSite: true,
      canGrantAccess: true,
      canAccessAllAdminPanels: true,
      role: 'full_admin',
      displayName: '👑 Full Admin'
    };
  }

  // Guest Broadcaster (configurable) - LIVE STREAM ONLY
  if (GUEST_BROADCASTER_EMAIL && email === GUEST_BROADCASTER_EMAIL) {
    return {
      canBroadcast: true,
      canModerateChat: true,
      canManageUsers: false,
      canManageSite: false,
      canGrantAccess: false,
      canAccessAllAdminPanels: false,
      role: 'guest_broadcaster',
      displayName: '📡 Guest Broadcaster'
    };
  }

  // Default viewer
  return {
    canBroadcast: false,
    canModerateChat: false,
    canManageUsers: false,
    canManageSite: false,
    canGrantAccess: false,
    canAccessAllAdminPanels: false,
    role: 'viewer',
    displayName: '👁️ Viewer'
  };
}

/**
 * Chat moderation actions available to different roles
 * Based on YouTube-style moderation with no copyright violations
 */
export interface ModerationActions {
  canDeleteMessage: boolean;
  canTimeoutUser: boolean;
  canKickUser: boolean;
  canBanUser: boolean;
  canMuteUser: boolean;
  canSlowMode: boolean;
  canManageFilters: boolean;
  canPinMessage: boolean;
  canHideUser: boolean;
  canReportUser: boolean;
  canManageEmotes: boolean;
}

/**
 * Get moderation actions based on user permissions
 */
export function getModerationActions(permissions: UserPermissions): ModerationActions {
  if (permissions.role === 'viewer') {
    return {
      canDeleteMessage: false,
      canTimeoutUser: false,
      canKickUser: false,
      canBanUser: false,
      canMuteUser: false,
      canSlowMode: false,
      canManageFilters: false,
      canPinMessage: false,
      canHideUser: false,
      canReportUser: true, // Viewers can report
      canManageEmotes: false
    };
  }

  if (permissions.role === 'guest_broadcaster') {
    return {
      canDeleteMessage: true,
      canTimeoutUser: true,
      canKickUser: true,
      canBanUser: false, // No permanent bans for guests
      canMuteUser: true,
      canSlowMode: true,
      canManageFilters: true,
      canPinMessage: true,
      canHideUser: true,
      canReportUser: true,
      canManageEmotes: false
    };
  }

  // Full admin has everything
  return {
    canDeleteMessage: true,
    canTimeoutUser: true,
    canKickUser: true,
    canBanUser: true,
    canMuteUser: true,
    canSlowMode: true,
    canManageFilters: true,
    canPinMessage: true,
    canHideUser: true,
    canReportUser: true,
    canManageEmotes: true
  };
}

/**
 * Security audit log
 */
export function logPermissionCheck(email: string, action: string, granted: boolean) {
  if (typeof window !== 'undefined') {
    console.log(`[SECURITY] ${email} attempted ${action}: ${granted ? 'GRANTED' : 'DENIED'}`);
  }
}
