{"name": "lightuponlight", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-slot": "^1.0.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.38.4", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "js-cookie": "^3.0.5", "lucide-react": "^0.294.0", "next": "^14.2.30", "next-auth": "^4.24.5", "next-intl": "^3.3.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-markdown": "^9.0.1", "react-player": "^2.13.0", "remark-gfm": "^4.0.0", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.0.0", "zod": "^3.22.4"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}