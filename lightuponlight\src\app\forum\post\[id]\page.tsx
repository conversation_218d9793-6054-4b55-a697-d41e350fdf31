'use client'

import { useState } from 'react'
import { useAuth } from '@/components/providers'
import { Button } from '@/components/ui/button'
import { 
  MessageCircle, 
  Heart, 
  Share2, 
  Flag, 
  Calendar,
  User,
  Reply,
  ThumbsUp,
  ThumbsDown,
  Edit,
  Trash2,
  Crown,
  Shield
} from 'lucide-react'
import { formatDate } from '@/lib/utils'

// Mock data for demonstration
const postData = {
  id: '1',
  title: 'The Wisdom Behind Fasting: A Comprehensive Guide',
  content: `<PERSON><PERSON><PERSON><PERSON> brothers and sisters,

I wanted to share some insights about the profound wisdom behind fasting in Islam, both from spiritual and scientific perspectives.

## Spiritual Benefits

Fasting is not just about abstaining from food and drink. It's a comprehensive spiritual exercise that:

1. **Develops Taqwa (God-consciousness)**: As mentioned in the Quran: "O you who believe! Fasting is prescribed for you as it was prescribed for those before you, that you may develop taqwa." (2:183)

2. **Increases empathy**: By experiencing hunger, we better understand the plight of the less fortunate.

3. **Strengthens self-discipline**: Regular fasting builds willpower and self-control.

## Scientific Benefits

Modern research has confirmed many health benefits of intermittent fasting:

- **Cellular repair**: Fasting triggers autophagy, helping cells repair themselves
- **Improved insulin sensitivity**: Helps regulate blood sugar levels
- **Brain health**: May improve cognitive function and protect against neurodegenerative diseases
- **Longevity**: Studies suggest fasting may extend lifespan

## Practical Tips

For those new to fasting or looking to improve their experience:

1. Start gradually if you're not used to fasting
2. Stay hydrated during non-fasting hours
3. Eat nutritious foods during iftar and suhur
4. Use fasting time for increased worship and reflection

What are your experiences with fasting? I'd love to hear your insights and any additional benefits you've noticed.

Barakallahu feekum!`,
  author: {
    name: 'WisdomSeeker',
    avatar: '/api/placeholder/40/40',
    joinDate: '2023-06-15',
    posts: 156,
    reputation: 892,
    isVerified: true,
    isModerator: false
  },
  category: 'Islamic Education',
  createdAt: new Date('2024-01-15T10:30:00'),
  updatedAt: new Date('2024-01-15T10:30:00'),
  views: 234,
  likes: 45,
  dislikes: 2,
  isPinned: true,
  isLocked: false,
  tags: ['fasting', 'spirituality', 'health', 'ramadan']
}

const replies = [
  {
    id: '1',
    content: 'Jazakallahu khair for this comprehensive post! I especially appreciate the scientific perspective. It\'s amazing how modern research confirms what Islam has taught us for centuries.',
    author: {
      name: 'HealthyMuslim',
      avatar: '/api/placeholder/40/40',
      joinDate: '2023-08-20',
      posts: 89,
      reputation: 445,
      isVerified: false,
      isModerator: false
    },
    createdAt: new Date('2024-01-15T11:15:00'),
    likes: 12,
    dislikes: 0,
    replies: []
  },
  {
    id: '2',
    content: 'This is excellent! I\'ve been fasting regularly for years and can confirm the mental clarity benefits. During Ramadan especially, I feel more focused and spiritually connected.',
    author: {
      name: 'Mohammad Abbas',
      avatar: '/api/placeholder/40/40',
      joinDate: '2023-01-01',
      posts: 234,
      reputation: 1250,
      isVerified: true,
      isModerator: true
    },
    createdAt: new Date('2024-01-15T12:30:00'),
    likes: 18,
    dislikes: 0,
    replies: [
      {
        id: '2-1',
        content: 'Absolutely agree! The mental clarity is remarkable. I also notice improved sleep quality during fasting periods.',
        author: {
          name: 'NightWorshipper',
          avatar: '/api/placeholder/40/40',
          joinDate: '2023-09-10',
          posts: 67,
          reputation: 234,
          isVerified: false,
          isModerator: false
        },
        createdAt: new Date('2024-01-15T13:45:00'),
        likes: 8,
        dislikes: 0
      }
    ]
  }
]

export default function ForumPostPage({ params }: { params: { id: string } }) {
  const { user } = useAuth()
  const [newReply, setNewReply] = useState('')
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [postReplies, setPostReplies] = useState(replies)
  const [liked, setLiked] = useState(false)
  const [disliked, setDisliked] = useState(false)

  const isUserAdmin = user?.email === '<EMAIL>'
  const isUserModerator = isUserAdmin || user?.role === 'moderator'

  const handleSubmitReply = (e: React.FormEvent) => {
    e.preventDefault()
    if (!newReply.trim() || !user) return

    const reply = {
      id: Date.now().toString(),
      content: newReply.trim(),
      author: {
        name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous',
        avatar: '/api/placeholder/40/40',
        joinDate: '2024-01-01',
        posts: 1,
        reputation: 10,
        isVerified: false,
        isModerator: isUserModerator
      },
      createdAt: new Date(),
      likes: 0,
      dislikes: 0,
      replies: []
    }

    if (replyingTo) {
      // Add as nested reply
      setPostReplies(prev => prev.map(r => 
        r.id === replyingTo 
          ? { ...r, replies: [...(r.replies || []), reply] }
          : r
      ))
    } else {
      // Add as top-level reply
      setPostReplies(prev => [...prev, reply])
    }

    setNewReply('')
    setReplyingTo(null)
  }

  const handleLike = () => {
    setLiked(!liked)
    if (disliked) setDisliked(false)
  }

  const handleDislike = () => {
    setDisliked(!disliked)
    if (liked) setLiked(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="mb-6 text-sm">
          <ol className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
            <li><a href="/forum" className="hover:text-blue-600">Forum</a></li>
            <li>/</li>
            <li><a href={`/forum/category/1`} className="hover:text-blue-600">{postData.category}</a></li>
            <li>/</li>
            <li className="text-gray-900 dark:text-white">{postData.title}</li>
          </ol>
        </nav>

        {/* Main Post */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-6">
          {/* Post Header */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-4">
                <img
                  src={postData.author.avatar}
                  alt={postData.author.name}
                  className="w-12 h-12 rounded-full"
                />
                <div>
                  <div className="flex items-center space-x-2">
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {postData.author.name}
                    </h3>
                    {postData.author.isVerified && (
                      <Shield className="h-4 w-4 text-blue-600" />
                    )}
                    {postData.author.isModerator && (
                      <Crown className="h-4 w-4 text-yellow-600" />
                    )}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    {postData.author.posts} posts • {postData.author.reputation} reputation
                  </div>
                </div>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                <div className="flex items-center space-x-1 mb-1">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDate(postData.createdAt)}</span>
                </div>
                <div>{postData.views} views</div>
              </div>
            </div>
          </div>

          {/* Post Title */}
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {postData.title}
            </h1>
            <div className="flex flex-wrap gap-2">
              {postData.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-sm"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </div>

          {/* Post Content */}
          <div className="p-6">
            <div className="prose prose-lg dark:prose-invert max-w-none">
              {postData.content.split('\n').map((paragraph, index) => {
                if (paragraph.startsWith('## ')) {
                  return (
                    <h2 key={index} className="text-xl font-bold text-gray-900 dark:text-white mt-6 mb-3">
                      {paragraph.replace('## ', '')}
                    </h2>
                  )
                } else if (paragraph.startsWith('1. ') || paragraph.startsWith('- ')) {
                  return (
                    <li key={index} className="text-gray-700 dark:text-gray-300 ml-4">
                      {paragraph.replace(/^[1-9]\. |^- /, '')}
                    </li>
                  )
                } else if (paragraph.trim()) {
                  return (
                    <p key={index} className="text-gray-700 dark:text-gray-300 mb-4">
                      {paragraph}
                    </p>
                  )
                }
                return null
              })}
            </div>
          </div>

          {/* Post Actions */}
          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleLike}
                  className={`flex items-center space-x-1 px-3 py-1 rounded ${
                    liked ? 'bg-green-100 text-green-800' : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <ThumbsUp className="h-4 w-4" />
                  <span>{postData.likes + (liked ? 1 : 0)}</span>
                </button>
                <button
                  onClick={handleDislike}
                  className={`flex items-center space-x-1 px-3 py-1 rounded ${
                    disliked ? 'bg-red-100 text-red-800' : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <ThumbsDown className="h-4 w-4" />
                  <span>{postData.dislikes + (disliked ? 1 : 0)}</span>
                </button>
                <button className="flex items-center space-x-1 text-gray-600 hover:bg-gray-100 px-3 py-1 rounded">
                  <Share2 className="h-4 w-4" />
                  <span>Share</span>
                </button>
              </div>
              <div className="flex items-center space-x-2">
                {user && (
                  <button
                    onClick={() => setReplyingTo(null)}
                    className="flex items-center space-x-1 text-blue-600 hover:bg-blue-50 px-3 py-1 rounded"
                  >
                    <Reply className="h-4 w-4" />
                    <span>Reply</span>
                  </button>
                )}
                <button className="text-gray-600 hover:bg-gray-100 p-1 rounded">
                  <Flag className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Replies Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            Replies ({postReplies.length})
          </h2>

          {/* Reply Form */}
          {user && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <form onSubmit={handleSubmitReply}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                    {replyingTo ? 'Reply to comment' : 'Add your reply'}
                  </label>
                  <textarea
                    value={newReply}
                    onChange={(e) => setNewReply(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Share your thoughts..."
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    Be respectful and constructive in your response
                  </div>
                  <div className="flex space-x-2">
                    {replyingTo && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setReplyingTo(null)}
                      >
                        Cancel
                      </Button>
                    )}
                    <Button type="submit">Post Reply</Button>
                  </div>
                </div>
              </form>
            </div>
          )}

          {/* Replies List */}
          {postReplies.map((reply) => (
            <div key={reply.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <img
                      src={reply.author.avatar}
                      alt={reply.author.name}
                      className="w-10 h-10 rounded-full"
                    />
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-gray-900 dark:text-white">
                          {reply.author.name}
                        </h4>
                        {reply.author.name === 'Mohammad Abbas' && (
                          <Crown className="h-3 w-3 text-yellow-600" />
                        )}
                        {reply.author.isModerator && (
                          <Shield className="h-3 w-3 text-blue-600" />
                        )}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {formatDate(reply.createdAt)}
                      </div>
                    </div>
                  </div>
                  {isUserModerator && (
                    <div className="flex space-x-1">
                      <button className="text-gray-600 hover:text-red-600 p-1">
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </div>

                <div className="prose dark:prose-invert">
                  <p className="text-gray-700 dark:text-gray-300">{reply.content}</p>
                </div>

                <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-4">
                    <button className="flex items-center space-x-1 text-gray-600 hover:text-green-600">
                      <ThumbsUp className="h-4 w-4" />
                      <span>{reply.likes}</span>
                    </button>
                    <button className="flex items-center space-x-1 text-gray-600 hover:text-red-600">
                      <ThumbsDown className="h-4 w-4" />
                      <span>{reply.dislikes}</span>
                    </button>
                  </div>
                  {user && (
                    <button
                      onClick={() => setReplyingTo(reply.id)}
                      className="text-blue-600 hover:bg-blue-50 px-2 py-1 rounded text-sm"
                    >
                      Reply
                    </button>
                  )}
                </div>

                {/* Nested Replies */}
                {reply.replies && reply.replies.length > 0 && (
                  <div className="ml-8 mt-4 space-y-4">
                    {reply.replies.map((nestedReply) => (
                      <div key={nestedReply.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <img
                            src={nestedReply.author.avatar}
                            alt={nestedReply.author.name}
                            className="w-8 h-8 rounded-full"
                          />
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h5 className="font-medium text-gray-900 dark:text-white">
                                {nestedReply.author.name}
                              </h5>
                              <span className="text-sm text-gray-600 dark:text-gray-300">
                                {formatDate(nestedReply.createdAt)}
                              </span>
                            </div>
                            <p className="text-gray-700 dark:text-gray-300 text-sm">
                              {nestedReply.content}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
