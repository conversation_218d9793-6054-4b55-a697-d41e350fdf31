import { supabase } from './supabase'

// Database service for all read/write operations
export class DatabaseService {
  
  // ===== FORUM OPERATIONS =====
  
  // Get all forum posts with pagination
  static async getForumPosts(page = 1, limit = 10, category?: string) {
    let query = supabase
      .from('forum_posts')
      .select(`
        *,
        author:users(id, full_name, email, avatar_url),
        category:forum_categories(id, name, color),
        replies:forum_replies(
          *,
          author:users(id, full_name, email, avatar_url)
        )
      `)
      .eq('is_deleted', false)
      .order('is_pinned', { ascending: false })
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1)

    if (category && category !== 'all') {
      query = query.eq('category_id', category)
    }

    const { data, error } = await query
    if (error) throw error
    return data
  }

  // Create new forum post
  static async createForumPost(post: {
    title: string
    content: string
    author_id: string
    category_id: string
  }) {
    const { data, error } = await supabase
      .from('forum_posts')
      .insert([post])
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Add reply to forum post
  static async addForumReply(reply: {
    post_id: string
    content: string
    author_id: string
  }) {
    const { data, error } = await supabase
      .from('forum_replies')
      .insert([reply])
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Like/dislike post or reply
  static async toggleLike(userId: string, postId: string, isLike: boolean, replyId?: string) {
    // Check if user already liked/disliked
    const { data: existing } = await supabase
      .from('post_likes')
      .select('*')
      .eq('user_id', userId)
      .eq('post_id', postId)
      .eq('reply_id', replyId || null)
      .single()

    if (existing) {
      if (existing.is_like === isLike) {
        // Remove like/dislike
        await supabase
          .from('post_likes')
          .delete()
          .eq('id', existing.id)
      } else {
        // Change like to dislike or vice versa
        await supabase
          .from('post_likes')
          .update({ is_like: isLike })
          .eq('id', existing.id)
      }
    } else {
      // Add new like/dislike
      await supabase
        .from('post_likes')
        .insert([{
          user_id: userId,
          post_id: postId,
          reply_id: replyId || null,
          is_like: isLike
        }])
    }

    // Update counts in posts/replies table
    await this.updateLikeCounts(postId, replyId)
  }

  // Update like/dislike counts
  static async updateLikeCounts(postId: string, replyId?: string) {
    if (replyId) {
      // Update reply counts
      const { data: likes } = await supabase
        .from('post_likes')
        .select('is_like')
        .eq('post_id', postId)
        .eq('reply_id', replyId)

      const likeCount = likes?.filter(l => l.is_like).length || 0
      const dislikeCount = likes?.filter(l => !l.is_like).length || 0

      await supabase
        .from('forum_replies')
        .update({ likes: likeCount, dislikes: dislikeCount })
        .eq('id', replyId)
    } else {
      // Update post counts
      const { data: likes } = await supabase
        .from('post_likes')
        .select('is_like')
        .eq('post_id', postId)
        .is('reply_id', null)

      const likeCount = likes?.filter(l => l.is_like).length || 0
      const dislikeCount = likes?.filter(l => !l.is_like).length || 0

      await supabase
        .from('forum_posts')
        .update({ likes: likeCount, dislikes: dislikeCount })
        .eq('id', postId)
    }
  }

  // ===== LIVE STREAM OPERATIONS =====

  // Get active live stream with public access
  static async getActiveLiveStream() {
    const { data, error } = await supabase
      .from('live_streams')
      .select(`
        *,
        streamer:users(id, full_name, email),
        current_viewers,
        total_messages
      `)
      .eq('is_live', true)
      .single()

    if (error && error.code !== 'PGRST116') throw error // PGRST116 = no rows returned
    return data
  }

  // Get public stream data (for viewers)
  static async getPublicStreamData(streamId: string) {
    const { data, error } = await supabase
      .from('live_streams')
      .select(`
        id,
        title,
        description,
        started_at,
        current_viewers,
        is_live,
        stream_quality,
        access_type
      `)
      .eq('id', streamId)
      .eq('is_live', true)
      .single()

    if (error) throw error
    return data
  }

  // Start live stream
  static async startLiveStream(streamData: {
    title: string
    description?: string
    streamer_id: string
  }) {
    const { data, error } = await supabase
      .from('live_streams')
      .insert([{
        ...streamData,
        is_live: true,
        started_at: new Date().toISOString()
      }])
      .select()
      .single()

    if (error) throw error
    return data
  }

  // End live stream
  static async endLiveStream(streamId: string) {
    const { data, error } = await supabase
      .from('live_streams')
      .update({
        is_live: false,
        ended_at: new Date().toISOString()
      })
      .eq('id', streamId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Get chat messages for stream
  static async getChatMessages(streamId: string, limit = 50) {
    const { data, error } = await supabase
      .from('chat_messages')
      .select(`
        *,
        user:users(id, full_name, email, role, avatar_url)
      `)
      .eq('stream_id', streamId)
      .eq('is_deleted', false)
      .order('created_at', { ascending: true })
      .limit(limit)

    if (error) throw error
    return data
  }

  // Add chat message
  static async addChatMessage(message: {
    stream_id: string
    user_id: string
    message: string
  }) {
    const { data, error } = await supabase
      .from('chat_messages')
      .insert([message])
      .select(`
        *,
        user:users(id, full_name, email, role, avatar_url)
      `)
      .single()

    if (error) throw error
    return data
  }

  // Delete chat message (admin only)
  static async deleteChatMessage(messageId: string, userId: string) {
    // Verify user authentication first
    if (!userId) {
      throw new Error('Authentication required')
    }

    // Check if user is admin with proper email verification
    const { data: user } = await supabase
      .from('users')
      .select('role, email')
      .eq('id', userId)
      .single()

    if (!user) {
      throw new Error('User not found')
    }

    // Verify admin role AND email
    const isAdmin = user.role === 'admin' &&
      (user.email === '<EMAIL>' || user.email === '<EMAIL>')

    if (!isAdmin) {
      throw new Error('Unauthorized: Only verified admins can delete messages')
    }

    // Validate messageId format (UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(messageId)) {
      throw new Error('Invalid message ID format')
    }

    const { error } = await supabase
      .from('chat_messages')
      .update({ is_deleted: true })
      .eq('id', messageId)

    if (error) throw error
  }

  // Get saved streams (admin only)
  static async getSavedStreams(limit = 20) {
    const { data, error } = await supabase
      .from('live_streams')
      .select(`
        *,
        streamer:users(id, full_name, email)
      `)
      .eq('is_live', false)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data
  }

  // Delete saved stream (admin only)
  static async deleteSavedStream(streamId: string, userId: string) {
    // Verify user authentication first
    if (!userId) {
      throw new Error('Authentication required')
    }

    // Check if user is admin with proper email verification
    const { data: user } = await supabase
      .from('users')
      .select('role, email')
      .eq('id', userId)
      .single()

    if (!user) {
      throw new Error('User not found')
    }

    // Verify admin role AND email
    const isAdmin = user.role === 'admin' &&
      (user.email === '<EMAIL>' || user.email === '<EMAIL>')

    if (!isAdmin) {
      throw new Error('Unauthorized: Only verified admins can delete streams')
    }

    // Validate streamId format (UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(streamId)) {
      throw new Error('Invalid stream ID format')
    }

    const { error } = await supabase
      .from('live_streams')
      .delete()
      .eq('id', streamId)

    if (error) throw error
  }

  // ===== LEARNING PATHS OPERATIONS =====

  // Get all learning paths
  static async getLearningPaths() {
    const { data, error } = await supabase
      .from('learning_paths')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  }

  // Enroll user in learning path
  static async enrollInLearningPath(userId: string, learningPathId: string) {
    const { data, error } = await supabase
      .from('learning_enrollments')
      .insert([{
        user_id: userId,
        learning_path_id: learningPathId
      }])
      .select()
      .single()

    if (error) throw error

    // Update enrollment count
    await supabase.rpc('increment_enrollment_count', {
      path_id: learningPathId
    })

    return data
  }

  // ===== EVENTS OPERATIONS =====

  // Get upcoming events
  static async getUpcomingEvents() {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('is_active', true)
      .gte('event_date', new Date().toISOString().split('T')[0])
      .order('event_date', { ascending: true })
      .order('event_time', { ascending: true })

    if (error) throw error
    return data
  }

  // Register for event
  static async registerForEvent(userId: string, eventId: string) {
    const { data, error } = await supabase
      .from('event_registrations')
      .insert([{
        user_id: userId,
        event_id: eventId
      }])
      .select()
      .single()

    if (error) throw error

    // Update attendee count
    await supabase.rpc('increment_event_attendees', {
      event_id: eventId
    })

    return data
  }

  // ===== ACHIEVEMENTS OPERATIONS =====

  // Get user achievements
  static async getUserAchievements(userId: string) {
    const { data, error } = await supabase
      .from('user_achievements')
      .select(`
        *,
        achievement:achievements(*)
      `)
      .eq('user_id', userId)

    if (error) throw error
    return data
  }

  // Award achievement to user
  static async awardAchievement(userId: string, achievementId: string) {
    const { data, error } = await supabase
      .from('user_achievements')
      .insert([{
        user_id: userId,
        achievement_id: achievementId,
        is_completed: true,
        progress: 100
      }])
      .select(`
        *,
        achievement:achievements(*)
      `)
      .single()

    if (error) throw error
    return data
  }

  // ===== ANALYTICS OPERATIONS =====

  // Track analytics event
  static async trackEvent(eventData: {
    user_id?: string
    event_type: string
    event_data?: any
    page_url?: string
    referrer?: string
    ip_address?: string
    user_agent?: string
  }) {
    const { error } = await supabase
      .from('analytics_events')
      .insert([eventData])

    if (error) throw error
  }

  // Get website statistics
  static async getWebsiteStats() {
    const [
      { count: totalUsers },
      { count: totalPosts },
      { count: totalStreams },
      { data: totalViews }
    ] = await Promise.all([
      supabase.from('users').select('*', { count: 'exact', head: true }),
      supabase.from('forum_posts').select('*', { count: 'exact', head: true }).eq('is_deleted', false),
      supabase.from('live_streams').select('*', { count: 'exact', head: true }),
      supabase.from('forum_posts').select('views').eq('is_deleted', false)
    ])

    const totalViewsCount = totalViews?.reduce((sum, post) => sum + (post.views || 0), 0) || 0

    return {
      totalUsers: totalUsers || 0,
      totalPosts: totalPosts || 0,
      totalStreams: totalStreams || 0,
      totalViews: totalViewsCount
    }
  }

  // ===== USER MANAGEMENT OPERATIONS =====

  // Update user profile
  static async updateUserProfile(userId: string, updates: any) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Delete user account
  static async deleteUser(userId: string) {
    // First, delete all user-related data
    await Promise.all([
      supabase.from('forum_posts').update({ is_deleted: true }).eq('author_id', userId),
      supabase.from('forum_replies').update({ is_deleted: true }).eq('author_id', userId),
      supabase.from('chat_messages').update({ is_deleted: true }).eq('user_id', userId),
      supabase.from('learning_enrollments').delete().eq('user_id', userId),
      supabase.from('event_registrations').delete().eq('user_id', userId),
      supabase.from('user_achievements').delete().eq('user_id', userId),
      supabase.from('post_likes').delete().eq('user_id', userId),
    ])

    // Finally, delete the user
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', userId)

    if (error) throw error
  }

  // Get all user data for GDPR compliance
  static async getUserData(userId: string) {
    const [
      { data: profile },
      { data: posts },
      { data: replies },
      { data: chatMessages },
      { data: enrollments },
      { data: registrations },
      { data: achievements },
      { data: likes },
      { data: analytics }
    ] = await Promise.all([
      supabase.from('users').select('*').eq('id', userId).single(),
      supabase.from('forum_posts').select('*').eq('author_id', userId),
      supabase.from('forum_replies').select('*').eq('author_id', userId),
      supabase.from('chat_messages').select('*').eq('user_id', userId),
      supabase.from('learning_enrollments').select('*').eq('user_id', userId),
      supabase.from('event_registrations').select('*').eq('user_id', userId),
      supabase.from('user_achievements').select('*').eq('user_id', userId),
      supabase.from('post_likes').select('*').eq('user_id', userId),
      supabase.from('analytics_events').select('*').eq('user_id', userId)
    ])

    return {
      profile,
      posts,
      replies,
      chatMessages,
      enrollments,
      registrations,
      achievements,
      likes,
      analytics
    }
  }
}
