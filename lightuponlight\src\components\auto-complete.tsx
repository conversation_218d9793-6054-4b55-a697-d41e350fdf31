'use client'

import { useState, useEffect, useRef } from 'react'
import { useLanguage } from './language-provider'
import { useData } from './data-provider'
import { ChevronDown, Check, X } from 'lucide-react'

interface AutoCompleteOption {
  value: string
  label: string
  category?: string
  description?: string
}

interface AutoCompleteProps {
  options: AutoCompleteOption[]
  value?: string
  onChange: (value: string) => void
  placeholder?: string
  allowCustom?: boolean
  multiple?: boolean
  className?: string
  disabled?: boolean
}

export function AutoComplete({
  options,
  value = '',
  onChange,
  placeholder = 'Type to search...',
  allowCustom = false,
  multiple = false,
  className = '',
  disabled = false
}: AutoCompleteProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedValues, setSelectedValues] = useState<string[]>(
    multiple ? (value ? value.split(',') : []) : []
  )
  const [highlightedIndex, setHighlightedIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const listRef = useRef<HTMLUListElement>(null)

  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    option.value.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (option.description && option.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  useEffect(() => {
    if (!multiple) {
      const selectedOption = options.find(opt => opt.value === value)
      setSearchTerm(selectedOption ? selectedOption.label : value)
    }
  }, [value, options, multiple])

  useEffect(() => {
    if (isOpen && highlightedIndex >= 0 && listRef.current) {
      const highlightedElement = listRef.current.children[highlightedIndex] as HTMLElement
      if (highlightedElement) {
        highlightedElement.scrollIntoView({ block: 'nearest' })
      }
    }
  }, [highlightedIndex, isOpen])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setSearchTerm(newValue)
    setIsOpen(true)
    setHighlightedIndex(-1)

    if (!multiple && allowCustom) {
      onChange(newValue)
    }
  }

  const handleOptionSelect = (option: AutoCompleteOption) => {
    if (multiple) {
      const newSelectedValues = selectedValues.includes(option.value)
        ? selectedValues.filter(v => v !== option.value)
        : [...selectedValues, option.value]
      
      setSelectedValues(newSelectedValues)
      onChange(newSelectedValues.join(','))
      setSearchTerm('')
    } else {
      setSearchTerm(option.label)
      onChange(option.value)
      setIsOpen(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'ArrowDown' || e.key === 'Enter') {
        setIsOpen(true)
        return
      }
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setHighlightedIndex(prev => 
          prev < filteredOptions.length - 1 ? prev + 1 : 0
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : filteredOptions.length - 1
        )
        break
      case 'Enter':
        e.preventDefault()
        if (highlightedIndex >= 0 && filteredOptions[highlightedIndex]) {
          handleOptionSelect(filteredOptions[highlightedIndex])
        } else if (allowCustom && searchTerm.trim()) {
          if (multiple) {
            const newSelectedValues = [...selectedValues, searchTerm.trim()]
            setSelectedValues(newSelectedValues)
            onChange(newSelectedValues.join(','))
            setSearchTerm('')
          } else {
            onChange(searchTerm.trim())
            setIsOpen(false)
          }
        }
        break
      case 'Escape':
        setIsOpen(false)
        setHighlightedIndex(-1)
        break
      case 'Tab':
        setIsOpen(false)
        break
    }
  }

  const removeSelectedValue = (valueToRemove: string) => {
    const newSelectedValues = selectedValues.filter(v => v !== valueToRemove)
    setSelectedValues(newSelectedValues)
    onChange(newSelectedValues.join(','))
  }

  const getOptionLabel = (value: string) => {
    const option = options.find(opt => opt.value === value)
    return option ? option.label : value
  }

  return (
    <div className={`relative ${className}`}>
      {/* Selected Values (Multiple) */}
      {multiple && selectedValues.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {selectedValues.map((selectedValue) => (
            <span
              key={selectedValue}
              className="inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm"
            >
              {getOptionLabel(selectedValue)}
              <button
                onClick={() => removeSelectedValue(selectedValue)}
                className="ml-1 hover:text-blue-600 dark:hover:text-blue-200"
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      {/* Input */}
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          onBlur={() => setTimeout(() => setIsOpen(false), 200)}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full px-3 py-2 pr-8 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
        />
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          disabled={disabled}
        >
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>
      </div>

      {/* Options Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto">
          <ul ref={listRef} className="py-1">
            {filteredOptions.length === 0 ? (
              <li className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                {allowCustom ? 'No matches found. Press Enter to add custom value.' : 'No options found'}
              </li>
            ) : (
              filteredOptions.map((option, index) => (
                <li
                  key={option.value}
                  onClick={() => handleOptionSelect(option)}
                  className={`px-3 py-2 cursor-pointer flex items-center justify-between ${
                    index === highlightedIndex
                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
                      : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-white'
                  }`}
                >
                  <div className="flex-1">
                    <div className="font-medium">{option.label}</div>
                    {option.description && (
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {option.description}
                      </div>
                    )}
                    {option.category && (
                      <div className="text-xs text-gray-400 dark:text-gray-500">
                        {option.category}
                      </div>
                    )}
                  </div>
                  {multiple && selectedValues.includes(option.value) && (
                    <Check className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  )}
                </li>
              ))
            )}
            {allowCustom && searchTerm.trim() && !filteredOptions.some(opt => opt.value === searchTerm.trim()) && (
              <li
                onClick={() => {
                  if (multiple) {
                    const newSelectedValues = [...selectedValues, searchTerm.trim()]
                    setSelectedValues(newSelectedValues)
                    onChange(newSelectedValues.join(','))
                    setSearchTerm('')
                  } else {
                    onChange(searchTerm.trim())
                    setIsOpen(false)
                  }
                }}
                className="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-white border-t border-gray-200 dark:border-gray-600"
              >
                <div className="font-medium">Add "{searchTerm.trim()}"</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Create new option
                </div>
              </li>
            )}
          </ul>
        </div>
      )}
    </div>
  )
}

// Predefined option sets for common use cases
export const commonOptions = {
  categories: [
    { value: 'natural-healing', label: 'Natural Healing', description: 'Traditional and natural healing methods' },
    { value: 'islamic-education', label: 'Islamic Education', description: 'Religious and spiritual learning' },
    { value: 'plant-medicine', label: 'Plant Medicine', description: 'Herbal and botanical remedies' },
    { value: 'mental-health', label: 'Mental Health', description: 'Psychological and emotional wellbeing' },
    { value: 'physical-health', label: 'Physical Health', description: 'Body health and fitness' },
    { value: 'spiritual-growth', label: 'Spiritual Growth', description: 'Spiritual development and practices' }
  ],
  
  tags: [
    { value: 'cupping', label: 'Cupping Therapy', category: 'Natural Healing' },
    { value: 'meditation', label: 'Meditation', category: 'Mental Health' },
    { value: 'herbs', label: 'Herbal Medicine', category: 'Plant Medicine' },
    { value: 'prayer', label: 'Prayer & Dhikr', category: 'Spiritual Growth' },
    { value: 'nutrition', label: 'Nutrition', category: 'Physical Health' },
    { value: 'exercise', label: 'Exercise', category: 'Physical Health' },
    { value: 'mindfulness', label: 'Mindfulness', category: 'Mental Health' },
    { value: 'quran', label: 'Quran Study', category: 'Islamic Education' }
  ],

  languages: [
    { value: 'en', label: 'English', description: 'English language' },
    { value: 'ar', label: 'العربية', description: 'Arabic language' },
    { value: 'fr', label: 'Français', description: 'French language' },
    { value: 'es', label: 'Español', description: 'Spanish language' },
    { value: 'ur', label: 'اردو', description: 'Urdu language' },
    { value: 'sv', label: 'Svenska', description: 'Swedish language' }
  ],

  countries: [
    { value: 'us', label: 'United States', description: 'United States of America' },
    { value: 'uk', label: 'United Kingdom', description: 'United Kingdom' },
    { value: 'ca', label: 'Canada', description: 'Canada' },
    { value: 'au', label: 'Australia', description: 'Australia' },
    { value: 'de', label: 'Germany', description: 'Germany' },
    { value: 'fr', label: 'France', description: 'France' },
    { value: 'sa', label: 'Saudi Arabia', description: 'Saudi Arabia' },
    { value: 'ae', label: 'UAE', description: 'United Arab Emirates' },
    { value: 'pk', label: 'Pakistan', description: 'Pakistan' },
    { value: 'se', label: 'Sweden', description: 'Sweden' }
  ]
}
