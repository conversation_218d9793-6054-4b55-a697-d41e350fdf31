import Link from 'next/link'
import { Home, ArrowLeft, Search, MessageCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-8xl font-bold text-blue-600 dark:text-blue-400 mb-4">
            404
          </div>
          <div className="w-24 h-1 bg-blue-600 dark:bg-blue-400 mx-auto rounded-full"></div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Page Not Found
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            The page you're looking for doesn't exist or has been moved. 
            Let's get you back on the path to wisdom and learning.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <Link href="/">
            <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
              <Home className="h-4 w-4 mr-2" />
              Return Home
            </Button>
          </Link>

          <div className="grid grid-cols-2 gap-3">
            <Link href="/forum">
              <Button variant="outline" className="w-full">
                <MessageCircle className="h-4 w-4 mr-2" />
                Forum
              </Button>
            </Link>
            
            <Link href="/community">
              <Button variant="outline" className="w-full">
                <Search className="h-4 w-4 mr-2" />
                Learning
              </Button>
            </Link>
          </div>
        </div>

        {/* Helpful Links */}
        <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Popular pages:
          </p>
          <div className="flex flex-wrap justify-center gap-2 text-sm">
            <Link 
              href="/cupping" 
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              Cupping Therapy
            </Link>
            <span className="text-gray-300">•</span>
            <Link 
              href="/heart-mind" 
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              Heart & Mind
            </Link>
            <span className="text-gray-300">•</span>
            <Link 
              href="/patience" 
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              Patience
            </Link>
            <span className="text-gray-300">•</span>
            <Link 
              href="/live" 
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              Live Stream
            </Link>
          </div>
        </div>

        {/* Spiritual Quote */}
        <div className="mt-8 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <p className="text-sm text-gray-600 dark:text-gray-300 italic">
            "And whoever relies upon Allah - then He is sufficient for him. 
            Indeed, Allah will accomplish His purpose."
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            Quran 65:3
          </p>
        </div>
      </div>
    </div>
  )
}
