import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getCachedSettings } from '@/lib/cms-service';
import { CategoryContentLoader } from '@/components/cms-content-loader';
import { ScrollToTopWithProgress } from '@/components/scroll-to-top';
import { Folder, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface CategoryPageProps {
  params: {
    slug: string;
  };
  searchParams: {
    lang?: string;
  };
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const settings = await getCachedSettings(true);
  const siteTitle = settings.site_title || 'Light Upon Light';
  
  // Format category name from slug
  const categoryName = params.slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return {
    title: `${categoryName} | ${siteTitle}`,
    description: `Browse all content in the ${categoryName} category. Discover articles, guides, and resources related to ${categoryName.toLowerCase()}.`,
    keywords: `${categoryName.toLowerCase()}, category, content, articles, education`,
    openGraph: {
      title: `${categoryName} | ${siteTitle}`,
      description: `Browse all content in the ${categoryName} category.`,
      type: 'website',
    },
  };
}

export default function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const language = searchParams.lang || 'en';
  
  // Format category name from slug for display
  const categoryName = params.slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-6">
            <Link href="/content">
              <Button variant="outline" size="sm" className="mr-4 text-white border-white hover:bg-white hover:text-green-600">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to All Content
              </Button>
            </Link>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-6">
              <Folder className="h-12 w-12 text-white mr-4" />
              <h1 className="text-4xl md:text-5xl font-bold text-white">
                {categoryName}
              </h1>
            </div>
            <p className="text-xl text-green-100 max-w-3xl mx-auto">
              Explore all content in the {categoryName} category. Find articles, guides, 
              and resources specifically related to {categoryName.toLowerCase()}.
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-400">
            <Link href="/" className="hover:text-white transition-colors">
              Home
            </Link>
            <span>/</span>
            <Link href="/content" className="hover:text-white transition-colors">
              Content
            </Link>
            <span>/</span>
            <span className="text-white">{categoryName}</span>
          </nav>
        </div>
      </div>

      {/* Content Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <CategoryContentLoader
          category={params.slug}
          showSearch={true}
          showFilters={true}
          showPagination={true}
          layout="grid"
          limit={12}
          className="space-y-8"
        />
      </div>

      {/* Enhanced scroll to top with progress */}
      <ScrollToTopWithProgress threshold={200} />
    </div>
  );
}
