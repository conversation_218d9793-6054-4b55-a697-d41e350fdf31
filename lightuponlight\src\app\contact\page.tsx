import { Mail, MessageCircle, Phone, MapPin, Clock, Send, User, Heart, Globe } from 'lucide-react'

const contactMethods = [
  {
    method: 'Email',
    value: '<EMAIL>',
    description: 'Primary contact for all inquiries, support, and administrative matters',
    responseTime: '24-48 hours',
    icon: Mail,
    color: 'text-blue-600'
  },
  {
    method: 'Community Forum',
    value: 'Discussion Forum',
    description: 'Join our community discussions for learning support and peer interaction',
    responseTime: 'Real-time',
    icon: MessageCircle,
    color: 'text-green-600'
  },
  {
    method: 'Live Learning Sessions',
    value: 'Interactive Sessions',
    description: 'Participate in live educational sessions with direct instructor interaction',
    responseTime: 'Scheduled times',
    icon: Heart,
    color: 'text-red-600'
  }
]

const inquiryTypes = [
  {
    type: 'General Information',
    description: 'Questions about our platform, services, and educational content',
    examples: ['Platform features', 'Learning paths', 'Content availability', 'Account setup']
  },
  {
    type: 'Technical Support',
    description: 'Help with technical issues, account problems, or platform functionality',
    examples: ['Login issues', 'Video playback', 'Mobile app problems', 'Browser compatibility']
  },
  {
    type: 'Educational Guidance',
    description: 'Academic support, learning recommendations, and educational consultation',
    examples: ['Learning path selection', 'Study guidance', 'Progress tracking', 'Certification']
  },
  {
    type: 'Community & Moderation',
    description: 'Community guidelines, content moderation, and forum-related matters',
    examples: ['Community rules', 'Content reporting', 'Moderation appeals', 'User conduct']
  },
  {
    type: 'Privacy & Legal',
    description: 'Data protection, privacy rights, legal compliance, and policy questions',
    examples: ['GDPR requests', 'Data deletion', 'Privacy concerns', 'Terms clarification']
  },
  {
    type: 'Partnerships & Collaboration',
    description: 'Business partnerships, content collaboration, and institutional inquiries',
    examples: ['Educational partnerships', 'Content licensing', 'Bulk access', 'Custom solutions']
  }
]

const languages = [
  { name: 'English', native: 'English', flag: '🇺🇸' },
  { name: 'Arabic', native: 'العربية', flag: '🇸🇦' },
  { name: 'French', native: 'Français', flag: '🇫🇷' },
  { name: 'Spanish', native: 'Español', flag: '🇪🇸' },
  { name: 'Urdu', native: 'اردو', flag: '🇵🇰' },
  { name: 'Swedish', native: 'Svenska', flag: '🇸🇪' }
]

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Mail className="h-16 w-16 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Contact Us
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              We're here to help you on your learning journey. Reach out to us for support, 
              guidance, or any questions about Light Upon Light platform and services.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Contact Methods */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            How to Reach Us
          </h2>
          <div className="space-y-6">
            {contactMethods.map((contact, index) => {
              const Icon = contact.icon
              return (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                  <div className="flex items-start space-x-4">
                    <Icon className={`h-6 w-6 ${contact.color} mt-1`} />
                    <div className="flex-1">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {contact.method}
                        </h3>
                        <span className="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                          {contact.responseTime}
                        </span>
                      </div>
                      <p className="text-blue-600 dark:text-blue-400 font-medium mb-2">
                        {contact.value}
                      </p>
                      <p className="text-gray-700 dark:text-gray-300">
                        {contact.description}
                      </p>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Inquiry Types */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Types of Inquiries We Handle
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {inquiryTypes.map((inquiry, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {inquiry.type}
                </h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  {inquiry.description}
                </p>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Examples:</h4>
                  <ul className="space-y-1">
                    {inquiry.examples.map((example, exampleIndex) => (
                      <li key={exampleIndex} className="text-sm text-gray-600 dark:text-gray-300">
                        • {example}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Form */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Send Us a Message
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Your Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Enter your full name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Inquiry Type
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                  <option>Select inquiry type</option>
                  {inquiryTypes.map((type, index) => (
                    <option key={index} value={type.type}>{type.type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Subject
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Brief description of your inquiry"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Message
                </label>
                <textarea
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Please provide detailed information about your inquiry..."
                ></textarea>
              </div>

              <button
                type="submit"
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
              >
                <Send className="h-5 w-5" />
                <span>Send Message</span>
              </button>
            </form>
          </div>
        </div>

        {/* Language Support */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Multi-Language Support
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <p className="text-gray-700 dark:text-gray-300 mb-6 text-center">
              We provide support in multiple languages to serve our diverse global community:
            </p>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {languages.map((language, index) => (
                <div key={index} className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="text-2xl mb-2">{language.flag}</div>
                  <div className="font-medium text-gray-900 dark:text-white">{language.name}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">{language.native}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Response Time */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Clock className="h-6 w-6 text-blue-600 mr-3" />
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-300">
              Response Time Commitment
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="font-medium text-blue-800 dark:text-blue-300">General Inquiries</div>
              <div className="text-blue-700 dark:text-blue-300">24-48 hours</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-blue-800 dark:text-blue-300">Technical Support</div>
              <div className="text-blue-700 dark:text-blue-300">12-24 hours</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-blue-800 dark:text-blue-300">Urgent Issues</div>
              <div className="text-blue-700 dark:text-blue-300">Within 6 hours</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
