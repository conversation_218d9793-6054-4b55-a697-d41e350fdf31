import { Metada<PERSON> } from 'next';
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { generatePageMetadata, pagesSEOData } from '@/lib/seo-metadata';

// Generate metadata for SEO
export const metadata: Metadata = generatePageMetadata(pagesSEOData.home);
import {
  Heart,
  Leaf,
  Mountain,
  Sun,
  BookOpen,
  Users,
  Video,
  MessageCircle,
  Star,
  Waves,
  Wind,
  Flame,
  ExternalLink,
} from "lucide-react";
import { FeaturedContentLoader, RecentContentLoader } from "@/components/cms-content-loader";
import { getCachedSettings, getCachedFeaturedContent } from "@/lib/cms-service";
import { ScrollToTopWithProgress } from "@/components/scroll-to-top";

const features = [
  {
    name: "Cupping Therapy",
    description:
      "Learn about cupping therapy - a natural way to help your body heal, support blood flow, and feel better.",
    icon: Heart,
    href: "/cupping",
    color: "text-red-600",
    benefits: [
      "Pain relief",
      "Circulation boost",
      "Stress reduction",
      "Natural healing",
    ],
  },
  {
    name: "Heart & Mind Development",
    description:
      "Gentle integration of heart wisdom with natural intelligence for peaceful understanding and sound development.",
    icon: Heart,
    href: "/heart-mind",
    color: "text-pink-600",
    benefits: [
      "Heart wisdom",
      "Natural intelligence",
      "Peaceful understanding",
      "Sound development",
    ],
  },
  {
    name: "Natural Healing & Plants",
    description:
      "Learn about healing plants and how they can help your body feel better naturally.",
    icon: Leaf,
    href: "/plants",
    color: "text-green-600",
    benefits: [
      "Natural remedies",
      "Scientific knowledge",
      "Health optimization",
      "Sustainable healing",
    ],
  },
  {
    name: "Honey & Natural Foods",
    description:
      "Discover how honey, onions, and other natural foods can help keep you healthy and strong.",
    icon: Mountain,
    href: "/honey",
    color: "text-amber-600",
    benefits: [
      "Nutritional wisdom",
      "Natural healing",
      "Natural energy",
      "Body support",
    ],
  },
  {
    name: "Nature & Elements",
    description:
      "Scientific principles of natural elements and their influence on human health and development.",
    icon: Sun,
    href: "/nature-elements",
    color: "text-yellow-600",
    benefits: [
      "Natural balance",
      "Natural harmony",
      "Natural health",
      "Environmental wisdom",
    ],
  },
  {
    name: "Heavens & Earth",
    description:
      "Natural wisdom and universal laws that provide gentle guidance for human growth and peaceful living.",
    icon: Star,
    href: "/heavens-earth",
    color: "text-purple-600",
    benefits: [
      "Natural perspective",
      "Universal principles",
      "Natural growth",
      "Peaceful development",
    ],
  },
  {
    name: "Quranic Logic",
    description:
      "Verses from the Quran presented with scientific insights, logical reasoning, and universal benefits.",
    icon: BookOpen,
    href: "/quran",
    color: "text-blue-600",
    benefits: [
      "Natural thinking",
      "Simple insights",
      "Universal wisdom",
      "Natural development",
    ],
  },
  {
    name: "99 Divine Attributes",
    description:
      "Perfect qualities and principles that serve as frameworks for character development and human excellence.",
    icon: BookOpen,
    href: "/names-of-god",
    color: "text-indigo-600",
    benefits: [
      "Character development",
      "Excellence principles",
      "Personal growth",
      "Ethical living",
    ],
  },
  {
    name: "Ancient Wisdom",
    description:
      "Timeless principles from various traditions presented through logical frameworks and practical applications.",
    icon: BookOpen,
    href: "/wisdom",
    color: "text-cyan-600",
    benefits: [
      "Universal principles",
      "Practical wisdom",
      "Cultural insights",
      "Timeless knowledge",
    ],
  },
  {
    name: "Logic & Intelligence",
    description:
      "Nurture natural thinking, gentle understanding, and pure intelligence through peaceful reflection and contentment.",
    icon: BookOpen,
    href: "/logic",
    color: "text-violet-600",
    benefits: [
      "Natural thinking",
      "Peaceful reflection",
      "Pure intelligence",
      "Gentle understanding",
    ],
  },
  {
    name: "Community Learning",
    description:
      "Join a diverse learning community with structured paths, achievements, and collaborative growth opportunities.",
    icon: Users,
    href: "/community",
    color: "text-emerald-600",
    benefits: [
      "Collaborative learning",
      "Structured paths",
      "Achievement system",
      "Knowledge sharing",
    ],
  },
  {
    name: "Live Learning",
    description:
      "Interactive educational sessions with camera/microphone support for real-time learning and engagement.",
    icon: Video,
    href: "/live",
    color: "text-orange-600",
    benefits: [
      "Interactive learning",
      "Real-time engagement",
      "Expert guidance",
      "Community interaction",
    ],
  },
];

const naturalElements = [
  {
    name: "Sun & Stars",
    icon: Sun,
    description: "Celestial wisdom and guidance",
  },
  {
    name: "Mountains",
    icon: Mountain,
    description: "Strength and steadfastness",
  },
  { name: "Ocean", icon: Waves, description: "Depth and purification" },
  { name: "Wind", icon: Wind, description: "Spirit and divine breath" },
  { name: "Fire", icon: Flame, description: "Purification and transformation" },
];

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/20 dark:via-indigo-900/20 dark:to-purple-900/20 py-20">
        <div className="absolute inset-0 bg-[url('/pattern.svg')] opacity-10"></div>

        {/* Additional Website Link - Top Right Corner */}
        <div className="absolute top-4 right-4 z-10">
          <a
            href="https://light-and-light.lovable.app/"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg shadow-sm hover:bg-white dark:hover:bg-gray-800 hover:shadow-md transition-all duration-200 border border-blue-200 dark:border-blue-700"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            <span>Light & Light</span>
          </a>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex justify-center mb-8">
            <Sun className="h-20 w-20 text-yellow-500 animate-pulse" />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Light Upon Light
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-6 max-w-3xl mx-auto">
            A place to learn wisdom, natural healing, and grow your mind through
            simple thinking and nature's lessons.
          </p>

          {/* Important Spiritual Reminder */}
          <div className="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-lg p-6 mb-8 max-w-4xl mx-auto border border-amber-200 dark:border-amber-800">
            <div className="flex items-center justify-center mb-4">
              <Heart className="h-6 w-6 text-amber-600 mr-2" />

              <span className="text-lg font-semibold text-amber-800 dark:text-amber-300">
                Important Reminder
              </span>
            </div>
            <p className="text-base md:text-lg text-amber-800 dark:text-amber-200 leading-relaxed text-center">
              <strong>Note:</strong> Death is not curable. Kindly remember to
              care for your inner safety and awaken to the return of inner life
              — life filled with consciousness and understanding. Journey
              towards straight guidance and home of peace and light. Explore
              more pages on{" "}
              <Link
                href="/heart-mind"
                className="underline hover:text-amber-900 dark:hover:text-amber-100"
              >
                heart development
              </Link>
              , in{" "}
              <Link
                href="/cupping"
                className="underline hover:text-amber-900 dark:hover:text-amber-100"
              >
                cupping
              </Link>
              , and in pages{" "}
              <Link
                href="/privacy"
                className="underline hover:text-amber-900 dark:hover:text-amber-100"
              >
                legal disclaimers
              </Link>
              .
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/community">
              <Button size="lg" className="text-lg px-8 py-3">
                Join Our Community
              </Button>
            </Link>
            <Link href="/quran">
              <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                Explore Wisdom
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Wisdom Quote Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="verse-container text-center">
            <div className="text-2xl md:text-3xl mb-4 text-gray-800 dark:text-gray-200 font-serif italic">
              "The light of wisdom illuminates both the heavens and the earth"
            </div>
            <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-2">
              Old wisdom teaches us that learning and understanding make all
              parts of life clearer.
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              - Universal Wisdom Tradition
            </p>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Explore Our Learning Paths
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Find wisdom through different learning paths made to help your
              heart, mind, and body feel good.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature) => {
              const Icon = feature.icon;
              return (
                <Link key={feature.name} href={feature.href}>
                  <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 p-6 h-full hover:scale-105 transform">
                    <div className="flex items-center mb-4">
                      <Icon className={`h-8 w-8 ${feature.color} mr-3`} />

                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {feature.name}
                      </h3>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {feature.description}
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {feature.benefits.map((benefit, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs"
                        >
                          {benefit}
                        </span>
                      ))}
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Natural Elements Section */}
      <section className="py-20 nature-gradient">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Nature's Divine Signs
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Learn from the natural world and discover the divine wisdom
              embedded in creation.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {naturalElements.map((element) => {
              const Icon = element.icon;
              return (
                <div
                  key={element.name}
                  className="text-center p-6 bg-white/80 dark:bg-gray-900/80 rounded-lg backdrop-blur-sm"
                >
                  <Icon className="h-12 w-12 mx-auto mb-4 text-blue-600" />

                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {element.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {element.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Featured CMS Content */}
      <section className="py-16 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              Featured Content
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Discover our latest articles, educational content, and wisdom teachings
            </p>
          </div>

          <FeaturedContentLoader
            limit={6}
            layout="grid"
            showPagination={false}
            className="mb-8"
          />

          <div className="text-center">
            <Link href="/content">
              <Button variant="outline" size="lg">
                <BookOpen className="h-5 w-5 mr-2" />
                View All Content
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Recent Content */}
      <section className="py-16 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              Latest Updates
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Stay updated with our newest content and teachings
            </p>
          </div>

          <RecentContentLoader
            limit={4}
            layout="card"
            showPagination={false}
          />
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-blue-600 dark:bg-blue-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Begin Your Journey of Enlightenment
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of learners on a path of spiritual growth, natural
            healing, and divine wisdom.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/signup">
              <Button
                size="lg"
                variant="secondary"
                className="text-lg px-8 py-3"
              >
                Start Learning Today
              </Button>
            </Link>
            <Link href="/live">
              <Button
                size="lg"
                variant="outline"
                className="text-lg px-8 py-3 text-white border-white hover:bg-white hover:text-blue-600"
              >
                Join Live Sessions
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Enhanced scroll to top with progress */}
      <ScrollToTopWithProgress threshold={200} />
    </div>
  );
}
