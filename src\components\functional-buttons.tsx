"use client";

import { useState } from "react";
import { useAuth } from "./providers";
import { useData } from "./data-provider";
import { useLanguage } from "./language-provider";
import { Button } from "./ui/button";
import {
  Download,
  Share2,
  Bookmark,
  Heart,
  ThumbsDown,
  MessageCircle,
  Eye,
  Calendar,
  Bell,
  Copy,
  ExternalLink,
  Save,
  Edit,
  Trash2,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Settings,
  Filter,
  Search,
  RefreshCw,
  Upload,
  FileText,
  Image,
  Video,
  Mic,
  Camera,
} from "lucide-react";

interface FunctionalButtonProps {
  type:
    | "like"
    | "dislike"
    | "bookmark"
    | "share"
    | "download"
    | "view"
    | "edit"
    | "delete"
    | "save"
    | "copy"
    | "reminder"
    | "play"
    | "pause"
    | "mute"
    | "unmute"
    | "refresh"
    | "upload"
    | "search"
    | "filter";
  itemId?: string;
  itemType?: "post" | "stream" | "event" | "achievement";
  count?: number;
  isActive?: boolean;
  disabled?: boolean;
  size?: "sm" | "default" | "lg";
  variant?: "default" | "outline" | "ghost";
  onClick?: () => void;
  className?: string;
  children?: React.ReactNode;
}

export function FunctionalButton({
  type,
  itemId,
  itemType = "post",
  count,
  isActive = false,
  disabled = false,
  size = "default",
  variant = "ghost",
  onClick,
  className = "",
  children,
}: FunctionalButtonProps) {
  const { user } = useAuth();
  const { t } = useLanguage();
  const { likePost, dislikePost } = useData();
  const [isLoading, setIsLoading] = useState(false);
  const [localCount, setLocalCount] = useState(count || 0);
  const [localActive, setLocalActive] = useState(isActive);

  const handleClick = async () => {
    if (disabled || isLoading) return;

    setIsLoading(true);

    try {
      switch (type) {
        case "like":
          if (!user) {
            alert("Please sign in to like posts");
            return;
          }
          if (itemId) {
            likePost(itemId);
            setLocalActive(!localActive);
            setLocalCount((prev) => (localActive ? prev - 1 : prev + 1));
          }
          break;

        case "dislike":
          if (!user) {
            alert("Please sign in to dislike posts");
            return;
          }
          if (itemId) {
            dislikePost(itemId);
            setLocalActive(!localActive);
            setLocalCount((prev) => (localActive ? prev - 1 : prev + 1));
          }
          break;

        case "bookmark":
          if (!user) {
            alert("Please sign in to bookmark items");
            return;
          }
          // Simulate bookmark functionality
          setLocalActive(!localActive);
          alert(localActive ? "Removed from bookmarks" : "Added to bookmarks");
          break;

        case "share":
          if (navigator.share) {
            await navigator.share({
              title: "Light Upon Light",
              text: "Check out this content",
              url: window.location.href,
            });
          } else {
            // Fallback to copy URL
            await navigator.clipboard.writeText(window.location.href);
            alert("Link copied to clipboard!");
          }
          break;

        case "download":
          // Simulate download functionality
          alert("Download started...");
          break;

        case "copy":
          await navigator.clipboard.writeText(window.location.href);
          alert("Copied to clipboard!");
          break;

        case "reminder":
          if (!user) {
            alert("Please sign in to set reminders");
            return;
          }
          setLocalActive(!localActive);
          alert(localActive ? "Reminder removed" : "Reminder set");
          break;

        case "refresh":
          window.location.reload();
          break;

        case "upload":
          // Trigger file upload
          const input = document.createElement("input");
          input.type = "file";
          input.accept = "*/*";
          input.onchange = (e) => {
            const file = (e.target as HTMLInputElement).files?.[0];
            if (file) {
              alert(`Selected file: ${file.name}`);
            }
          };
          input.click();
          break;

        default:
          if (onClick) {
            onClick();
          }
          break;
      }
    } catch (error) {
      console.error("Button action failed:", error);
      alert("Action failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const getIcon = () => {
    switch (type) {
      case "like":
        return (
          <Heart
            className={`h-4 w-4 ${localActive ? "fill-current text-red-500" : ""}`}
          />
        );

      case "dislike":
        return (
          <ThumbsDown
            className={`h-4 w-4 ${localActive ? "fill-current text-blue-500" : ""}`}
          />
        );

      case "bookmark":
        return (
          <Bookmark
            className={`h-4 w-4 ${localActive ? "fill-current text-yellow-500" : ""}`}
          />
        );

      case "share":
        return <Share2 className="h-4 w-4" />;
      case "download":
        return <Download className="h-4 w-4" />;
      case "view":
        return <Eye className="h-4 w-4" />;
      case "edit":
        return <Edit className="h-4 w-4" />;
      case "delete":
        return <Trash2 className="h-4 w-4" />;
      case "save":
        return <Save className="h-4 w-4" />;
      case "copy":
        return <Copy className="h-4 w-4" />;
      case "reminder":
        return (
          <Bell
            className={`h-4 w-4 ${localActive ? "fill-current text-blue-500" : ""}`}
          />
        );

      case "play":
        return <Play className="h-4 w-4" />;
      case "pause":
        return <Pause className="h-4 w-4" />;
      case "mute":
        return <VolumeX className="h-4 w-4" />;
      case "unmute":
        return <Volume2 className="h-4 w-4" />;
      case "refresh":
        return (
          <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
        );

      case "upload":
        return <Upload className="h-4 w-4" />;
      case "search":
        return <Search className="h-4 w-4" />;
      case "filter":
        return <Filter className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const getLabel = () => {
    switch (type) {
      case "like":
        return t("forum.like") || "Like";
      case "dislike":
        return t("forum.dislike") || "Dislike";
      case "bookmark":
        return localActive ? "Bookmarked" : "Bookmark";
      case "share":
        return t("common.share") || "Share";
      case "download":
        return t("common.download") || "Download";
      case "view":
        return t("common.view") || "View";
      case "edit":
        return t("common.edit") || "Edit";
      case "delete":
        return t("common.delete") || "Delete";
      case "save":
        return t("common.save") || "Save";
      case "copy":
        return t("common.copy") || "Copy";
      case "reminder":
        return localActive ? "Reminder Set" : "Set Reminder";
      case "play":
        return "Play";
      case "pause":
        return "Pause";
      case "mute":
        return "Mute";
      case "unmute":
        return "Unmute";
      case "refresh":
        return t("common.refresh") || "Refresh";
      case "upload":
        return t("common.upload") || "Upload";
      case "search":
        return t("common.search") || "Search";
      case "filter":
        return t("common.filter") || "Filter";
      default:
        return "";
    }
  };

  const getVariantClass = () => {
    if (localActive) {
      switch (type) {
        case "like":
          return "text-red-500 hover:text-red-600";
        case "dislike":
          return "text-blue-500 hover:text-blue-600";
        case "bookmark":
          return "text-yellow-500 hover:text-yellow-600";
        case "reminder":
          return "text-blue-500 hover:text-blue-600";
        default:
          return "";
      }
    }
    return "";
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={disabled || isLoading}
      className={`${getVariantClass()} ${className}`}
      title={getLabel()}
    >
      {getIcon()}
      {children || (
        <span className="ml-1">
          {getLabel()}
          {localCount > 0 && ` (${localCount})`}
        </span>
      )}
    </Button>
  );
}

// Specialized button components
export function LikeButton({
  itemId,
  count,
  isLiked,
  ...props
}: {
  itemId: string;
  count?: number;
  isLiked?: boolean;
} & Partial<FunctionalButtonProps>) {
  return (
    <FunctionalButton
      type="like"
      itemId={itemId}
      count={count}
      isActive={isLiked}
      {...props}
    />
  );
}

export function ShareButton({
  url,
  title,
  ...props
}: { url?: string; title?: string } & Partial<FunctionalButtonProps>) {
  return (
    <FunctionalButton
      type="share"
      onClick={() => {
        if (navigator.share) {
          navigator.share({
            title: title || "Light Upon Light",
            url: url || window.location.href,
          });
        } else {
          navigator.clipboard.writeText(url || window.location.href);
          alert("Link copied to clipboard!");
        }
      }}
      {...props}
    />
  );
}

export function DownloadButton({
  url,
  filename,
  ...props
}: { url: string; filename?: string } & Partial<FunctionalButtonProps>) {
  return (
    <FunctionalButton
      type="download"
      onClick={() => {
        const link = document.createElement("a");
        link.href = url;
        link.download = filename || "download";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }}
      {...props}
    />
  );
}

export function BookmarkButton({
  itemId,
  isBookmarked,
  ...props
}: {
  itemId: string;
  isBookmarked?: boolean;
} & Partial<FunctionalButtonProps>) {
  return (
    <FunctionalButton
      type="bookmark"
      itemId={itemId}
      isActive={isBookmarked}
      {...props}
    />
  );
}

export function ReminderButton({
  eventId,
  isSet,
  ...props
}: { eventId: string; isSet?: boolean } & Partial<FunctionalButtonProps>) {
  return (
    <FunctionalButton
      type="reminder"
      itemId={eventId}
      isActive={isSet}
      {...props}
    />
  );
}

// Media control buttons
export function PlayPauseButton({
  isPlaying,
  onToggle,
  ...props
}: {
  isPlaying: boolean;
  onToggle: () => void;
} & Partial<FunctionalButtonProps>) {
  return (
    <FunctionalButton
      type={isPlaying ? "pause" : "play"}
      onClick={onToggle}
      {...props}
    />
  );
}

export function MuteButton({
  isMuted,
  onToggle,
  ...props
}: {
  isMuted: boolean;
  onToggle: () => void;
} & Partial<FunctionalButtonProps>) {
  return (
    <FunctionalButton
      type={isMuted ? "unmute" : "mute"}
      onClick={onToggle}
      {...props}
    />
  );
}

// Action button groups
export function PostActionButtons({
  postId,
  likes,
  dislikes,
  isLiked,
  isDisliked,
  onReply,
}: {
  postId: string;
  likes: number;
  dislikes: number;
  isLiked?: boolean;
  isDisliked?: boolean;
  onReply: () => void;
}) {
  return (
    <div className="flex items-center space-x-2">
      <LikeButton itemId={postId} count={likes} isLiked={isLiked} size="sm" />

      <FunctionalButton
        type="dislike"
        itemId={postId}
        count={dislikes}
        isActive={isDisliked}
        size="sm"
      />

      <Button variant="ghost" size="sm" onClick={onReply}>
        <MessageCircle className="h-4 w-4 mr-1" />
        Reply
      </Button>
      <ShareButton size="sm" />
      <BookmarkButton itemId={postId} size="sm" />
    </div>
  );
}

export function StreamActionButtons({
  streamId,
  isBookmarked,
  downloadUrl,
}: {
  streamId: string;
  isBookmarked?: boolean;
  downloadUrl?: string;
}) {
  return (
    <div className="flex items-center space-x-2">
      <Button variant="outline" size="sm">
        <Play className="h-4 w-4 mr-1" />
        Watch
      </Button>
      <BookmarkButton itemId={streamId} isBookmarked={isBookmarked} size="sm" />

      <ShareButton size="sm" />
      {downloadUrl && <DownloadButton url={downloadUrl} size="sm" />}
    </div>
  );
}

export function EventActionButtons({
  eventId,
  isReminded,
}: {
  eventId: string;
  isReminded?: boolean;
}) {
  return (
    <div className="flex items-center space-x-2">
      <Button variant="default" size="sm">
        <Calendar className="h-4 w-4 mr-1" />
        Join Event
      </Button>
      <ReminderButton eventId={eventId} isSet={isReminded} size="sm" />

      <ShareButton size="sm" />
    </div>
  );
}
