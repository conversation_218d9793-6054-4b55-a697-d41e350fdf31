'use client'

import { create<PERSON>ontext, useContext, useState, useEffect, ReactNode } from 'react'
import { useAuth } from './providers'
import { DatabaseService } from '@/lib/database'

// Types for live data
export interface ForumPost {
  id: string
  title: string
  content: string
  author: string
  authorEmail: string
  category: string
  createdAt: Date
  updatedAt: Date
  likes: number
  dislikes: number
  userLiked?: boolean
  userDisliked?: boolean
  replies: ForumReply[]
  isPinned: boolean
  isLocked: boolean
  views: number
}

export interface ForumReply {
  id: string
  content: string
  author: string
  authorEmail: string
  createdAt: Date
  likes: number
  dislikes: number
  userLiked?: boolean
  userDisliked?: boolean
  parentId?: string
}

export interface ChatMessage {
  id: string
  user: string
  message: string
  timestamp: Date
  isAdmin?: boolean
}

export interface StreamData {
  id: string
  title: string
  description: string
  thumbnail: string
  duration: string
  views: number
  date: Date
  quality: '720p' | '1080p'
  size: string
  isLive: boolean
}

export interface LearningPath {
  id: string
  title: string
  description: string
  duration: string
  modules: number
  enrolled: number
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced'
  instructor: string
  rating: number
  topics: string[]
  icon: any
  color: string
}

export interface Event {
  id: string
  title: string
  description: string
  date: string
  time: string
  duration: string
  instructor: string
  attendees: number
  maxAttendees: number
  type: string
  category: string
  icon: any
  color: string
}

export interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  points: number
  rarity: 'Common' | 'Uncommon' | 'Rare'
  progress: number
  requirement: string
}

interface DataContextType {
  // Forum data
  forumPosts: ForumPost[]
  addForumPost: (post: Omit<ForumPost, 'id' | 'createdAt' | 'updatedAt' | 'likes' | 'dislikes' | 'replies' | 'views'>) => void
  updateForumPost: (id: string, updates: Partial<ForumPost>) => void
  deleteForumPost: (id: string) => void
  addReply: (postId: string, reply: Omit<ForumReply, 'id' | 'createdAt' | 'likes' | 'dislikes'>) => void
  deleteReply: (postId: string, replyId: string) => void
  likePost: (postId: string, isReply?: boolean, replyId?: string) => void
  dislikePost: (postId: string, isReply?: boolean, replyId?: string) => void
  
  // Live stream data
  chatMessages: ChatMessage[]
  addChatMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void
  clearChat: () => void
  
  // Stream archive
  streamArchive: StreamData[]
  addStream: (stream: Omit<StreamData, 'id' | 'views' | 'date'>) => void
  deleteStream: (id: string) => void
  
  // Live data
  viewerCount: number
  setViewerCount: (count: number) => void
  isLive: boolean
  setIsLive: (live: boolean) => void
  
  // Community data
  learningPaths: LearningPath[]
  upcomingEvents: Event[]
  achievements: Achievement[]

  // Statistics
  totalUsers: number
  totalPosts: number
  totalViews: number
  activeUsers: number
  completionRates: { [key: string]: number }
  userProgress: { [key: string]: number }
  refreshStats: () => void
  updateUserProgress: (category: string, progress: number) => void
  loading: boolean
}

const DataContext = createContext<DataContextType | undefined>(undefined)

// Initial data - Start with empty arrays for real user-generated content
const initialForumPosts: ForumPost[] = []

const initialChatMessages: ChatMessage[] = []

const initialStreamArchive: StreamData[] = []

const initialLearningPaths: LearningPath[] = [
  {
    id: '1',
    title: 'Natural Healing Fundamentals',
    description: 'Master the basics of natural healing through cupping, plants, and traditional methods',
    duration: '6 weeks',
    modules: 8,
    enrolled: 234,
    difficulty: 'Beginner',
    instructor: 'Healing Specialist',
    rating: 4.8,
    topics: ['Cupping Therapy', 'Plant Medicine', 'Natural Foods', 'Healing Principles'],
    icon: 'Heart',
    color: 'text-red-600'
  },
  {
    id: '2',
    title: 'Intelligence & Logic Development',
    description: 'Develop logical thinking, emotional intelligence, and reasoning skills',
    duration: '8 weeks',
    modules: 12,
    enrolled: 189,
    difficulty: 'Intermediate',
    instructor: 'Logic Instructor',
    rating: 4.9,
    topics: ['Logical Thinking', 'Emotional Intelligence', 'Problem Solving', 'Decision Making'],
    icon: 'Brain',
    color: 'text-blue-600'
  },
  {
    id: '3',
    title: 'Ancient Wisdom & Modern Science',
    description: 'Explore timeless wisdom through the lens of modern scientific understanding',
    duration: '10 weeks',
    modules: 15,
    enrolled: 156,
    difficulty: 'Advanced',
    instructor: 'Wisdom Teacher',
    rating: 4.7,
    topics: ['Universal Principles', 'Scientific Validation', 'Practical Application', 'Integration'],
    icon: 'Star',
    color: 'text-yellow-600'
  }
]

const initialEvents: Event[] = [
  {
    id: '1',
    title: 'Live Cupping Therapy Demonstration',
    description: 'Watch a professional cupping session and learn proper techniques',
    date: '2024-01-15',
    time: '19:00 UTC',
    duration: '90 minutes',
    instructor: 'Healing Specialist',
    attendees: 45,
    maxAttendees: 100,
    type: 'Live Demo',
    category: 'Health',
    icon: 'Heart',
    color: 'text-red-600'
  },
  {
    id: '2',
    title: 'Logic & Reasoning Workshop',
    description: 'Interactive workshop on developing logical reasoning and thinking skills',
    date: '2024-01-18',
    time: '18:00 UTC',
    duration: '120 minutes',
    instructor: 'Logic Instructor',
    attendees: 67,
    maxAttendees: 80,
    type: 'Workshop',
    category: 'Intelligence',
    icon: 'Brain',
    color: 'text-blue-600'
  }
]

const initialAchievements: Achievement[] = [
  {
    id: '1',
    title: 'First Steps',
    description: 'Complete your first learning module',
    icon: '🌱',
    points: 10,
    rarity: 'Common',
    progress: 0,
    requirement: 'Complete 1 module'
  },
  {
    id: '2',
    title: 'Knowledge Seeker',
    description: 'Complete 5 learning modules across different topics',
    icon: '📚',
    points: 50,
    rarity: 'Uncommon',
    progress: 0,
    requirement: 'Complete 5 modules'
  },
  {
    id: '3',
    title: 'Wisdom Keeper',
    description: 'Complete an entire learning path',
    icon: '🧠',
    points: 100,
    rarity: 'Rare',
    progress: 0,
    requirement: 'Complete 1 learning path'
  }
]

export function DataProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth()
  const [forumPosts, setForumPosts] = useState<ForumPost[]>([])
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [streamArchive, setStreamArchive] = useState<StreamData[]>([])
  const [learningPaths, setLearningPaths] = useState<LearningPath[]>(initialLearningPaths)
  const [upcomingEvents, setUpcomingEvents] = useState<Event[]>(initialEvents)
  const [achievements, setAchievements] = useState<Achievement[]>(initialAchievements)
  const [viewerCount, setViewerCount] = useState(42)
  const [isLive, setIsLive] = useState(false)
  const [totalUsers, setTotalUsers] = useState(2847)
  const [totalPosts, setTotalPosts] = useState(0)
  const [totalViews, setTotalViews] = useState(0)
  const [mounted, setMounted] = useState(false)
  const [loading, setLoading] = useState(true)

  // Load initial data from database
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)

        // Load forum posts
        const posts = await DatabaseService.getForumPosts(1, 20)
        setForumPosts(posts || [])

        // Load learning paths
        const paths = await DatabaseService.getLearningPaths()
        if (paths && paths.length > 0) {
          setLearningPaths(paths)
        }

        // Load upcoming events
        const events = await DatabaseService.getUpcomingEvents()
        if (events && events.length > 0) {
          setUpcomingEvents(events)
        }

        // Load website statistics
        const stats = await DatabaseService.getWebsiteStats()
        setTotalUsers(stats.totalUsers)
        setTotalPosts(stats.totalPosts)
        setTotalViews(stats.totalViews)

      } catch (error) {
        console.error('Error loading data:', error)
        // Fall back to initial data if database fails
      } finally {
        setLoading(false)
        setMounted(true)
      }
    }

    loadData()

    // Simulate live viewer count updates
    const interval = setInterval(() => {
      if (isLive) {
        setViewerCount(prev => prev + Math.floor(Math.random() * 10) - 5)
      }
    }, 5000)

    return () => clearInterval(interval)
  }, [isLive])

  const addForumPost = async (post: Omit<ForumPost, 'id' | 'createdAt' | 'updatedAt' | 'likes' | 'dislikes' | 'replies' | 'views'>) => {
    if (!user) return

    try {
      const newPost = await DatabaseService.createForumPost({
        title: post.title,
        content: post.content,
        author_id: user.id,
        category_id: post.category
      })

      // Refresh forum posts
      const posts = await DatabaseService.getForumPosts(1, 20)
      setForumPosts(posts || [])

    } catch (error) {
      console.error('Error creating forum post:', error)
      // Fall back to local state update
      const newPost: ForumPost = {
        ...post,
        id: Date.now().toString(),
        createdAt: new Date(),
        updatedAt: new Date(),
        likes: 0,
        dislikes: 0,
        replies: [],
        views: 0
      }
      setForumPosts(prev => [newPost, ...prev])
    }
  }

  const updateForumPost = (id: string, updates: Partial<ForumPost>) => {
    setForumPosts(prev => prev.map(post => 
      post.id === id ? { ...post, ...updates, updatedAt: new Date() } : post
    ))
  }

  const deleteForumPost = (id: string) => {
    setForumPosts(prev => prev.filter(post => post.id !== id))
  }

  const addReply = async (postId: string, reply: Omit<ForumReply, 'id' | 'createdAt' | 'likes' | 'dislikes'>) => {
    if (!user) return

    try {
      await DatabaseService.addForumReply({
        post_id: postId,
        content: reply.content,
        author_id: user.id
      })

      // Refresh forum posts to get updated replies
      const posts = await DatabaseService.getForumPosts(1, 20)
      setForumPosts(posts || [])

    } catch (error) {
      console.error('Error adding reply:', error)
      // Fall back to local state update
      const newReply: ForumReply = {
        ...reply,
        id: Date.now().toString(),
        createdAt: new Date(),
        likes: 0,
        dislikes: 0
      }

      setForumPosts(prev => prev.map(post =>
        post.id === postId
          ? { ...post, replies: [...post.replies, newReply] }
          : post
      ))
    }
  }

  const deleteReply = (postId: string, replyId: string) => {
    setForumPosts(prev => prev.map(post => 
      post.id === postId 
        ? { ...post, replies: post.replies.filter(reply => reply.id !== replyId) }
        : post
    ))
  }

  const likePost = async (postId: string, isReply: boolean = false, replyId?: string) => {
    if (!user) return

    try {
      await DatabaseService.toggleLike(user.id, postId, true, replyId)

      // Refresh forum posts to get updated counts
      const posts = await DatabaseService.getForumPosts(1, 20)
      setForumPosts(posts || [])

    } catch (error) {
      console.error('Error liking post:', error)
      // Fall back to local state update
      setForumPosts(prev => prev.map(post => {
        if (post.id === postId) {
          if (isReply && replyId) {
            return {
              ...post,
              replies: post.replies.map(reply =>
                reply.id === replyId
                  ? {
                      ...reply,
                      likes: reply.userLiked ? reply.likes - 1 : reply.likes + 1,
                      dislikes: reply.userDisliked ? reply.dislikes - 1 : reply.dislikes,
                      userLiked: !reply.userLiked,
                      userDisliked: false
                    }
                  : reply
              )
            }
          } else {
            return {
              ...post,
              likes: post.userLiked ? post.likes - 1 : post.likes + 1,
              dislikes: post.userDisliked ? post.dislikes - 1 : post.dislikes,
              userLiked: !post.userLiked,
              userDisliked: false
            }
          }
        }
        return post
      }))
    }
  }

  const dislikePost = (postId: string, isReply: boolean = false, replyId?: string) => {
    if (!user) return

    setForumPosts(prev => prev.map(post => {
      if (post.id === postId) {
        if (isReply && replyId) {
          return {
            ...post,
            replies: post.replies.map(reply =>
              reply.id === replyId
                ? { 
                    ...reply, 
                    dislikes: reply.userDisliked ? reply.dislikes - 1 : reply.dislikes + 1,
                    likes: reply.userLiked ? reply.likes - 1 : reply.likes,
                    userDisliked: !reply.userDisliked,
                    userLiked: false
                  }
                : reply
            )
          }
        } else {
          return {
            ...post,
            dislikes: post.userDisliked ? post.dislikes - 1 : post.dislikes + 1,
            likes: post.userLiked ? post.likes - 1 : post.likes,
            userDisliked: !post.userDisliked,
            userLiked: false
          }
        }
      }
      return post
    }))
  }

  const addChatMessage = async (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    if (!user) return

    try {
      // For now, we'll use local state since live stream might not have a database stream_id yet
      // In a full implementation, you'd get the active stream ID from the database
      const newMessage: ChatMessage = {
        ...message,
        id: Date.now().toString(),
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, newMessage])

      // Track the message in analytics
      await DatabaseService.trackEvent({
        user_id: user.id,
        event_type: 'chat_message_sent',
        event_data: { message_length: message.message.length }
      })

    } catch (error) {
      console.error('Error adding chat message:', error)
      // Fall back to local state update
      const newMessage: ChatMessage = {
        ...message,
        id: Date.now().toString(),
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, newMessage])
    }
  }

  const clearChat = () => {
    setChatMessages([])
  }

  const addStream = (stream: Omit<StreamData, 'id' | 'views' | 'date'>) => {
    const newStream: StreamData = {
      ...stream,
      id: Date.now().toString(),
      views: 0,
      date: new Date()
    }
    setStreamArchive(prev => [newStream, ...prev])
  }

  const deleteStream = (id: string) => {
    setStreamArchive(prev => prev.filter(stream => stream.id !== id))
  }

  const refreshStats = async () => {
    try {
      const stats = await DatabaseService.getWebsiteStats()
      setTotalUsers(stats.totalUsers)
      setTotalPosts(stats.totalPosts)
      setTotalViews(stats.totalViews)
    } catch (error) {
      console.error('Error refreshing statistics:', error)
    }
  }

  if (!mounted) {
    // Return a minimal context during SSR to prevent hydration issues
    const defaultContext: DataContextType = {
      forumPosts: [],
      streamArchive: [],
      chatMessages: [],
      learningPaths: [],
      upcomingEvents: [],
      achievements: [],
      totalUsers: 0,
      totalPosts: 0,
      totalViews: 0,
      viewerCount: 0,
      isLive: false,
      activeUsers: 0,
      completionRates: {},
      userProgress: {},
      loading: true,
      likePost: () => {},
      dislikePost: () => {},
      addChatMessage: () => {},
      addForumPost: () => {},
      updateForumPost: () => {},
      deleteForumPost: () => {},
      addReply: () => {},
      deleteReply: () => {},
      clearChat: () => {},
      addStream: () => {},
      deleteStream: () => {},
      setViewerCount: () => {},
      setIsLive: () => {},
      refreshStats: () => {},
      updateUserProgress: () => {}
    }
    return (
      <DataContext.Provider value={defaultContext}>
        {children}
      </DataContext.Provider>
    )
  }

  return (
    <DataContext.Provider value={{
      forumPosts,
      addForumPost,
      updateForumPost,
      deleteForumPost,
      addReply,
      deleteReply,
      likePost,
      dislikePost,
      chatMessages,
      addChatMessage,
      clearChat,
      streamArchive,
      addStream,
      deleteStream,
      learningPaths,
      upcomingEvents,
      achievements,
      viewerCount,
      setViewerCount,
      isLive,
      setIsLive,
      totalUsers,
      totalPosts,
      totalViews,
      activeUsers: Math.floor(totalUsers * 0.1), // Estimate 10% active users
      completionRates: {},
      userProgress: {},
      refreshStats,
      updateUserProgress: () => {},
      loading
    }}>
      {children}
    </DataContext.Provider>
  )
}

export function useData() {
  const context = useContext(DataContext)
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider')
  }
  return context
}
