'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers'
import { useData } from '@/components/data-provider'
import { useLanguage } from '@/components/language-provider'
import { SeeAllPosts } from '@/components/see-all-button'
import { SearchFunctionality } from '@/components/search-functionality'
import { AutoComplete, commonOptions } from '@/components/auto-complete'
import { PostActionButtons, FunctionalButton } from '@/components/functional-buttons'
import { Button } from '@/components/ui/button'
import {
  MessageSquare,
  Users,
  TrendingUp,
  Plus,
  Search,
  ThumbsUp,
  ThumbsDown,
  Reply,
  Crown,
  Shield,
  Trash2,
  Flag,
  Pin,
  Lock,
  Eye,
  MessageCircle,
  Calendar,
  User,
  Send,
  Edit,
  Save,
  X,
  BookOpen,
  Heart,
  Leaf,
  Mountain
} from 'lucide-react'

interface ForumPost {
  id: string
  title: string
  content: string
  author: string
  authorEmail: string
  category: string
  createdAt: Date
  updatedAt: Date
  likes: number
  dislikes: number
  userLiked?: boolean
  userDisliked?: boolean
  replies: ForumReply[]
  isPinned: boolean
  isLocked: boolean
  views: number
}

interface ForumReply {
  id: string
  content: string
  author: string
  authorEmail: string
  createdAt: Date
  likes: number
  dislikes: number
  userLiked?: boolean
  userDisliked?: boolean
  parentId?: string
}

interface Category {
  id: string
  name: string
  description: string
  postCount: number
  color: string
  icon: any
}

const getCategories = (posts: ForumPost[]): Category[] => [
  {
    id: 'islamic-education',
    name: 'Islamic Education',
    description: 'Discussions about Quran, Hadith, and Islamic teachings',
    postCount: posts.filter(p => p.category === 'islamic-education').length,
    color: 'text-blue-600',
    icon: BookOpen
  },
  {
    id: 'natural-healing',
    name: 'Natural Healing',
    description: 'Cupping therapy, herbal medicine, and holistic health',
    postCount: posts.filter(p => p.category === 'natural-healing').length,
    color: 'text-green-600',
    icon: Heart
  },
  {
    id: 'plant-medicine',
    name: 'Plant Medicine',
    description: 'Medicinal plants, herbs, and natural remedies',
    postCount: posts.filter(p => p.category === 'plant-medicine').length,
    color: 'text-emerald-600',
    icon: Leaf
  },
  {
    id: 'spiritual-growth',
    name: 'Spiritual Growth',
    description: 'Personal development and spiritual journey',
    postCount: posts.filter(p => p.category === 'spiritual-growth').length,
    color: 'text-purple-600',
    icon: Mountain
  }
]



export default function ForumPage() {
  const { user } = useAuth()
  const { t } = useLanguage()
  const {
    forumPosts: posts,
    addForumPost,
    deleteForumPost,
    addReply,
    deleteReply,
    likePost,
    dislikePost,
    totalPosts,
    totalUsers,
    totalViews
  } = useData()
  const [mounted, setMounted] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showNewPostForm, setShowNewPostForm] = useState(false)
  const [newPost, setNewPost] = useState({
    title: '',
    content: '',
    category: 'islamic-education'
  })
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState('')

  useEffect(() => {
    setMounted(true)
  }, [])

  const isAdmin = user?.email === '<EMAIL>'
  const isModerator = isAdmin || user?.role === 'moderator'

  const categories = getCategories(posts)

  const filteredPosts = posts.filter(post => {
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.content.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  const handleCreatePost = (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !newPost.title.trim() || !newPost.content.trim()) return

    addForumPost({
      title: newPost.title,
      content: newPost.content,
      author: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous',
      authorEmail: user.email || '',
      category: newPost.category,
      isPinned: false,
      isLocked: false
    })

    setNewPost({ title: '', content: '', category: 'islamic-education' })
    setShowNewPostForm(false)
  }

  const handleReply = (postId: string) => {
    if (!user || !replyContent.trim()) return

    addReply(postId, {
      content: replyContent,
      author: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous',
      authorEmail: user.email || ''
    })

    setReplyContent('')
    setReplyingTo(null)
  }

  const handleLike = (postId: string, isReply: boolean = false, replyId?: string) => {
    likePost(postId, isReply, replyId)
  }

  const handleDislike = (postId: string, isReply: boolean = false, replyId?: string) => {
    dislikePost(postId, isReply, replyId)
  }

  const handleDeletePost = (postId: string) => {
    if (!isModerator) return
    deleteForumPost(postId)
  }

  const handleDeleteReply = (postId: string, replyId: string) => {
    if (!isModerator) return
    deleteReply(postId, replyId)
  }

  const togglePin = (postId: string) => {
    if (!isModerator) return
    setPosts(prev => prev.map(post =>
      post.id === postId ? { ...post, isPinned: !post.isPinned } : post
    ))
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Community Forum
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Share knowledge, ask questions, and engage in meaningful discussions about
              Islamic education, natural healing, and spiritual growth.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Forum Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center">
              <MessageSquare className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{totalPosts}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300">{t('forum.totalposts') || 'Total Posts'}</div>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600 mr-3" />
              <div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{totalUsers}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300">{t('community.members') || 'Active Members'}</div>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center">
              <MessageCircle className="h-8 w-8 text-purple-600 mr-3" />
              <div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {posts.reduce((total, post) => total + post.replies.length, 0)}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">{t('forum.totalreplies') || 'Total Replies'}</div>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-orange-600 mr-3" />
              <div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{totalViews}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300">{t('community.views') || 'Total Views'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Categories and Controls */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setSelectedCategory('all')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === 'all'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                All Categories
              </button>
              {categories.map((category) => {
                const Icon = category.icon
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{category.name}</span>
                    <span className="bg-gray-200 dark:bg-gray-600 text-xs px-2 py-1 rounded-full">
                      {category.postCount}
                    </span>
                  </button>
                )
              })}
            </div>

            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search posts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              {user && (
                <Button onClick={() => setShowNewPostForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  New Post
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* New Post Form */}
        {showNewPostForm && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Create New Post</h3>
              <Button variant="outline" size="sm" onClick={() => setShowNewPostForm(false)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <form onSubmit={handleCreatePost} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Category
                </label>
                <select
                  value={newPost.category}
                  onChange={(e) => setNewPost({...newPost, category: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Title
                </label>
                <input
                  type="text"
                  value={newPost.title}
                  onChange={(e) => setNewPost({...newPost, title: e.target.value})}
                  placeholder="Enter post title..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Content
                </label>
                <textarea
                  value={newPost.content}
                  onChange={(e) => setNewPost({...newPost, content: e.target.value})}
                  placeholder="Write your post content..."
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>
              <div className="flex space-x-4">
                <Button type="submit">
                  <Send className="h-4 w-4 mr-2" />
                  Create Post
                </Button>
                <Button type="button" variant="outline" onClick={() => setShowNewPostForm(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        )}

        {/* Posts List */}
        <div className="space-y-6">
          {filteredPosts.length === 0 ? (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-12 text-center">
              <MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No posts found
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                {searchTerm ? 'Try adjusting your search terms' : 'Be the first to start a discussion!'}
              </p>
              {user && !searchTerm && (
                <Button onClick={() => setShowNewPostForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Post
                </Button>
              )}
            </div>
          ) : (
            filteredPosts.map((post) => {
              const category = categories.find(c => c.id === post.category)
              const CategoryIcon = category?.icon || MessageSquare

              return (
                <div key={post.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                  {/* Post Header */}
                  <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          {post.isPinned && (
                            <Pin className="h-4 w-4 text-yellow-600" />
                          )}
                          {post.isLocked && (
                            <Lock className="h-4 w-4 text-red-600" />
                          )}
                          <span className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${category?.color} bg-gray-100 dark:bg-gray-700`}>
                            <CategoryIcon className="h-3 w-3" />
                            <span>{category?.name}</span>
                          </span>
                        </div>
                        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                          {post.title}
                        </h2>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-300">
                          <div className="flex items-center space-x-1">
                            <User className="h-4 w-4" />
                            <span className="font-medium">
                              {post.author}
                              {post.authorEmail === '<EMAIL>' && (
                                <Crown className="h-3 w-3 text-yellow-500 inline ml-1" />
                              )}
                            </span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{mounted ? post.createdAt.toLocaleDateString() : '--/--/----'}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Eye className="h-4 w-4" />
                            <span>{post.views} views</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MessageCircle className="h-4 w-4" />
                            <span>{post.replies.length} replies</span>
                          </div>
                        </div>
                      </div>
                      {isModerator && (
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => togglePin(post.id)}
                            className={post.isPinned ? 'text-yellow-600' : ''}
                          >
                            <Pin className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeletePost(post.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Post Content */}
                  <div className="p-6">
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
                      {post.content}
                    </p>

                    {/* Post Actions */}
                    <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4">
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => handleLike(post.id)}
                          className={`flex items-center space-x-1 px-3 py-1 rounded-lg transition-colors ${
                            post.userLiked
                              ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                              : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                          }`}
                          disabled={!user}
                        >
                          <ThumbsUp className="h-4 w-4" />
                          <span>{post.likes}</span>
                        </button>
                        <button
                          onClick={() => handleDislike(post.id)}
                          className={`flex items-center space-x-1 px-3 py-1 rounded-lg transition-colors ${
                            post.userDisliked
                              ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
                              : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                          }`}
                          disabled={!user}
                        >
                          <ThumbsDown className="h-4 w-4" />
                          <span>{post.dislikes}</span>
                        </button>
                        {user && !post.isLocked && (
                          <button
                            onClick={() => setReplyingTo(replyingTo === post.id ? null : post.id)}
                            className="flex items-center space-x-1 px-3 py-1 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                          >
                            <Reply className="h-4 w-4" />
                            <span>Reply</span>
                          </button>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                          <Flag className="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    {/* Reply Form */}
                    {replyingTo === post.id && (
                      <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-gray-900 dark:text-white">Reply to this post</h4>
                          <Button variant="outline" size="sm" onClick={() => setReplyingTo(null)}>
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                        <textarea
                          value={replyContent}
                          onChange={(e) => setReplyContent(e.target.value)}
                          placeholder="Write your reply..."
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white mb-3"
                        />
                        <div className="flex space-x-2">
                          <Button onClick={() => handleReply(post.id)} size="sm">
                            <Send className="h-4 w-4 mr-2" />
                            Post Reply
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => setReplyingTo(null)}>
                            Cancel
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Replies */}
                    {post.replies.length > 0 && (
                      <div className="mt-6 space-y-4">
                        <h4 className="font-semibold text-gray-900 dark:text-white">
                          Replies ({post.replies.length})
                        </h4>
                        {post.replies.map((reply) => (
                          <div key={reply.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center space-x-2">
                                <span className="font-medium text-gray-900 dark:text-white">
                                  {reply.author}
                                  {reply.authorEmail === '<EMAIL>' && (
                                    <Crown className="h-3 w-3 text-yellow-500 inline ml-1" />
                                  )}
                                </span>
                                <span className="text-sm text-gray-500 dark:text-gray-400">
                                  {mounted ? reply.createdAt.toLocaleDateString() : '--/--/----'}
                                </span>
                              </div>
                              {isModerator && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDeleteReply(post.id, reply.id)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                            <p className="text-gray-700 dark:text-gray-300 mb-3">
                              {reply.content}
                            </p>
                            <div className="flex items-center space-x-4">
                              <button
                                onClick={() => handleLike(post.id, true, reply.id)}
                                className={`flex items-center space-x-1 px-2 py-1 rounded text-sm transition-colors ${
                                  reply.userLiked
                                    ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                                }`}
                                disabled={!user}
                              >
                                <ThumbsUp className="h-3 w-3" />
                                <span>{reply.likes}</span>
                              </button>
                              <button
                                onClick={() => handleDislike(post.id, true, reply.id)}
                                className={`flex items-center space-x-1 px-2 py-1 rounded text-sm transition-colors ${
                                  reply.userDisliked
                                    ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
                                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                                }`}
                                disabled={!user}
                              >
                                <ThumbsDown className="h-3 w-3" />
                                <span>{reply.dislikes}</span>
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )
            })
          )}
        </div>

        {/* Sign In Prompt */}
        {!user && (
          <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 text-center">
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2">
              Join the Discussion
            </h3>
            <p className="text-blue-700 dark:text-blue-300 mb-4">
              Sign in to create posts, reply to discussions, and engage with the community.
            </p>
            <Button asChild>
              <a href="/auth/signin">Sign In to Participate</a>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}