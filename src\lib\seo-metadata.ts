import { Metadata } from 'next';
import { config } from './config';

interface SEOPageData {
  title: string;
  description: string;
  keywords: string;
  type?: 'website' | 'article';
  image?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
}

export function generatePageMetadata(pageData: SEOPageData): Metadata {
  const siteTitle = 'Light Upon Light';
  const fullTitle = `${pageData.title} | ${siteTitle}`;
  const baseUrl = config.site.url;
  
  return {
    title: fullTitle,
    description: pageData.description,
    keywords: pageData.keywords,
    authors: pageData.author ? [{ name: pageData.author }] : [{ name: siteTitle }],
    creator: siteTitle,
    publisher: siteTitle,
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(baseUrl),
    openGraph: {
      title: pageData.title,
      description: pageData.description,
      url: baseUrl,
      siteName: siteTitle,
      images: pageData.image ? [
        {
          url: pageData.image,
          width: 1200,
          height: 630,
          alt: pageData.title,
        }
      ] : [
        {
          url: '/og-image.jpg',
          width: 1200,
          height: 630,
          alt: `${siteTitle} - ${pageData.title}`,
        }
      ],
      locale: 'en_US',
      type: pageData.type || 'website',
      publishedTime: pageData.publishedTime,
      modifiedTime: pageData.modifiedTime,
      authors: pageData.author ? [pageData.author] : undefined,
      section: pageData.section,
      tags: pageData.tags,
    },
    twitter: {
      card: 'summary_large_image',
      title: pageData.title,
      description: pageData.description,
      images: pageData.image ? [pageData.image] : ['/og-image.jpg'],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    alternates: {
      canonical: '/',
      languages: {
        'en': '/',
        'sv': '/?lang=sv',
      },
    },
  };
}

// Predefined SEO data for all pages
export const pagesSEOData: Record<string, SEOPageData> = {
  home: {
    title: 'Wisdom, Knowledge & Spiritual Growth',
    description: 'A comprehensive educational platform for spiritual wisdom, natural healing, Quranic knowledge, and community learning. Discover the connection between nature, faith, and intellectual development.',
    keywords: 'Islamic education, spiritual growth, natural healing, Quran, wisdom, meditation, nature therapy, community learning, enlightenment, consciousness',
    type: 'website',
  },
  live: {
    title: 'Live Stream - Natural Healing & Wisdom Sessions',
    description: 'Join our live streaming sessions for natural healing wisdom, spiritual guidance, and community discussions. Interactive chat and real-time learning experiences.',
    keywords: 'live stream, natural healing, spiritual guidance, wisdom sessions, community chat, interactive learning, enlightenment, consciousness',
    type: 'video',
  },
  forum: {
    title: 'Community Forum - Discussion & Knowledge Sharing',
    description: 'Engage in meaningful discussions about Islamic education, natural healing, and spiritual growth. Share knowledge, ask questions, and connect with like-minded learners.',
    keywords: 'forum, community discussion, Islamic education, natural healing, spiritual growth, knowledge sharing, Q&A, community support',
    type: 'forum',
  },
  community: {
    title: 'Community Learning Hub - Connect & Grow Together',
    description: 'Join our vibrant community of learners exploring natural healing, ancient wisdom, and intelligent living. Access learning paths, events, and achievements.',
    keywords: 'community learning, learning paths, events, achievements, natural healing, ancient wisdom, intelligent living, spiritual community',
    type: 'website',
  },
  cupping: {
    title: 'Cupping Therapy - Natural Healing & Benefits',
    description: 'Discover the ancient art of cupping therapy for natural healing. Learn about benefits, techniques, and the wisdom behind this traditional healing practice.',
    keywords: 'cupping therapy, natural healing, traditional medicine, hijama, alternative therapy, holistic healing, ancient wisdom, therapeutic benefits',
    type: 'article',
  },
  'heart-mind': {
    title: 'Heart & Mind Development - Consciousness & Contentment',
    description: 'Explore the connection between heart and mind development. Learn about consciousness, contentment, and the path to inner peace and spiritual growth.',
    keywords: 'heart development, mind development, consciousness, contentment, inner peace, spiritual growth, emotional intelligence, mindfulness',
    type: 'article',
  },
  plants: {
    title: 'Plants & Natural Healing - Herbal Wisdom',
    description: 'Discover the healing power of plants and herbs. Learn about natural remedies, botanical wisdom, and the therapeutic benefits of nature\'s pharmacy.',
    keywords: 'herbal medicine, plant healing, natural remedies, botanical wisdom, phytotherapy, medicinal plants, nature therapy, herbal treatments',
    type: 'article',
  },
  honey: {
    title: 'Honey & Natural Foods - Healing Nutrition',
    description: 'Explore the healing properties of honey and natural foods. Learn about nutritional wisdom, therapeutic benefits, and nature\'s healing nourishment.',
    keywords: 'honey benefits, natural foods, healing nutrition, therapeutic foods, natural sweeteners, nutritional wisdom, healthy eating, food as medicine',
    type: 'article',
  },
  'nature-elements': {
    title: 'Nature Elements - Earth, Water, Fire & Air Wisdom',
    description: 'Understand the wisdom of natural elements and their role in healing and spiritual development. Explore the connection between nature and consciousness.',
    keywords: 'nature elements, earth wisdom, water healing, fire transformation, air purification, elemental wisdom, natural forces, environmental consciousness',
    type: 'article',
  },
  'heavens-earth': {
    title: 'Heavens & Earth - Cosmic Wisdom & Spiritual Insights',
    description: 'Explore the relationship between heavens and earth, cosmic wisdom, and spiritual insights. Discover the connection between celestial knowledge and earthly life.',
    keywords: 'cosmic wisdom, heavens and earth, spiritual insights, celestial knowledge, astronomy, cosmology, divine creation, universal consciousness',
    type: 'article',
  },
  quran: {
    title: 'Quranic Logic & Wisdom - Divine Knowledge',
    description: 'Discover the logical wisdom and divine knowledge within the Quran. Explore verses that illuminate the path to understanding and enlightenment.',
    keywords: 'Quranic wisdom, divine knowledge, Islamic logic, Quran verses, spiritual guidance, divine revelation, Islamic education, religious wisdom',
    type: 'article',
  },
  'names-of-god': {
    title: '99 Divine Names - Attributes & Healing Benefits',
    description: 'Explore the 99 beautiful names of God and their healing benefits. Learn about divine attributes, spiritual significance, and therapeutic applications.',
    keywords: '99 names of God, divine attributes, Asma ul Husna, spiritual healing, divine names benefits, Islamic spirituality, divine qualities, sacred names',
    type: 'article',
  },
  wisdom: {
    title: 'Ancient Wisdom - Timeless Knowledge & Insights',
    description: 'Access ancient wisdom and timeless knowledge for modern living. Discover insights from traditional teachings and their relevance to contemporary life.',
    keywords: 'ancient wisdom, timeless knowledge, traditional teachings, wisdom literature, philosophical insights, spiritual wisdom, life guidance, enlightenment',
    type: 'article',
  },
  logic: {
    title: 'Logic & Intelligence - Rational Thinking & Wisdom',
    description: 'Develop logical thinking and intelligence through wisdom-based learning. Explore rational approaches to understanding life and spiritual growth.',
    keywords: 'logical thinking, intelligence development, rational wisdom, critical thinking, intellectual growth, reasoning skills, wisdom-based learning, mental clarity',
    type: 'article',
  },
  'nature-law': {
    title: 'Nature Law - Universal Principles & Natural Order',
    description: 'Understand the laws of nature and universal principles that govern life. Learn about natural order, cosmic laws, and their spiritual significance.',
    keywords: 'nature law, universal principles, natural order, cosmic laws, natural philosophy, universal truth, natural wisdom, divine order',
    type: 'article',
  },
  therapy: {
    title: 'Natural Therapy - Holistic Healing Approaches',
    description: 'Explore natural therapy methods and holistic healing approaches. Learn about therapeutic techniques that promote wellness and spiritual well-being.',
    keywords: 'natural therapy, holistic healing, therapeutic approaches, wellness therapy, alternative healing, integrative medicine, healing modalities, therapeutic wisdom',
    type: 'article',
  },
  patience: {
    title: 'Patience & Virtue - Spiritual Development & Growth',
    description: 'Cultivate patience and virtue for spiritual development. Learn about the wisdom of patience, its benefits, and its role in personal growth.',
    keywords: 'patience virtue, spiritual development, personal growth, virtue cultivation, patience benefits, spiritual patience, character development, inner strength',
    type: 'article',
  },
  quiz: {
    title: 'Quiz & Learning - Interactive Knowledge Testing',
    description: 'Test your knowledge with interactive quizzes on natural healing, spiritual wisdom, and Islamic education. Learn through engaging educational content.',
    keywords: 'educational quiz, knowledge testing, interactive learning, spiritual quiz, Islamic education quiz, natural healing quiz, wisdom assessment, learning games',
    type: 'website',
  },
  contact: {
    title: 'Contact Us - Get in Touch',
    description: 'Contact Light Upon Light for questions, feedback, or support. Connect with our team for guidance on natural healing, spiritual growth, and educational resources.',
    keywords: 'contact us, get in touch, support, feedback, questions, customer service, help, communication',
    type: 'website',
  },
  privacy: {
    title: 'Privacy Policy - Data Protection & User Rights',
    description: 'Our privacy policy explains how we protect your data and respect your privacy rights. Learn about our GDPR compliance and data handling practices.',
    keywords: 'privacy policy, data protection, GDPR compliance, user rights, data privacy, information security, privacy rights, data handling',
    type: 'website',
  },
  terms: {
    title: 'Terms of Service - Usage Guidelines',
    description: 'Read our terms of service and usage guidelines for the Light Upon Light platform. Understand your rights and responsibilities as a user.',
    keywords: 'terms of service, usage guidelines, user agreement, terms and conditions, platform rules, user rights, service terms',
    type: 'website',
  },
};
