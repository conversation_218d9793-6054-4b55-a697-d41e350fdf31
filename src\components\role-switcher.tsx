'use client'

// Role Switcher Component
// Allows <EMAIL> to switch between Full Admin and Guest Admin modes

import { useState, useEffect } from 'react'
import { useAuth } from './providers'
import { PlatformService } from '@/lib/platform-service'
import { Shield, User, Settings, Video } from 'lucide-react'

type ActiveRole = 'full_admin' | 'guest_admin'

interface RoleSwitcherProps {
  onRoleChange?: (role: ActiveRole) => void
}

export function RoleSwitcher({ onRoleChange }: RoleSwitcherProps) {
  const { user } = useAuth()
  const [activeRole, setActiveRole] = useState<ActiveRole>('full_admin')
  const [hasMultipleRoles, setHasMultipleRoles] = useState(false)

  useEffect(() => {
    if (user?.email) {
      const multipleRoles = PlatformService.hasMultipleRoles(user.email)
      setHasMultipleRoles(multipleRoles)
    }
  }, [user])

  const handleRoleSwitch = (role: ActiveRole) => {
    setActiveRole(role)
    onRoleChange?.(role)
    
    // Store preference in localStorage
    localStorage.setItem('preferred_role', role)
  }

  // Load preferred role on mount
  useEffect(() => {
    const savedRole = localStorage.getItem('preferred_role') as ActiveRole
    if (savedRole && hasMultipleRoles) {
      setActiveRole(savedRole)
      onRoleChange?.(savedRole)
    }
  }, [hasMultipleRoles, onRoleChange])

  // Only <NAME_EMAIL>
  if (!user || !hasMultipleRoles) {
    return null
  }

  return (
    <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-800 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Settings className="h-5 w-5 text-purple-400" />
          <h3 className="text-lg font-semibold text-purple-300">Role Selection</h3>
        </div>
        <div className="text-sm text-gray-400">
          Active: <span className="text-white font-medium capitalize">{activeRole.replace('_', ' ')}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Full Admin Mode */}
        <button
          onClick={() => handleRoleSwitch('full_admin')}
          className={`p-4 rounded-lg border-2 transition-all ${
            activeRole === 'full_admin'
              ? 'border-purple-500 bg-purple-900/30'
              : 'border-gray-600 bg-gray-800/50 hover:border-purple-600'
          }`}
        >
          <div className="flex items-center space-x-3 mb-2">
            <Shield className={`h-6 w-6 ${
              activeRole === 'full_admin' ? 'text-purple-400' : 'text-gray-400'
            }`} />
            <div className="text-left">
              <h4 className={`font-semibold ${
                activeRole === 'full_admin' ? 'text-purple-300' : 'text-gray-300'
              }`}>
                Full Admin
              </h4>
              <p className="text-xs text-gray-400">Complete site control</p>
            </div>
          </div>
          <div className="text-xs text-left space-y-1">
            <div className="text-gray-300">✓ Site management</div>
            <div className="text-gray-300">✓ User management</div>
            <div className="text-gray-300">✓ Live streaming</div>
            <div className="text-gray-300">✓ Forum moderation</div>
            <div className="text-gray-300">✓ All permissions</div>
          </div>
        </button>

        {/* Guest Admin Mode */}
        <button
          onClick={() => handleRoleSwitch('guest_admin')}
          className={`p-4 rounded-lg border-2 transition-all ${
            activeRole === 'guest_admin'
              ? 'border-blue-500 bg-blue-900/30'
              : 'border-gray-600 bg-gray-800/50 hover:border-blue-600'
          }`}
        >
          <div className="flex items-center space-x-3 mb-2">
            <Video className={`h-6 w-6 ${
              activeRole === 'guest_admin' ? 'text-blue-400' : 'text-gray-400'
            }`} />
            <div className="text-left">
              <h4 className={`font-semibold ${
                activeRole === 'guest_admin' ? 'text-blue-300' : 'text-gray-300'
              }`}>
                Guest Admin
              </h4>
              <p className="text-xs text-gray-400">Live stream focused</p>
            </div>
          </div>
          <div className="text-xs text-left space-y-1">
            <div className="text-gray-300">✓ Live streaming</div>
            <div className="text-gray-300">✓ Chat moderation</div>
            <div className="text-gray-300">✓ User timeouts</div>
            <div className="text-gray-300">✓ Message management</div>
            <div className="text-gray-400">✗ Site administration</div>
          </div>
        </button>
      </div>

      {/* Role Description */}
      <div className="mt-4 p-3 bg-gray-800/50 rounded-lg">
        <div className="text-sm">
          {activeRole === 'full_admin' ? (
            <div>
              <span className="text-purple-300 font-medium">Full Admin Mode:</span>
              <span className="text-gray-300 ml-2">
                Complete access to all site features, user management, and administrative functions.
              </span>
            </div>
          ) : (
            <div>
              <span className="text-blue-300 font-medium">Guest Admin Mode:</span>
              <span className="text-gray-300 ml-2">
                Focused on live streaming and chat moderation. Limited to streaming-related functions only.
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Hook to get current active role
export function useActiveRole() {
  const { user } = useAuth()
  const [activeRole, setActiveRole] = useState<ActiveRole>('full_admin')

  useEffect(() => {
    if (user?.email && PlatformService.hasMultipleRoles(user.email)) {
      const savedRole = localStorage.getItem('preferred_role') as ActiveRole
      if (savedRole) {
        setActiveRole(savedRole)
      }
    }
  }, [user])

  return {
    activeRole,
    setActiveRole,
    hasMultipleRoles: user?.email ? PlatformService.hasMultipleRoles(user.email) : false
  }
}
