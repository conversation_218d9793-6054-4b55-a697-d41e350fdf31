/**
 * Code Helper Utilities
 * Simple functions to help create code more easily
 */

// Component Template Generator
export function generateComponentTemplate(name: string, type: 'page' | 'component' = 'component') {
  const componentName = name.charAt(0).toUpperCase() + name.slice(1);
  
  if (type === 'page') {
    return `"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/providers";
import { useLanguage } from "@/components/language-provider";

export default function ${componentName}Page() {
  const { user } = useAuth();
  const { t } = useLanguage();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Initialize page
    setLoading(false);
  }, []);

  if (loading) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">{t('${name}.title') || '${componentName}'}</h1>
      <p className="text-gray-600 dark:text-gray-300">
        {t('${name}.description') || 'Page content goes here'}
      </p>
    </div>
  );
}`;
  }

  return `"use client";

import { useState } from "react";

interface ${componentName}Props {
  // Add your props here
}

export function ${componentName}({ }: ${componentName}Props) {
  const [state, setState] = useState(false);

  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">${componentName}</h2>
      {/* Component content */}
    </div>
  );
}`;
}

// API Route Template Generator
export function generateApiTemplate(name: string, methods: string[] = ['GET']) {
  const methodHandlers = methods.map(method => `
export async function ${method}(request: Request) {
  try {
    // Handle ${method} request
    return Response.json({ success: true, message: '${method} successful' });
  } catch (error) {
    console.error('${method} ${name} error:', error);
    return Response.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}`).join('\n');

  return `import { NextRequest } from 'next/server';
${methodHandlers}`;
}

// Database Query Helper
export function generateDatabaseQuery(table: string, operation: 'select' | 'insert' | 'update' | 'delete') {
  switch (operation) {
    case 'select':
      return `
// Get ${table} data
export async function get${table.charAt(0).toUpperCase() + table.slice(1)}() {
  const { data, error } = await supabase
    .from('${table}')
    .select('*')
    .eq('is_deleted', false)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching ${table}:', error);
    return { success: false, error: error.message };
  }

  return { success: true, data };
}`;

    case 'insert':
      return `
// Create new ${table} record
export async function create${table.charAt(0).toUpperCase() + table.slice(1)}(data: any) {
  const { data: result, error } = await supabase
    .from('${table}')
    .insert([data])
    .select()
    .single();

  if (error) {
    console.error('Error creating ${table}:', error);
    return { success: false, error: error.message };
  }

  return { success: true, data: result };
}`;

    case 'update':
      return `
// Update ${table} record
export async function update${table.charAt(0).toUpperCase() + table.slice(1)}(id: string, data: any) {
  const { data: result, error } = await supabase
    .from('${table}')
    .update(data)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating ${table}:', error);
    return { success: false, error: error.message };
  }

  return { success: true, data: result };
}`;

    case 'delete':
      return `
// Delete ${table} record (soft delete)
export async function delete${table.charAt(0).toUpperCase() + table.slice(1)}(id: string) {
  const { error } = await supabase
    .from('${table}')
    .update({ is_deleted: true })
    .eq('id', id);

  if (error) {
    console.error('Error deleting ${table}:', error);
    return { success: false, error: error.message };
  }

  return { success: true };
}`;

    default:
      return '// Invalid operation';
  }
}

// Form Component Generator
export function generateFormComponent(name: string, fields: string[]) {
  const fieldInputs = fields.map(field => `
        <div className="mb-4">
          <label htmlFor="${field}" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('form.${field}') || '${field.charAt(0).toUpperCase() + field.slice(1)}'}
          </label>
          <input
            type="text"
            id="${field}"
            name="${field}"
            value={formData.${field}}
            onChange={(e) => setFormData({ ...formData, ${field}: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>`).join('');

  const formDataInterface = fields.map(field => `  ${field}: string;`).join('\n');
  const initialFormData = fields.map(field => `  ${field}: '',`).join('\n');

  return `"use client";

import { useState } from "react";
import { useLanguage } from "@/components/language-provider";
import { Button } from "@/components/ui/button";

interface ${name}FormData {
${formDataInterface}
}

export function ${name}Form() {
  const { t } = useLanguage();
  const [formData, setFormData] = useState<${name}FormData>({
${initialFormData}
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Handle form submission
      console.log('Form data:', formData);
      // Add your submission logic here
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="max-w-md mx-auto">
      <h2 className="text-2xl font-bold mb-6">{t('form.${name.toLowerCase()}') || '${name} Form'}</h2>
      ${fieldInputs}
      <Button type="submit" disabled={loading} className="w-full">
        {loading ? 'Submitting...' : (t('form.submit') || 'Submit')}
      </Button>
    </form>
  );
}`;
}

// Quick CSS Class Generator
export function generateTailwindClasses(type: 'button' | 'card' | 'input' | 'container') {
  const classes = {
    button: 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors',
    card: 'bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700',
    input: 'w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white',
    container: 'container mx-auto px-4 py-8 max-w-7xl'
  };

  return classes[type] || '';
}

// Export all helpers
export const codeHelpers = {
  generateComponentTemplate,
  generateApiTemplate,
  generateDatabaseQuery,
  generateFormComponent,
  generateTailwindClasses
};
