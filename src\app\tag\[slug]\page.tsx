import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getCachedSettings } from '@/lib/cms-service';
import { TagContentLoader } from '@/components/cms-content-loader';
import { ScrollToTopWithProgress } from '@/components/scroll-to-top';
import { Tag, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface TagPageProps {
  params: {
    slug: string;
  };
  searchParams: {
    lang?: string;
  };
}

export async function generateMetadata({ params }: TagPageProps): Promise<Metadata> {
  const settings = await getCachedSettings(true);
  const siteTitle = settings.site_title || 'Light Upon Light';
  
  // Format tag name from slug
  const tagName = params.slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return {
    title: `#${tagName} | ${siteTitle}`,
    description: `Browse all content tagged with ${tagName}. Discover articles, guides, and resources related to ${tagName.toLowerCase()}.`,
    keywords: `${tagName.toLowerCase()}, tag, content, articles, education`,
    openGraph: {
      title: `#${tagName} | ${siteTitle}`,
      description: `Browse all content tagged with ${tagName}.`,
      type: 'website',
    },
  };
}

export default function TagPage({ params, searchParams }: TagPageProps) {
  const language = searchParams.lang || 'en';
  
  // Format tag name from slug for display
  const tagName = params.slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-pink-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-6">
            <Link href="/content">
              <Button variant="outline" size="sm" className="mr-4 text-white border-white hover:bg-white hover:text-purple-600">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to All Content
              </Button>
            </Link>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-6">
              <Tag className="h-12 w-12 text-white mr-4" />
              <h1 className="text-4xl md:text-5xl font-bold text-white">
                #{tagName}
              </h1>
            </div>
            <p className="text-xl text-purple-100 max-w-3xl mx-auto">
              Explore all content tagged with {tagName}. Find articles, guides, 
              and resources specifically related to {tagName.toLowerCase()}.
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-400">
            <Link href="/" className="hover:text-white transition-colors">
              Home
            </Link>
            <span>/</span>
            <Link href="/content" className="hover:text-white transition-colors">
              Content
            </Link>
            <span>/</span>
            <span className="text-white">#{tagName}</span>
          </nav>
        </div>
      </div>

      {/* Content Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <TagContentLoader
          tag={params.slug}
          showSearch={true}
          showFilters={true}
          showPagination={true}
          layout="grid"
          limit={12}
          className="space-y-8"
        />
      </div>

      {/* Enhanced scroll to top with progress */}
      <ScrollToTopWithProgress threshold={200} />
    </div>
  );
}
