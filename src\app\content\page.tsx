import { Metadata } from 'next';
import { getCachedSettings } from '@/lib/cms-service';
import { CMSContentLoader } from '@/components/cms-content-loader';
import { ScrollToTopWithProgress } from '@/components/scroll-to-top';
import { BookOpen, Filter, Search, Grid, List } from 'lucide-react';

export async function generateMetadata(): Promise<Metadata> {
  const settings = await getCachedSettings(true);
  const siteTitle = settings.site_title || 'Light Upon Light';

  return {
    title: `All Content | ${siteTitle}`,
    description: 'Browse all our educational content, articles, and wisdom teachings on natural healing, spiritual growth, and enlightenment.',
    keywords: 'content, articles, education, natural healing, spiritual growth, wisdom, enlightenment',
    openGraph: {
      title: `All Content | ${siteTitle}`,
      description: 'Browse all our educational content, articles, and wisdom teachings.',
      type: 'website',
    },
  };
}

export default function ContentPage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <BookOpen className="h-12 w-12 text-white mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-white">
              All Content
            </h1>
          </div>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Explore our comprehensive collection of educational content, wisdom teachings, 
            and natural healing resources. Find articles, guides, and insights to support 
            your journey of learning and growth.
          </p>
        </div>
      </div>

      {/* Content Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <CMSContentLoader
          showSearch={true}
          showFilters={true}
          showPagination={true}
          layout="grid"
          limit={12}
          className="space-y-8"
        />
      </div>

      {/* Enhanced scroll to top with progress */}
      <ScrollToTopWithProgress threshold={200} />
    </div>
  );
}
