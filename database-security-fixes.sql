-- COMPREHENSIVE DATABASE SECURITY & PERFORMANCE FIXES
-- Light Upon Light Platform - Production Ready Database Schema

-- =====================================================
-- 1. MISSING RLS POLICIES - CRITICAL SECURITY FIX
-- =====================================================

-- Enable RLS on ALL tables (many were missing)
ALTER TABLE forum_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE blocked_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- USERS TABLE POLICIES
CREATE POLICY "Users can view all active users" ON users
  FOR SELECT USING (is_active = true);

CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can manage all users" ON users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role = 'admin'
      AND email IN ('<EMAIL>', '<EMAIL>')
    )
  );

-- FORUM CATEGORIES POLICIES
CREATE POLICY "Everyone can view active categories" ON forum_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage categories" ON forum_categories
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role = 'admin'
      AND email IN ('<EMAIL>', '<EMAIL>')
    )
  );

-- POST LIKES POLICIES
CREATE POLICY "Users can view all likes" ON post_likes
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can like posts" ON post_likes
  FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = user_id);

CREATE POLICY "Users can update their own likes" ON post_likes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own likes" ON post_likes
  FOR DELETE USING (auth.uid() = user_id);

-- EVENTS POLICIES
CREATE POLICY "Everyone can view active events" ON events
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage events" ON events
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role = 'admin'
      AND email IN ('<EMAIL>', '<EMAIL>')
    )
  );

-- EVENT REGISTRATIONS POLICIES
CREATE POLICY "Users can view event registrations" ON event_registrations
  FOR SELECT USING (true);

CREATE POLICY "Users can register for events" ON event_registrations
  FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = user_id);

CREATE POLICY "Users can manage their own registrations" ON event_registrations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can cancel their own registrations" ON event_registrations
  FOR DELETE USING (auth.uid() = user_id);

-- ACHIEVEMENTS POLICIES
CREATE POLICY "Everyone can view achievements" ON achievements
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage achievements" ON achievements
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role = 'admin'
      AND email IN ('<EMAIL>', '<EMAIL>')
    )
  );

-- BLOCKED USERS POLICIES
CREATE POLICY "Moderators can view blocked users" ON blocked_users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role IN ('admin', 'moderator')
    )
  );

CREATE POLICY "Moderators can block users" ON blocked_users
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role IN ('admin', 'moderator')
    )
  );

-- ANALYTICS EVENTS POLICIES (Restricted)
CREATE POLICY "Only system can insert analytics" ON analytics_events
  FOR INSERT WITH CHECK (false); -- Prevent direct inserts

CREATE POLICY "Admins can view analytics" ON analytics_events
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role = 'admin'
      AND email IN ('<EMAIL>', '<EMAIL>')
    )
  );

-- USER SESSIONS POLICIES
CREATE POLICY "Users can view their own sessions" ON user_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own sessions" ON user_sessions
  FOR ALL USING (auth.uid() = user_id);

-- CHAT MESSAGES POLICIES (Enhanced)
DROP POLICY IF EXISTS "Public read access for chat messages" ON chat_messages;
CREATE POLICY "Users can view chat messages" ON chat_messages
  FOR SELECT USING (is_deleted = false);

CREATE POLICY "Authenticated users can send messages" ON chat_messages
  FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = user_id);

CREATE POLICY "Users can edit their own messages" ON chat_messages
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Moderators can delete any message" ON chat_messages
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role IN ('admin', 'moderator')
    )
  );

-- LIVE STREAMS POLICIES (Enhanced)
CREATE POLICY "Everyone can view active streams" ON live_streams
  FOR SELECT USING (is_live = true OR is_archived = true);

CREATE POLICY "Admins can manage streams" ON live_streams
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role = 'admin'
      AND email IN ('<EMAIL>', '<EMAIL>')
    )
  );

-- =====================================================
-- 2. FOREIGN KEY CONSTRAINTS - DATA INTEGRITY FIX
-- =====================================================

-- Add missing foreign key constraints with proper cascade behavior
ALTER TABLE post_likes 
  DROP CONSTRAINT IF EXISTS post_likes_post_id_fkey,
  DROP CONSTRAINT IF EXISTS post_likes_reply_id_fkey;

-- Fix post_likes constraints (either post_id OR reply_id, not both)
ALTER TABLE post_likes 
  ADD CONSTRAINT post_likes_post_id_fkey 
    FOREIGN KEY (post_id) REFERENCES forum_posts(id) ON DELETE CASCADE,
  ADD CONSTRAINT post_likes_reply_id_fkey 
    FOREIGN KEY (reply_id) REFERENCES forum_replies(id) ON DELETE CASCADE,
  ADD CONSTRAINT post_likes_check_target 
    CHECK ((post_id IS NOT NULL AND reply_id IS NULL) OR (post_id IS NULL AND reply_id IS NOT NULL));

-- Add missing constraints for data integrity
ALTER TABLE forum_posts 
  ADD CONSTRAINT forum_posts_title_length CHECK (length(title) >= 5 AND length(title) <= 500),
  ADD CONSTRAINT forum_posts_content_length CHECK (length(content) >= 10);

ALTER TABLE forum_replies 
  ADD CONSTRAINT forum_replies_content_length CHECK (length(content) >= 5);

ALTER TABLE chat_messages 
  ADD CONSTRAINT chat_messages_content_length CHECK (length(message) >= 1 AND length(message) <= 1000);

ALTER TABLE users 
  ADD CONSTRAINT users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- =====================================================
-- 3. PERFORMANCE INDEXES - QUERY OPTIMIZATION
-- =====================================================

-- Drop existing indexes if they exist and recreate optimized ones
DROP INDEX IF EXISTS idx_forum_posts_category;
DROP INDEX IF EXISTS idx_forum_posts_author;
DROP INDEX IF EXISTS idx_forum_posts_created;

-- Optimized composite indexes for common queries
CREATE INDEX CONCURRENTLY idx_forum_posts_active_category_created 
  ON forum_posts(category_id, created_at DESC) 
  WHERE is_deleted = false;

CREATE INDEX CONCURRENTLY idx_forum_posts_author_active 
  ON forum_posts(author_id, created_at DESC) 
  WHERE is_deleted = false;

CREATE INDEX CONCURRENTLY idx_forum_posts_pinned_created 
  ON forum_posts(is_pinned DESC, created_at DESC) 
  WHERE is_deleted = false;

-- Forum replies optimization
CREATE INDEX CONCURRENTLY idx_forum_replies_post_created 
  ON forum_replies(post_id, created_at ASC) 
  WHERE is_deleted = false;

-- Chat messages optimization
CREATE INDEX CONCURRENTLY idx_chat_messages_stream_created 
  ON chat_messages(stream_id, created_at ASC) 
  WHERE is_deleted = false;

-- User sessions optimization
CREATE INDEX CONCURRENTLY idx_user_sessions_active 
  ON user_sessions(user_id, expires_at) 
  WHERE is_active = true;

-- Analytics optimization
CREATE INDEX CONCURRENTLY idx_analytics_events_type_created 
  ON analytics_events(event_type, created_at DESC);

CREATE INDEX CONCURRENTLY idx_analytics_events_user_created 
  ON analytics_events(user_id, created_at DESC) 
  WHERE user_id IS NOT NULL;

-- Learning paths optimization
CREATE INDEX CONCURRENTLY idx_learning_enrollments_user_path 
  ON learning_enrollments(user_id, learning_path_id, enrolled_at DESC);

-- Events optimization
CREATE INDEX CONCURRENTLY idx_events_active_date 
  ON events(event_date, event_time) 
  WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_event_registrations_event_user 
  ON event_registrations(event_id, user_id, registered_at DESC);

-- =====================================================
-- 4. AUTOMATIC TIMESTAMP MANAGEMENT
-- =====================================================

-- Enhanced updated_at trigger function with validation
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update if the row actually changed
  IF row(NEW.*) IS DISTINCT FROM row(OLD.*) THEN
    NEW.updated_at = CURRENT_TIMESTAMP;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers to all relevant tables
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at 
  BEFORE UPDATE ON users 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_forum_posts_updated_at ON forum_posts;
CREATE TRIGGER update_forum_posts_updated_at 
  BEFORE UPDATE ON forum_posts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_forum_replies_updated_at ON forum_replies;
CREATE TRIGGER update_forum_replies_updated_at 
  BEFORE UPDATE ON forum_replies 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_learning_paths_updated_at ON learning_paths;
CREATE TRIGGER update_learning_paths_updated_at 
  BEFORE UPDATE ON learning_paths 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_events_updated_at ON events;
CREATE TRIGGER update_events_updated_at
  BEFORE UPDATE ON events
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 5. DATA CLEANUP FUNCTIONS - PREVENT DATABASE BLOAT
-- =====================================================

-- Function to clean up old analytics events (keep last 90 days)
CREATE OR REPLACE FUNCTION cleanup_old_analytics()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM analytics_events
  WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';

  GET DIAGNOSTICS deleted_count = ROW_COUNT;

  -- Log cleanup activity
  INSERT INTO analytics_events (event_type, event_data, created_at)
  VALUES ('system_cleanup', jsonb_build_object('deleted_analytics_count', deleted_count), CURRENT_TIMESTAMP);

  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up expired user sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM user_sessions
  WHERE expires_at < CURRENT_TIMESTAMP OR is_active = false;

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old chat messages (keep last 30 days for non-archived streams)
CREATE OR REPLACE FUNCTION cleanup_old_chat_messages()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM chat_messages
  WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days'
    AND stream_id NOT IN (
      SELECT id FROM live_streams WHERE is_archived = true
    );

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to archive old forum posts (soft delete after 2 years of inactivity)
CREATE OR REPLACE FUNCTION archive_inactive_posts()
RETURNS INTEGER AS $$
DECLARE
  archived_count INTEGER;
BEGIN
  UPDATE forum_posts
  SET is_deleted = true, updated_at = CURRENT_TIMESTAMP
  WHERE updated_at < CURRENT_TIMESTAMP - INTERVAL '2 years'
    AND is_deleted = false
    AND is_pinned = false;

  GET DIAGNOSTICS archived_count = ROW_COUNT;
  RETURN archived_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update forum statistics
CREATE OR REPLACE FUNCTION update_forum_statistics()
RETURNS void AS $$
BEGIN
  -- Update post reply counts
  UPDATE forum_posts
  SET reply_count = (
    SELECT COUNT(*)
    FROM forum_replies
    WHERE post_id = forum_posts.id AND is_deleted = false
  )
  WHERE is_deleted = false;

  -- Update like/dislike counts
  UPDATE forum_posts
  SET likes = (
    SELECT COUNT(*)
    FROM post_likes
    WHERE post_id = forum_posts.id AND is_like = true
  ),
  dislikes = (
    SELECT COUNT(*)
    FROM post_likes
    WHERE post_id = forum_posts.id AND is_like = false
  )
  WHERE is_deleted = false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 6. REALTIME CONFIGURATION - SUPABASE PUBLICATIONS
-- =====================================================

-- Drop existing publication if it exists
DROP PUBLICATION IF EXISTS supabase_realtime;

-- Create comprehensive realtime publication
CREATE PUBLICATION supabase_realtime FOR TABLE
  chat_messages,
  live_streams,
  forum_posts,
  forum_replies,
  post_likes,
  event_registrations,
  user_achievements,
  analytics_events;

-- Enable realtime for specific tables with filters
ALTER PUBLICATION supabase_realtime ADD TABLE chat_messages;
ALTER PUBLICATION supabase_realtime ADD TABLE live_streams;
ALTER PUBLICATION supabase_realtime ADD TABLE forum_posts;
ALTER PUBLICATION supabase_realtime ADD TABLE forum_replies;
ALTER PUBLICATION supabase_realtime ADD TABLE post_likes;

-- =====================================================
-- 7. ENHANCED DATA VALIDATION FUNCTIONS
-- =====================================================

-- Function to validate user email format
CREATE OR REPLACE FUNCTION validate_email(email_input TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN email_input ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to sanitize user input
CREATE OR REPLACE FUNCTION sanitize_input(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
  -- Remove potential XSS and harmful content
  RETURN regexp_replace(
    regexp_replace(
      regexp_replace(input_text, '<[^>]*>', '', 'g'), -- Remove HTML tags
      'javascript:', '', 'gi' -- Remove javascript: protocol
    ),
    'on\w+\s*=', '', 'gi' -- Remove event handlers
  );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to check admin privileges
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users
    WHERE id = user_id
      AND role = 'admin'
      AND is_active = true
      AND email IN ('<EMAIL>', '<EMAIL>')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check moderator privileges
CREATE OR REPLACE FUNCTION is_moderator(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users
    WHERE id = user_id
      AND role IN ('admin', 'moderator')
      AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 8. AUTOMATED MAINTENANCE SCHEDULE
-- =====================================================

-- Note: These would typically be set up as cron jobs or scheduled functions
-- In Supabase, you can use pg_cron extension or external schedulers

-- Example cron job setup (requires pg_cron extension):
-- SELECT cron.schedule('cleanup-analytics', '0 2 * * *', 'SELECT cleanup_old_analytics();');
-- SELECT cron.schedule('cleanup-sessions', '0 3 * * *', 'SELECT cleanup_expired_sessions();');
-- SELECT cron.schedule('cleanup-chat', '0 4 * * 0', 'SELECT cleanup_old_chat_messages();');
-- SELECT cron.schedule('update-stats', '0 1 * * *', 'SELECT update_forum_statistics();');

-- =====================================================
-- 9. SECURITY AUDIT FUNCTIONS
-- =====================================================

-- Function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
  event_type TEXT,
  user_id UUID DEFAULT NULL,
  event_details JSONB DEFAULT '{}'::jsonb
)
RETURNS void AS $$
BEGIN
  INSERT INTO analytics_events (
    user_id,
    event_type,
    event_data,
    created_at
  ) VALUES (
    user_id,
    'security_' || event_type,
    event_details,
    CURRENT_TIMESTAMP
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check for suspicious activity
CREATE OR REPLACE FUNCTION check_suspicious_activity(user_id UUID)
RETURNS JSONB AS $$
DECLARE
  result JSONB := '{}'::jsonb;
  message_count INTEGER;
  login_attempts INTEGER;
BEGIN
  -- Check for excessive messaging
  SELECT COUNT(*) INTO message_count
  FROM chat_messages
  WHERE user_id = check_suspicious_activity.user_id
    AND created_at > CURRENT_TIMESTAMP - INTERVAL '1 hour';

  -- Check for multiple login attempts
  SELECT COUNT(*) INTO login_attempts
  FROM analytics_events
  WHERE user_id = check_suspicious_activity.user_id
    AND event_type = 'user_login'
    AND created_at > CURRENT_TIMESTAMP - INTERVAL '1 hour';

  result := jsonb_build_object(
    'excessive_messaging', message_count > 50,
    'message_count', message_count,
    'multiple_logins', login_attempts > 10,
    'login_attempts', login_attempts,
    'checked_at', CURRENT_TIMESTAMP
  );

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 10. FINAL OPTIMIZATIONS
-- =====================================================

-- Update table statistics for query planner
ANALYZE users;
ANALYZE forum_posts;
ANALYZE forum_replies;
ANALYZE chat_messages;
ANALYZE live_streams;
ANALYZE analytics_events;

-- Vacuum tables to reclaim space
VACUUM (ANALYZE) users;
VACUUM (ANALYZE) forum_posts;
VACUUM (ANALYZE) forum_replies;
VACUUM (ANALYZE) chat_messages;

-- Grant necessary permissions for application user
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO anon;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION cleanup_old_analytics() TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_sessions() TO authenticated;
GRANT EXECUTE ON FUNCTION validate_email(TEXT) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION sanitize_input(TEXT) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION is_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION is_moderator(UUID) TO authenticated;
