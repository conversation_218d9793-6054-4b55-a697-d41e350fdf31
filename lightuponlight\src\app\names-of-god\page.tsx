import { Book<PERSON><PERSON>, Star, Heart } from 'lucide-react'

const namesOfGod = [
  { arabic: 'الرَّحْمَنُ', transliteration: '<PERSON><PERSON><PERSON><PERSON>', english: 'The Most Gracious', meaning: 'Universal compassion and kindness that extends to all creation without discrimination. This principle teaches unconditional love and care for all beings, promoting empathy and humanitarian values.' },
  { arabic: 'الرَّحِيمُ', transliteration: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', english: 'The Most Merciful', meaning: 'Specific mercy and forgiveness that responds to individual needs. This attribute encourages forgiveness, second chances, and understanding in human relationships and personal growth.' },
  { arabic: 'الْمَلِكُ', transliteration: '<PERSON><PERSON><PERSON>', english: 'The Sovereign', meaning: 'Perfect leadership and governance based on justice and wisdom. This principle guides ethical leadership, fair decision-making, and responsible authority in all aspects of life.' },
  { arabic: 'الْقُدُّوسُ', transliteration: 'Al-Quddus', english: 'The Pure', meaning: 'Perfect purity and freedom from all defects or corruption. This principle promotes cleanliness, integrity, and moral purity in thoughts, actions, and environment.' },
  { arabic: 'السَّلاَمُ', transliteration: 'As-<PERSON>aa<PERSON>', english: 'The Source of Peace', meaning: 'Complete peace, safety, and harmony. This attribute teaches conflict resolution, inner tranquility, and the creation of peaceful environments for human flourishing.' },
  { arabic: 'الْمُؤْمِنُ', transliteration: '<PERSON>-<PERSON>\'min', english: 'The Faithful', meaning: 'Absolute trustworthiness and reliability. This principle emphasizes the importance of keeping promises, being dependable, and building trust in relationships and society.' },
  { arabic: 'الْمُهَيْمِنُ', transliteration: 'Al-Muhaymin', english: 'The Guardian', meaning: 'Perfect oversight and protection that ensures safety and security. This principle teaches responsible leadership, careful attention to details, and protective care for those under one\'s responsibility.' },
  { arabic: 'الْعَزِيزُ', transliteration: 'Al-Aziz', english: 'The Mighty', meaning: 'Invincible strength combined with honor and dignity. This attribute inspires inner strength, resilience in facing challenges, and maintaining dignity under pressure.' },
  { arabic: 'الْجَبَّارُ', transliteration: 'Al-Jabbar', english: 'The Compeller', meaning: 'The power to restore and mend what is broken. This principle teaches healing, restoration, and the ability to fix problems and repair relationships.' },
  { arabic: 'الْمُتَكَبِّرُ', transliteration: 'Al-Mutakabbir', english: 'The Majestic', meaning: 'The One who is clear from the attributes of the creatures and from resembling them.' },
  { arabic: 'الْخَالِقُ', transliteration: 'Al-Khaliq', english: 'The Creator', meaning: 'The One who brings everything from non-existence to existence.' },
  { arabic: 'الْبَارِئُ', transliteration: 'Al-Bari\'', english: 'The Evolver', meaning: 'The Maker, The Creator who has the Power to turn the entities.' },
  { arabic: 'الْمُصَوِّرُ', transliteration: 'Al-Musawwir', english: 'The Fashioner', meaning: 'The One who forms His creatures in different pictures.' },
  { arabic: 'الْغَفَّارُ', transliteration: 'Al-Ghaffar', english: 'The Great Forgiver', meaning: 'The Forgiver, The One who forgives the sins of His slaves time and time again.' },
  { arabic: 'الْقَهَّارُ', transliteration: 'Al-Qahhar', english: 'The Subduer', meaning: 'The Dominant, The One who has the perfect Power and is not unable over anything.' },
  { arabic: 'الْوَهَّابُ', transliteration: 'Al-Wahhab', english: 'The Bestower', meaning: 'The One who is Generous in giving plenty without any return. He is everything that benefits whether Halal or Haram.' },
  { arabic: 'الرَّزَّاقُ', transliteration: 'Ar-Razzaq', english: 'The Provider', meaning: 'The One who gives all the creatures their needs and gives them what benefits them by His Power and Mercy.' },
  { arabic: 'الْفَتَّاحُ', transliteration: 'Al-Fattah', english: 'The Opener', meaning: 'The One who opens for His slaves the closed worldly and religious matters.' },
  { arabic: 'اَلْعَلِيْمُ', transliteration: 'Al-\'Alim', english: 'The All-Knowing', meaning: 'The Knowledgeable; The One nothing is absent from His knowledge.' },
  { arabic: 'الْقَابِضُ', transliteration: 'Al-Qabid', english: 'The Constrictor', meaning: 'The One who constricts the sustenance by His wisdom and expands and widens it with His Generosity and Mercy.' },
  { arabic: 'الْبَاسِطُ', transliteration: 'Al-Basit', english: 'The Expander', meaning: 'The One who constricts the sustenance by His wisdom and expands and widens it with His Generosity and Mercy.' },
  { arabic: 'الْخَافِضُ', transliteration: 'Al-Khafid', english: 'The Abaser', meaning: 'The One who lowers whoever He willed by His Destruction and raises whoever He willed by His Endowment.' },
  { arabic: 'الرَّافِعُ', transliteration: 'Ar-Rafi\'', english: 'The Exalter', meaning: 'The One who lowers whoever He willed by His Destruction and raises whoever He willed by His Endowment.' },
  { arabic: 'الْمُعِزُّ', transliteration: 'Al-Mu\'izz', english: 'The Honorer', meaning: 'He gives esteem to whoever He willed, hence there is no one to degrade Him; And He degrades whoever He willed, hence there is no one to give Him esteem.' },
  { arabic: 'الْمُذِلُّ', transliteration: 'Al-Mudhill', english: 'The Dishonorer', meaning: 'He gives esteem to whoever He willed, hence there is no one to degrade Him; And He degrades whoever He willed, hence there is no one to give Him esteem.' },
  { arabic: 'السَّمِيعُ', transliteration: 'As-Sami\'', english: 'The All-Hearing', meaning: 'The One who Hears all things that are heard by His Eternal Hearing without an ear, instrument or organ.' },
  { arabic: 'الْبَصِيرُ', transliteration: 'Al-Basir', english: 'The All-Seeing', meaning: 'The One who Sees all things that are seen by His Eternal Seeing without a pupil or any other instrument.' },
  { arabic: 'الْحَكَمُ', transliteration: 'Al-Hakam', english: 'The Judge', meaning: 'He is the Ruler and His judgment is His Word.' },
  { arabic: 'الْعَدْلُ', transliteration: 'Al-\'Adl', english: 'The Just', meaning: 'The One who is entitled to do what He does.' },
  { arabic: 'اللَّطِيفُ', transliteration: 'Al-Latif', english: 'The Gentle', meaning: 'The One who is kind to His slaves and endows upon them.' },
  { arabic: 'الْخَبِيرُ', transliteration: 'Al-Khabir', english: 'The Aware', meaning: 'The One who knows the truth of things.' },
  { arabic: 'الْحَلِيمُ', transliteration: 'Al-Halim', english: 'The Forbearing', meaning: 'The One who delays the punishment for those who deserve it and then He might forgive them.' },
  { arabic: 'الْعَظِيمُ', transliteration: 'Al-\'Azim', english: 'The Magnificent', meaning: 'The One deserving the attributes of Exaltment, Glory, Extolement, and Purity from all imperfection.' },
  { arabic: 'الْغَفُورُ', transliteration: 'Al-Ghafur', english: 'The Much-Forgiving', meaning: 'The One who forgives a lot.' },
  { arabic: 'الشَّكُورُ', transliteration: 'Ash-Shakur', english: 'The Appreciative', meaning: 'The One who gives a lot of reward for a little obedience.' },
  { arabic: 'الْعَلِيُّ', transliteration: 'Al-\'Ali', english: 'The Most High', meaning: 'The One who is clear from the attributes of the creatures.' },
  { arabic: 'الْكَبِيرُ', transliteration: 'Al-Kabir', english: 'The Most Great', meaning: 'The One who is greater than everything in status.' },
  { arabic: 'الْحَفِيظُ', transliteration: 'Al-Hafiz', english: 'The Preserver', meaning: 'The One who protects whatever and whoever He willed to protect.' },
  { arabic: 'الْمُقِيتُ', transliteration: 'Al-Muqit', english: 'The Nourisher', meaning: 'The One who has the Power.' },
  { arabic: 'الْحَسِيبُ', transliteration: 'Al-Hasib', english: 'The Reckoner', meaning: 'The One who gives the satisfaction.' },
  { arabic: 'الْجَلِيلُ', transliteration: 'Al-Jalil', english: 'The Majestic', meaning: 'The One who is attributed with greatness of Power and Glory of status.' },
  { arabic: 'الْكَرِيمُ', transliteration: 'Al-Karim', english: 'The Bountiful', meaning: 'The One who is clear from abjectness.' },
  { arabic: 'الرَّقِيبُ', transliteration: 'Ar-Raqib', english: 'The Watchful', meaning: 'The One that nothing is absent from Him. Hence it\'s meaning is related to the attribute of Knowledge.' },
  { arabic: 'الْمُجِيبُ', transliteration: 'Al-Mujib', english: 'The Responsive', meaning: 'The One who answers the one in need if he asks Him and rescues the yearner if he calls upon Him.' },
  { arabic: 'الْوَاسِعُ', transliteration: 'Al-Wasi\'', english: 'The All-Encompassing', meaning: 'The Knowledgeable.' },
  { arabic: 'الْحَكِيمُ', transliteration: 'Al-Hakim', english: 'The Wise', meaning: 'The One who is correct in His doings.' },
  { arabic: 'الْوَدُودُ', transliteration: 'Al-Wadud', english: 'The Loving', meaning: 'The One who loves His believing slaves and His believing slaves love Him. His love to His slaves is His Will to be merciful to them and praise them.' },
  { arabic: 'الْمَجِيدُ', transliteration: 'Al-Majid', english: 'The Most Glorious', meaning: 'The One who is with perfect Power, High Status, Compassion, Generosity and Kindness.' },
  { arabic: 'الْبَاعِثُ', transliteration: 'Al-Ba\'ith', english: 'The Resurrector', meaning: 'The One who resurrects His slaves after death for reward and/or punishment.' },
  { arabic: 'الشَّهِيدُ', transliteration: 'Ash-Shahid', english: 'The Witness', meaning: 'The One who nothing is absent from Him.' },
  { arabic: 'الْحَقُّ', transliteration: 'Al-Haqq', english: 'The Truth', meaning: 'Absolute truth and reality that serves as the foundation for all knowledge and understanding. This principle promotes honesty, authenticity, and the pursuit of truth in all endeavors.' },
  { arabic: 'الصَّبُورُ', transliteration: 'As-Sabur', english: 'The Patient', meaning: 'Perfect patience that endures without haste or frustration. This attribute teaches perseverance, emotional regulation, and the wisdom of proper timing in all actions.' },
  { arabic: 'الشَّكُورُ', transliteration: 'Ash-Shakur', english: 'The Appreciative', meaning: 'Recognition and appreciation that multiplies the value of good deeds. This principle encourages gratitude, positive reinforcement, and acknowledging the efforts of others.' },
  { arabic: 'الْحَكِيمُ', transliteration: 'Al-Hakim', english: 'The Wise', meaning: 'Perfect wisdom that combines knowledge with sound judgment. This attribute promotes thoughtful decision-making, learning from experience, and applying knowledge effectively.' },
  { arabic: 'الْوَدُودُ', transliteration: 'Al-Wadud', english: 'The Loving', meaning: 'Unconditional love that seeks the best for others. This principle teaches compassion, kindness, and creating loving relationships and communities.' },
  { arabic: 'الْجَمِيلُ', transliteration: 'Al-Jamil', english: 'The Beautiful', meaning: 'Perfect beauty in all forms - physical, moral, and spiritual. This attribute inspires appreciation of beauty, creation of beautiful things, and developing inner beauty of character.' },
  { arabic: 'الْكَرِيمُ', transliteration: 'Al-Karim', english: 'The Generous', meaning: 'Noble generosity that gives freely without expectation of return. This principle promotes charitable giving, sharing knowledge, and generous treatment of others.' },
  { arabic: 'الْحَلِيمُ', transliteration: 'Al-Halim', english: 'The Gentle', meaning: 'Gentle forbearance that responds to mistakes with kindness rather than harsh punishment. This attribute teaches patience with others\' faults and gentle correction.' },
  { arabic: 'الْعَفُوُّ', transliteration: 'Al-\'Afuww', english: 'The Pardoner', meaning: 'The power to forgive and erase the effects of wrongdoing. This principle promotes forgiveness, letting go of grudges, and giving others fresh starts.' },
  { arabic: 'الْهَادِي', transliteration: 'Al-Hadi', english: 'The Guide', meaning: 'Perfect guidance that leads to the right path. This attribute inspires teaching, mentoring, and helping others find their way to success and fulfillment.' }
]

export default function NamesOfGodPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/20 dark:via-indigo-900/20 dark:to-purple-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <BookOpen className="h-16 w-16 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              The 99 Divine Attributes
            </h1>
            <div className="arabic-text text-2xl md:text-3xl text-blue-600 dark:text-blue-400 mb-4">
              أَسْمَاءُ اللَّهِ الْحُسْنَى
            </div>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Explore 99 divine attributes that represent perfect qualities and principles.
              These names offer logical frameworks for understanding excellence, wisdom, and ideal characteristics
              that inspire human development and ethical living.
            </p>
          </div>
        </div>
      </div>

      {/* Legal Disclaimer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2">
            Respectful Educational Approach
          </h3>
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            The divine attributes presented here are explored through the lens of human development and universal principles.
            These interpretations are offered with utmost respect for Islamic tradition while making the wisdom accessible
            to diverse audiences. They should not replace traditional religious study or guidance from qualified scholars.
          </p>
        </div>
      </div>

      {/* Quranic Verse */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="verse-container text-center">
          <div className="arabic-text text-2xl md:text-3xl mb-4 text-gray-800 dark:text-gray-200">
            وَلِلَّهِ الْأَسْمَاءُ الْحُسْنَىٰ فَادْعُوهُ بِهَا
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-2">
            "And to Allah belong the best names, so invoke Him by them."
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Quran 7:180 (Al-A'raf)
          </p>
          <div className="mt-4 text-sm text-gray-600 dark:text-gray-400 italic">
            These names represent perfect attributes that serve as ideals for human character development and understanding.
          </div>
        </div>
      </div>

      {/* Names Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {namesOfGod.map((name, index) => (
            <div
              key={index}
              className="bg-white dark:bg-gray-900 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 p-6 border-l-4 border-blue-500"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {index + 1}
                  </span>
                </div>
                <Heart className="h-5 w-5 text-red-500" />
              </div>
              
              <div className="text-center mb-4">
                <div className="arabic-text text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                  {name.arabic}
                </div>
                <div className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-1">
                  {name.transliteration}
                </div>
                <div className="text-md font-medium text-gray-600 dark:text-gray-300">
                  {name.english}
                </div>
              </div>
              
              <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                  {name.meaning}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Note about completion */}
        <div className="mt-12 text-center">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 max-w-2xl mx-auto">
            <BookOpen className="h-8 w-8 text-blue-600 mx-auto mb-4" />
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              <strong>Note:</strong> This page shows the first 50 of the 99 Beautiful Names of Allah. 
              The complete collection includes all 99 names with detailed explanations, benefits of recitation, 
              and spiritual guidance for each name.
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Regular recitation and contemplation of these names brings spiritual purification, 
              increased faith, and closeness to Allah (SWT).
            </p>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 bg-white dark:bg-gray-900 rounded-lg shadow-lg">
            <Heart className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Spiritual Purification
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Regular recitation purifies the heart and soul, bringing peace and tranquility.
            </p>
          </div>
          
          <div className="text-center p-6 bg-white dark:bg-gray-900 rounded-lg shadow-lg">
            <Star className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Increased Faith
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Understanding Allah's attributes strengthens belief and trust in His wisdom.
            </p>
          </div>
          
          <div className="text-center p-6 bg-white dark:bg-gray-900 rounded-lg shadow-lg">
            <BookOpen className="h-12 w-12 text-blue-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Divine Connection
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Contemplating these names creates a deeper connection with the Divine.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
