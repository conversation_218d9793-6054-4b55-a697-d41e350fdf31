'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers'
import { useData } from '@/components/data-provider'
import { useLanguage } from '@/components/language-provider'
import { Button } from '@/components/ui/button'
import { User, Settings, Heart, BookOpen, MessageCircle, Award, Shield, Bell, Eye, Lock, Download, Trash2, Cookie, Mail, AlertTriangle, CheckCircle } from 'lucide-react'
import { EmailService, type ContactFormData } from '@/lib/email-service'

export default function AccountPage() {
  const [activeTab, setActiveTab] = useState('profile')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [showCookieSettings, setShowCookieSettings] = useState(false)
  const [showContactForm, setShowContactForm] = useState(false)
  const [contactMessage, setContactMessage] = useState('')
  const [contactSubject, setContactSubject] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  // 2FA State
  const [show2FASetup, setShow2FASetup] = useState(false)
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
  const [qrCodeUrl, setQrCodeUrl] = useState('')
  const [verificationCode, setVerificationCode] = useState('')
  const [backupCodes, setBackupCodes] = useState<string[]>([])
  const [setup2FAStep, setSetup2FAStep] = useState(1)

  const { user, signOut } = useAuth()
  const { userProfile, updateUserProfile, deleteUserAccount, getUserData } = useData()
  const { t } = useLanguage()

  // Functional handlers
  const handleDownloadData = async () => {
    try {
      const userData = await getUserData?.(user?.id || '')
      if (userData) {
        const dataBlob = new Blob([JSON.stringify(userData, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(dataBlob)
        const a = document.createElement('a')
        a.href = url
        a.download = `light-upon-light-data-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        setSubmitStatus('success')
        setTimeout(() => setSubmitStatus('idle'), 3000)
      }
    } catch (error) {
      console.error('Error downloading data:', error)
      setSubmitStatus('error')
      setTimeout(() => setSubmitStatus('idle'), 3000)
    }
  }

  const handleDeleteAccount = async () => {
    try {
      await deleteUserAccount?.(user?.id || '')
      await signOut()
      setShowDeleteConfirm(false)
    } catch (error) {
      console.error('Error deleting account:', error)
      setSubmitStatus('error')
      setTimeout(() => setSubmitStatus('idle'), 3000)
    }
  }

  const handleSendMessage = async () => {
    if (!contactMessage.trim() || !contactSubject.trim()) return

    if (!user?.email) {
      alert('User email not available')
      return
    }

    setIsSubmitting(true)
    try {
      const contactData: ContactFormData = {
        name: user.user_metadata?.full_name || user.email.split('@')[0] || 'Account User',
        email: user.email,
        subject: contactSubject,
        message: contactMessage,
        source: 'account-page'
      }

      const result = await EmailService.sendContactMessage(contactData)

      if (result.success) {
        setSubmitStatus('success')
        setContactMessage('')
        setContactSubject('')
        setShowContactForm(false)
        setTimeout(() => setSubmitStatus('idle'), 3000)
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('Error sending message:', error)
      setSubmitStatus('error')
      setTimeout(() => setSubmitStatus('idle'), 3000)
    } finally {
      setIsSubmitting(false)
    }
  }

  // 2FA Functions
  const handleEnable2FA = async () => {
    setShow2FASetup(true)
    setSetup2FAStep(1)

    // Generate QR code URL (in real app, this would come from your backend)
    const secret = 'JBSWY3DPEHPK3PXP' // This would be generated by your backend
    const appName = 'Light Upon Light'
    const userEmail = user?.email || '<EMAIL>'
    const qrUrl = `otpauth://totp/${encodeURIComponent(appName)}:${encodeURIComponent(userEmail)}?secret=${secret}&issuer=${encodeURIComponent(appName)}`
    setQrCodeUrl(qrUrl)
  }

  const handleVerify2FA = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      alert('Please enter a valid 6-digit code')
      return
    }

    setIsSubmitting(true)
    try {
      // In real app, verify the code with your backend
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Generate backup codes
      const codes = Array.from({ length: 8 }, () =>
        Math.random().toString(36).substring(2, 8).toUpperCase()
      )
      setBackupCodes(codes)
      setTwoFactorEnabled(true)
      setSetup2FAStep(3)
      setSubmitStatus('success')
    } catch (error) {
      console.error('Error verifying 2FA:', error)
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDisable2FA = async () => {
    if (!confirm('Are you sure you want to disable Two-Factor Authentication? This will make your account less secure.')) {
      return
    }

    setIsSubmitting(true)
    try {
      // In real app, disable 2FA on your backend
      await new Promise(resolve => setTimeout(resolve, 1000))

      setTwoFactorEnabled(false)
      setSubmitStatus('success')
      setTimeout(() => setSubmitStatus('idle'), 3000)
    } catch (error) {
      console.error('Error disabling 2FA:', error)
      setSubmitStatus('error')
      setTimeout(() => setSubmitStatus('idle'), 3000)
    } finally {
      setIsSubmitting(false)
    }
  }

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'settings', label: 'Settings', icon: Settings },
    { id: 'activity', label: 'Activity', icon: Heart },
    { id: 'privacy', label: 'Privacy', icon: Shield },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-green-50 to-purple-50 dark:from-blue-900/20 dark:via-green-900/20 dark:to-purple-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <User className="h-16 w-16 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              My Account
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Manage your profile, settings, and spiritual learning journey
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Tab Navigation */}
        <div className="flex flex-wrap justify-center mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* Tab Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          {activeTab === 'profile' && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Profile Information</h2>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Display Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Your display name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Bio
                  </label>
                  <textarea
                    rows={4}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Tell us about your spiritual journey..."
                  />
                </div>
                <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Save Changes
                </button>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Account Settings</h2>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Bell className="h-5 w-5 text-gray-600 dark:text-gray-300" />
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">Email Notifications</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300">Receive updates about new content</p>
                    </div>
                  </div>
                  <input type="checkbox" className="toggle" />
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Eye className="h-5 w-5 text-gray-600 dark:text-gray-300" />
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">Public Profile</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300">Make your profile visible to others</p>
                    </div>
                  </div>
                  <input type="checkbox" className="toggle" />
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Lock className="h-5 w-5 text-gray-600 dark:text-gray-300" />
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">{t('2fa.title')}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{t('2fa.description')}</p>
                    </div>
                  </div>
                  {twoFactorEnabled ? (
                    <div className="flex items-center space-x-2">
                      <span className="text-green-600 text-sm font-medium">{t('2fa.enabled')}</span>
                      <Button
                        onClick={handleDisable2FA}
                        variant="outline"
                        size="sm"
                        disabled={isSubmitting}
                      >
                        {t('2fa.disable')}
                      </Button>
                    </div>
                  ) : (
                    <Button
                      onClick={handleEnable2FA}
                      variant="outline"
                      size="sm"
                      disabled={isSubmitting}
                    >
                      {t('2fa.enable')}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'activity' && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Recent Activity</h2>
              <div className="space-y-4">
                <div className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <BookOpen className="h-8 w-8 text-blue-600" />
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">Completed: Heart Development Course</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <MessageCircle className="h-8 w-8 text-green-600" />
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">Posted in Natural Healing Forum</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">1 day ago</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <Award className="h-8 w-8 text-yellow-600" />
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">Earned: Wisdom Seeker Badge</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">3 days ago</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'privacy' && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Privacy Settings</h2>

              {/* Status Messages */}
              {submitStatus === 'success' && (
                <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-green-800 dark:text-green-300">Action completed successfully!</span>
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  <span className="text-red-800 dark:text-red-300">An error occurred. Please try again.</span>
                </div>
              )}

              <div className="space-y-6">
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2">
                    Data Protection
                  </h3>
                  <p className="text-blue-700 dark:text-blue-300 text-sm mb-4">
                    Your privacy is important to us. We follow GDPR guidelines and only collect necessary information.
                  </p>
                  <div className="space-y-3">
                    <Button
                      onClick={handleDownloadData}
                      variant="outline"
                      className="w-full justify-start"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download My Data
                    </Button>

                    <Button
                      onClick={() => setShowDeleteConfirm(true)}
                      variant="outline"
                      className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete My Account
                    </Button>

                    <Button
                      onClick={() => setShowCookieSettings(true)}
                      variant="outline"
                      className="w-full justify-start"
                    >
                      <Cookie className="h-4 w-4 mr-2" />
                      Manage Cookie Preferences
                    </Button>

                    <Button
                      onClick={() => setShowContactForm(true)}
                      variant="outline"
                      className="w-full justify-start"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Send Us a Message
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Delete Account Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="h-8 w-8 text-red-600" />
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">Delete Account</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.
            </p>
            <div className="flex space-x-3">
              <Button
                onClick={() => setShowDeleteConfirm(false)}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleDeleteAccount}
                variant="destructive"
                className="flex-1"
              >
                Delete Account
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Cookie Settings Modal */}
      {showCookieSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">Cookie Preferences</h3>
              <Button
                onClick={() => setShowCookieSettings(false)}
                variant="ghost"
                size="sm"
              >
                ✕
              </Button>
            </div>

            <div className="space-y-4">
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-gray-900 dark:text-white">Necessary Cookies</h4>
                  <span className="text-sm text-green-600">Always Active</span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Required for basic site functionality and security.
                </p>
              </div>

              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-gray-900 dark:text-white">Analytics Cookies</h4>
                  <input type="checkbox" className="toggle" />
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Help us understand how visitors interact with our website.
                </p>
              </div>

              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-gray-900 dark:text-white">Functional Cookies</h4>
                  <input type="checkbox" className="toggle" />
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Enable enhanced functionality and personalization.
                </p>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                onClick={() => setShowCookieSettings(false)}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  // Save cookie preferences
                  localStorage.setItem('cookie-preferences', JSON.stringify({
                    necessary: true,
                    analytics: true,
                    functional: true
                  }))
                  setShowCookieSettings(false)
                  setSubmitStatus('success')
                  setTimeout(() => setSubmitStatus('idle'), 3000)
                }}
                className="flex-1"
              >
                Save Preferences
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Contact Form Modal */}
      {showContactForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">Send Us a Message</h3>
              <Button
                onClick={() => setShowContactForm(false)}
                variant="ghost"
                size="sm"
              >
                ✕
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Subject
                </label>
                <input
                  type="text"
                  value={contactSubject}
                  onChange={(e) => setContactSubject(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="What's this about?"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Message
                </label>
                <textarea
                  value={contactMessage}
                  onChange={(e) => setContactMessage(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Your message..."
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                onClick={() => setShowContactForm(false)}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSendMessage}
                disabled={isSubmitting || !contactMessage.trim() || !contactSubject.trim()}
                className="flex-1"
              >
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 2FA Setup Modal */}
      {show2FASetup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                {t('2fa.setup.title')}
              </h3>
              <Button
                onClick={() => {
                  setShow2FASetup(false)
                  setSetup2FAStep(1)
                  setVerificationCode('')
                }}
                variant="ghost"
                size="sm"
              >
                ✕
              </Button>
            </div>

            {setup2FAStep === 1 && (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                    <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-2">
                      Step 1: Install an Authenticator App
                    </h4>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">
                      Download Google Authenticator, Authy, or any TOTP-compatible app on your phone.
                    </p>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                    <h4 className="font-semibold text-gray-800 dark:text-gray-300 mb-2">
                      Step 2: Scan QR Code
                    </h4>
                    <div className="bg-white p-4 rounded-lg inline-block">
                      {/* QR Code placeholder - in real app, use a QR code library */}
                      <div className="w-48 h-48 bg-gray-200 dark:bg-gray-600 flex items-center justify-center rounded">
                        <div className="text-center">
                          <div className="text-4xl mb-2">📱</div>
                          <p className="text-sm text-gray-600 dark:text-gray-300">QR Code</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Scan with your authenticator app
                          </p>
                        </div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">
                      Can't scan? Enter this code manually: <br />
                      <code className="bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded text-xs">
                        JBSWY3DPEHPK3PXP
                      </code>
                    </p>
                  </div>
                </div>

                <Button
                  onClick={() => setSetup2FAStep(2)}
                  className="w-full"
                >
                  I've Added the Account
                </Button>
              </div>
            )}

            {setup2FAStep === 2 && (
              <div className="space-y-4">
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <h4 className="font-semibold text-green-800 dark:text-green-300 mb-2">
                    Step 3: Verify Setup
                  </h4>
                  <p className="text-green-700 dark:text-green-300 text-sm">
                    Enter the 6-digit code from your authenticator app to complete setup.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Verification Code
                  </label>
                  <input
                    type="text"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-center text-lg tracking-widest"
                    placeholder="000000"
                    maxLength={6}
                  />
                </div>

                <div className="flex space-x-3">
                  <Button
                    onClick={() => setSetup2FAStep(1)}
                    variant="outline"
                    className="flex-1"
                  >
                    Back
                  </Button>
                  <Button
                    onClick={handleVerify2FA}
                    disabled={isSubmitting || verificationCode.length !== 6}
                    className="flex-1"
                  >
                    {isSubmitting ? 'Verifying...' : 'Verify & Enable'}
                  </Button>
                </div>
              </div>
            )}

            {setup2FAStep === 3 && (
              <div className="space-y-4">
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <h4 className="font-semibold text-green-800 dark:text-green-300">
                      2FA Successfully Enabled!
                    </h4>
                  </div>
                  <p className="text-green-700 dark:text-green-300 text-sm">
                    Your account is now protected with two-factor authentication.
                  </p>
                </div>

                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <h4 className="font-semibold text-yellow-800 dark:text-yellow-300 mb-2">
                    Save Your Backup Codes
                  </h4>
                  <p className="text-yellow-700 dark:text-yellow-300 text-sm mb-3">
                    Store these codes in a safe place. You can use them to access your account if you lose your phone.
                  </p>
                  <div className="grid grid-cols-2 gap-2 text-sm font-mono">
                    {backupCodes.map((code, index) => (
                      <div key={index} className="bg-white dark:bg-gray-700 p-2 rounded border">
                        {code}
                      </div>
                    ))}
                  </div>
                  <Button
                    onClick={() => {
                      const codesText = backupCodes.join('\n')
                      const blob = new Blob([codesText], { type: 'text/plain' })
                      const url = URL.createObjectURL(blob)
                      const a = document.createElement('a')
                      a.href = url
                      a.download = 'light-upon-light-backup-codes.txt'
                      document.body.appendChild(a)
                      a.click()
                      document.body.removeChild(a)
                      URL.revokeObjectURL(url)
                    }}
                    variant="outline"
                    size="sm"
                    className="w-full mt-3"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Backup Codes
                  </Button>
                </div>

                <Button
                  onClick={() => {
                    setShow2FASetup(false)
                    setSetup2FAStep(1)
                    setVerificationCode('')
                  }}
                  className="w-full"
                >
                  Complete Setup
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}