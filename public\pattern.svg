<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="geometric-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
      <!-- Islamic geometric pattern -->
      <g fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.3">
        <!-- Octagon -->
        <polygon points="30,5 45,15 45,45 30,55 15,45 15,15" />
        
        <!-- Inner star -->
        <polygon points="30,15 35,25 30,35 25,25" />
        
        <!-- Corner decorations -->
        <circle cx="15" cy="15" r="3" />
        <circle cx="45" cy="15" r="3" />
        <circle cx="15" cy="45" r="3" />
        <circle cx="45" cy="45" r="3" />
        
        <!-- Connecting lines -->
        <line x1="0" y1="30" x2="60" y2="30" />
        <line x1="30" y1="0" x2="30" y2="60" />
      </g>
    </pattern>
  </defs>
  
  <rect width="100%" height="100%" fill="url(#geometric-pattern)" />
</svg>
