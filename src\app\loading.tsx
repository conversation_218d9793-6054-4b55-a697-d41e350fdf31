import { Loader2, Heart } from 'lucide-react'

export default function Loading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
      <div className="text-center">
        {/* Loading Animation */}
        <div className="relative mb-8">
          {/* Outer spinning ring */}
          <div className="w-16 h-16 border-4 border-blue-200 dark:border-blue-800 border-t-blue-600 dark:border-t-blue-400 rounded-full animate-spin mx-auto"></div>
          
          {/* Inner heart icon */}
          <div className="absolute inset-0 flex items-center justify-center">
            <Heart className="h-6 w-6 text-blue-600 dark:text-blue-400 animate-pulse" />
          </div>
        </div>

        {/* Loading Text */}
        <div className="space-y-2">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Loading...
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Preparing your spiritual journey
          </p>
        </div>

        {/* Progress Dots */}
        <div className="flex justify-center space-x-2 mt-6">
          <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>

        {/* Inspirational Quote */}
        <div className="mt-8 max-w-md mx-auto">
          <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <p className="text-sm text-gray-600 dark:text-gray-300 italic">
              "And whoever fears Allah - He will make for him a way out."
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Quran 65:2
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
