'use client'

import { useState } from 'react'
import { Button } from './ui/button'
import { ChevronDown, ChevronUp, Eye } from 'lucide-react'
import { useLanguage } from './language-provider'

interface SeeAllButtonProps {
  items: any[]
  initialCount?: number
  children: (items: any[], showAll: boolean) => React.ReactNode
  className?: string
  buttonText?: string
  showAllText?: string
  showLessText?: string
}

export function SeeAllButton({ 
  items, 
  initialCount = 3, 
  children, 
  className = '',
  buttonText,
  showAllText,
  showLessText
}: SeeAllButtonProps) {
  const [showAll, setShowAll] = useState(false)
  const { t } = useLanguage()

  const displayItems = showAll ? items : items.slice(0, initialCount)
  const hasMore = items.length > initialCount

  const defaultButtonText = buttonText || t('common.view') || 'View'
  const defaultShowAllText = showAllText || `${t('common.view')} All (${items.length})` || `View All (${items.length})`
  const defaultShowLessText = showLessText || t('common.showless') || 'Show Less'

  return (
    <div className={className}>
      {children(displayItems, showAll)}
      
      {hasMore && (
        <div className="mt-6 text-center">
          <Button
            variant="outline"
            onClick={() => setShowAll(!showAll)}
            className="flex items-center space-x-2"
          >
            <Eye className="h-4 w-4" />
            <span>{showAll ? defaultShowLessText : defaultShowAllText}</span>
            {showAll ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      )}
    </div>
  )
}

// Specialized components for different content types
export function SeeAllPosts({ posts, className = '' }: { posts: any[], className?: string }) {
  const { t } = useLanguage()
  
  return (
    <SeeAllButton
      items={posts}
      initialCount={5}
      className={className}
      buttonText={t('forum.viewall') || 'View All Posts'}
    >
      {(displayPosts) => (
        <div className="space-y-4">
          {displayPosts.map((post: any, index: number) => (
            <div key={post.id || index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {post.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-3">
                    {post.content}
                  </p>
                </div>
                {post.isPinned && (
                  <span className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded text-xs">
                    Pinned
                  </span>
                )}
              </div>
              <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center space-x-4">
                  <span>{post.author}</span>
                  <span>{post.category}</span>
                  <span>{post.createdAt?.toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-4">
                  <span>👍 {post.likes || 0}</span>
                  <span>💬 {post.replies?.length || 0}</span>
                  <span>👁 {post.views || 0}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </SeeAllButton>
  )
}

export function SeeAllStreams({ streams, className = '' }: { streams: any[], className?: string }) {
  const { t } = useLanguage()
  
  return (
    <SeeAllButton
      items={streams}
      initialCount={6}
      className={className}
      buttonText={t('live.viewallstreams') || 'View All Streams'}
    >
      {(displayStreams) => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayStreams.map((stream: any, index: number) => (
            <div key={stream.id || index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
              <div className="aspect-video bg-gray-200 dark:bg-gray-700 relative">
                <img 
                  src={stream.thumbnail || '/api/placeholder/320/180'} 
                  alt={stream.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute bottom-2 right-2 bg-black/80 text-white px-2 py-1 rounded text-xs">
                  {stream.duration}
                </div>
                {stream.isLive && (
                  <div className="absolute top-2 left-2 bg-red-600 text-white px-2 py-1 rounded text-xs font-medium">
                    LIVE
                  </div>
                )}
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                  {stream.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-3 line-clamp-2">
                  {stream.description}
                </p>
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                  <span>{stream.views} views</span>
                  <span>{stream.date?.toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </SeeAllButton>
  )
}

export function SeeAllEvents({ events, className = '' }: { events: any[], className?: string }) {
  const { t } = useLanguage()
  
  return (
    <SeeAllButton
      items={events}
      initialCount={4}
      className={className}
      buttonText={t('community.viewallevents') || 'View All Events'}
    >
      {(displayEvents) => (
        <div className="space-y-4">
          {displayEvents.map((event: any, index: number) => (
            <div key={event.id || index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {event.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                    {event.description}
                  </p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                    <span>📅 {event.date} at {event.time}</span>
                    <span>⏱ {event.duration}</span>
                    <span>👥 {event.attendees}/{event.maxAttendees}</span>
                  </div>
                </div>
                <span className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-sm">
                  {event.type}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  {event.instructor} • {event.category}
                </span>
                <Button size="sm">
                  {t('community.joinevent') || 'Join Event'}
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </SeeAllButton>
  )
}

export function SeeAllAchievements({ achievements, className = '' }: { achievements: any[], className?: string }) {
  const { t } = useLanguage()
  
  return (
    <SeeAllButton
      items={achievements}
      initialCount={6}
      className={className}
      buttonText={t('community.viewallachievements') || 'View All Achievements'}
    >
      {(displayAchievements) => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayAchievements.map((achievement: any, index: number) => (
            <div key={achievement.id || index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
              <div className="text-4xl mb-4">{achievement.icon}</div>
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                {achievement.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                {achievement.description}
              </p>
              <div className="flex justify-between items-center mb-3">
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  achievement.rarity === 'Common' ? 'bg-gray-100 text-gray-800' :
                  achievement.rarity === 'Uncommon' ? 'bg-green-100 text-green-800' :
                  achievement.rarity === 'Rare' ? 'bg-blue-100 text-blue-800' :
                  'bg-purple-100 text-purple-800'
                }`}>
                  {achievement.rarity}
                </span>
                <span className="text-yellow-600 font-bold">{achievement.points} pts</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${achievement.progress || 0}%` }}
                ></div>
              </div>
              <div className="mt-2 text-xs text-gray-600 dark:text-gray-300">
                Progress: {achievement.progress || 0}%
              </div>
            </div>
          ))}
        </div>
      )}
    </SeeAllButton>
  )
}
