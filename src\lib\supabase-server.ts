import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

// For server components
export const createSupabaseServerClient = () => {
  // During build time, return a mock client if env vars are not available
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    console.warn('Supabase environment variables not found, using placeholder client for build')
    return createServerClient(supabaseUrl, supabaseAnonKey, {
      cookies: {
        get() { return undefined },
      },
    })
  }

  const cookieStore = cookies()
  return createServerClient(supabaseUrl, supabase<PERSON><PERSON><PERSON><PERSON>, {
    cookies: {
      get(name: string) {
        return cookieStore.get(name)?.value
      },
    },
  })
}
