/**
 * ACCESS CONTROL & SECURITY MIDDLEWARE
 * 
 * This module provides secure access control for pages and functions.
 * Only loads necessary code based on user permissions.
 */

import { getUserPermissions } from './stream-permissions';

export interface AccessLevel {
  requiresAuth: boolean;
  requiresAdmin: boolean;
  requiresBroadcast: boolean;
  allowedRoles: string[];
  restrictedEmails?: string[];
}

export interface PageAccess {
  canView: boolean;
  canInteract: boolean;
  canModerate: boolean;
  canBroadcast: boolean;
  canAdmin: boolean;
  loadLevel: 'public' | 'user' | 'moderator' | 'admin';
}

/**
 * Define access levels for different pages/components
 */
export const ACCESS_LEVELS: Record<string, AccessLevel> = {
  // Public pages - no restrictions
  home: {
    requiresAuth: false,
    requiresAdmin: false,
    requiresBroadcast: false,
    allowedRoles: ['*']
  },
  
  // Live stream viewing - public can view, auth can chat
  'live-view': {
    requiresAuth: false,
    requiresAdmin: false,
    requiresBroadcast: false,
    allowedRoles: ['*']
  },
  
  // Live stream broadcasting - restricted
  'live-broadcast': {
    requiresAuth: true,
    requiresAdmin: false,
    requiresBroadcast: true,
    allowedRoles: ['full_admin', 'guest_broadcaster']
  },
  
  // Live stream admin panel - admin only
  'live-admin': {
    requiresAuth: true,
    requiresAdmin: true,
    requiresBroadcast: false,
    allowedRoles: ['full_admin'],
    restrictedEmails: ['<EMAIL>']
  },
  
  // Forum - public read, auth write
  forum: {
    requiresAuth: false,
    requiresAdmin: false,
    requiresBroadcast: false,
    allowedRoles: ['*']
  },
  
  // Admin pages - admin only
  admin: {
    requiresAuth: true,
    requiresAdmin: true,
    requiresBroadcast: false,
    allowedRoles: ['full_admin'],
    restrictedEmails: ['<EMAIL>']
  }
};

/**
 * Check if user has access to a specific page/component
 */
export function checkAccess(
  pageKey: string, 
  userEmail?: string | null
): PageAccess {
  const accessLevel = ACCESS_LEVELS[pageKey];
  
  if (!accessLevel) {
    // Default to public access if not defined
    return {
      canView: true,
      canInteract: false,
      canModerate: false,
      canBroadcast: false,
      canAdmin: false,
      loadLevel: 'public'
    };
  }

  const permissions = getUserPermissions(userEmail);
  
  // Check if user has required role
  const hasRequiredRole = accessLevel.allowedRoles.includes('*') || 
                         accessLevel.allowedRoles.includes(permissions.role);
  
  // Check email restrictions
  const hasEmailAccess = !accessLevel.restrictedEmails || 
                        accessLevel.restrictedEmails.includes(userEmail || '');
  
  // Determine access levels
  const canView = hasRequiredRole && (!accessLevel.requiresAuth || !!userEmail);
  const canInteract = canView && !!userEmail;
  const canModerate = canInteract && permissions.canModerateChat;
  const canBroadcast = canInteract && permissions.canBroadcast;
  const canAdmin = canInteract && permissions.canManageSite && hasEmailAccess;
  
  // Determine load level for code splitting
  let loadLevel: 'public' | 'user' | 'moderator' | 'admin' = 'public';
  
  if (canAdmin) {
    loadLevel = 'admin';
  } else if (canModerate || canBroadcast) {
    loadLevel = 'moderator';
  } else if (canInteract) {
    loadLevel = 'user';
  }
  
  return {
    canView,
    canInteract,
    canModerate,
    canBroadcast,
    canAdmin,
    loadLevel
  };
}

/**
 * Lazy load components based on access level
 */
export async function loadComponentByAccess(
  componentName: string, 
  accessLevel: 'public' | 'user' | 'moderator' | 'admin'
) {
  try {
    switch (accessLevel) {
      case 'admin':
        // Load full admin components
        if (componentName === 'LiveStreamAdmin') {
          return await import('@/components/live-stream-admin');
        }
        break;
        
      case 'moderator':
        // Load moderator components
        if (componentName === 'ModerationPanel') {
          return await import('@/components/moderation-panel');
        }
        break;
        
      case 'user':
        // Load user interaction components
        if (componentName === 'ChatPanel') {
          return await import('@/components/chat-panel');
        }
        break;
        
      case 'public':
      default:
        // Load only public viewing components
        return null;
    }
  } catch (error) {
    console.error(`Failed to load component ${componentName}:`, error);
    return null;
  }
}

/**
 * Security middleware for API routes
 */
export function withAuth(handler: Function, requiredAccess: AccessLevel) {
  return async (req: any, res: any) => {
    try {
      // Get user from request (implement based on your auth system)
      const userEmail = req.user?.email || req.headers['x-user-email'];
      
      const access = checkAccess('api', userEmail);
      
      if (!access.canView) {
        return res.status(403).json({ error: 'Access denied' });
      }
      
      if (requiredAccess.requiresAdmin && !access.canAdmin) {
        return res.status(403).json({ error: 'Admin access required' });
      }
      
      if (requiredAccess.requiresBroadcast && !access.canBroadcast) {
        return res.status(403).json({ error: 'Broadcast permission required' });
      }
      
      return handler(req, res);
    } catch (error) {
      console.error('Auth middleware error:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
}

/**
 * Rate limiting for different access levels
 */
export const RATE_LIMITS = {
  public: {
    requests: 100,
    window: 60000 // 1 minute
  },
  user: {
    requests: 500,
    window: 60000
  },
  moderator: {
    requests: 1000,
    window: 60000
  },
  admin: {
    requests: 5000,
    window: 60000
  }
};

/**
 * Get rate limit for user
 */
export function getRateLimit(userEmail?: string | null) {
  const permissions = getUserPermissions(userEmail);
  
  if (permissions.canManageSite) return RATE_LIMITS.admin;
  if (permissions.canModerateChat) return RATE_LIMITS.moderator;
  if (userEmail) return RATE_LIMITS.user;
  return RATE_LIMITS.public;
}

/**
 * Log access attempts for security monitoring
 */
export function logAccess(
  pageKey: string,
  userEmail: string | null,
  access: PageAccess,
  success: boolean
) {
  const logData = {
    timestamp: new Date().toISOString(),
    page: pageKey,
    user: userEmail || 'anonymous',
    access: access.loadLevel,
    success,
    permissions: {
      canView: access.canView,
      canInteract: access.canInteract,
      canModerate: access.canModerate,
      canBroadcast: access.canBroadcast,
      canAdmin: access.canAdmin
    }
  };
  
  // In production, send to monitoring service
  console.log('Access Log:', logData);
}
