{"name": "lightuponlight", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@supabase/supabase-js": "^2.38.4", "@supabase/ssr": "^0.1.0", "next-auth": "^4.24.5", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "lucide-react": "^0.294.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "next-intl": "^3.3.0", "framer-motion": "^10.16.16", "react-player": "^2.13.0", "socket.io-client": "^4.7.4", "date-fns": "^2.30.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "js-cookie": "^3.0.5", "@radix-ui/react-slot": "^1.0.2"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/js-cookie": "^3.0.6", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "eslint": "^8", "eslint-config-next": "14.0.4"}}