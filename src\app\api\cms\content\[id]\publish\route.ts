import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { CMSService } from '@/lib/cms-service';

// POST /api/cms/content/[id]/publish - Publish content
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if we're in build mode
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      return NextResponse.json(
        { success: false, error: 'Service unavailable during build' },
        { status: 503 }
      );
    }

    const supabase = createSupabaseServerClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin/moderator
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || !['admin', 'moderator'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { scheduled_at } = body;

    // Determine status and publish time
    const now = new Date().toISOString();
    const status = scheduled_at && new Date(scheduled_at) > new Date() ? 'scheduled' : 'published';
    const published_at = status === 'published' ? now : null;

    // Update content status
    const { data, error } = await supabase
      .from('cms_content')
      .update({
        status,
        published_at,
        scheduled_at: scheduled_at || null,
        updated_at: now,
      })
      .eq('id', params.id)
      .select()
      .single();

    if (error) {
      console.error('Error publishing content:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to publish content' },
        { status: 500 }
      );
    }

    // Clear caches
    CMSService.clearAllCaches();

    const message = status === 'scheduled' 
      ? `Content scheduled for publication on ${new Date(scheduled_at).toLocaleString()}`
      : 'Content published successfully';

    return NextResponse.json({
      success: true,
      data,
      message,
    });
  } catch (error) {
    console.error('Error in POST /api/cms/content/[id]/publish:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to publish content' },
      { status: 500 }
    );
  }
}

// DELETE /api/cms/content/[id]/publish - Unpublish content
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin/moderator
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || !['admin', 'moderator'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Update content to draft status
    const { data, error } = await supabase
      .from('cms_content')
      .update({
        status: 'draft',
        published_at: null,
        scheduled_at: null,
        updated_at: new Date().toISOString(),
      })
      .eq('id', params.id)
      .select()
      .single();

    if (error) {
      console.error('Error unpublishing content:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to unpublish content' },
        { status: 500 }
      );
    }

    // Clear caches
    CMSService.clearAllCaches();

    return NextResponse.json({
      success: true,
      data,
      message: 'Content unpublished successfully',
    });
  } catch (error) {
    console.error('Error in DELETE /api/cms/content/[id]/publish:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to unpublish content' },
      { status: 500 }
    );
  }
}
