// Global type definitions for the Light Upon Light platform

declare global {
  interface Window {
    gtag?: (
      command: 'config' | 'event' | 'js' | 'set',
      targetId: string,
      config?: Record<string, any>
    ) => void
    dataLayer?: any[]
  }
}

// Environment Variables Type Safety
declare namespace NodeJS {
  interface ProcessEnv {
    // Required Environment Variables
    NEXT_PUBLIC_SUPABASE_URL: string
    NEXT_PUBLIC_SUPABASE_ANON_KEY: string
    
    // Optional Environment Variables
    NEXT_PUBLIC_SITE_URL?: string
    NEXT_PUBLIC_GA_ID?: string
    SUPABASE_SERVICE_ROLE_KEY?: string
    SENTRY_DSN?: string
    ADMIN_EMAILS?: string
    NODE_ENV: 'development' | 'production' | 'test'
  }
}

// Supabase Database Types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: 'user' | 'moderator' | 'admin'
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'user' | 'moderator' | 'admin'
          is_active?: boolean
        }
        Update: {
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'user' | 'moderator' | 'admin'
          is_active?: boolean
          updated_at?: string
        }
      }
      forum_posts: {
        Row: {
          id: string
          title: string
          content: string
          author_id: string
          category_id: string
          is_pinned: boolean
          is_locked: boolean
          view_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          title: string
          content: string
          author_id: string
          category_id: string
          is_pinned?: boolean
          is_locked?: boolean
        }
        Update: {
          title?: string
          content?: string
          is_pinned?: boolean
          is_locked?: boolean
          view_count?: number
          updated_at?: string
        }
      }
      chat_messages: {
        Row: {
          id: string
          content: string
          user_id: string
          stream_id: string | null
          is_deleted: boolean
          created_at: string
        }
        Insert: {
          content: string
          user_id: string
          stream_id?: string | null
        }
        Update: {
          content?: string
          is_deleted?: boolean
        }
      }
      live_streams: {
        Row: {
          id: string
          title: string
          description: string | null
          streamer_id: string
          is_active: boolean
          started_at: string | null
          ended_at: string | null
          viewer_count: number
          created_at: string
        }
        Insert: {
          title: string
          description?: string | null
          streamer_id: string
        }
        Update: {
          title?: string
          description?: string | null
          is_active?: boolean
          started_at?: string | null
          ended_at?: string | null
          viewer_count?: number
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      is_admin: {
        Args: { user_id: string }
        Returns: boolean
      }
      is_moderator: {
        Args: { user_id: string }
        Returns: boolean
      }
    }
    Enums: {
      user_role: 'user' | 'moderator' | 'admin'
      request_type: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction'
    }
  }
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// User Types
export interface User {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  role: 'user' | 'moderator' | 'admin'
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface UserPermissions {
  isAdmin: boolean
  isModerator: boolean
  canModerate: boolean
  canStream: boolean
  canDeleteMessages: boolean
  canBanUsers: boolean
}

// Forum Types
export interface ForumPost {
  id: string
  title: string
  content: string
  author_id: string
  author?: User
  category_id: string
  category?: ForumCategory
  is_pinned: boolean
  is_locked: boolean
  view_count: number
  reply_count?: number
  created_at: string
  updated_at: string
}

export interface ForumCategory {
  id: string
  name: string
  description: string | null
  color: string
  icon: string | null
  post_count: number
  is_active: boolean
  created_at: string
}

export interface ForumReply {
  id: string
  content: string
  post_id: string
  author_id: string
  author?: User
  parent_id: string | null
  is_deleted: boolean
  created_at: string
  updated_at: string
}

// Chat Types
export interface ChatMessage {
  id: string
  content: string
  user_id: string
  user?: User
  stream_id: string | null
  is_deleted: boolean
  created_at: string
}

// Live Stream Types
export interface LiveStream {
  id: string
  title: string
  description: string | null
  streamer_id: string
  streamer?: User
  is_active: boolean
  started_at: string | null
  ended_at: string | null
  viewer_count: number
  created_at: string
}

// Contact Form Types
export interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
  source: 'contact' | 'account' | 'footer'
}

export interface GDPRRequestData {
  name: string
  email: string
  requestType: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction'
  description: string
}

// Language Types
export type SupportedLanguage = 'en' | 'ar' | 'fr' | 'es' | 'ur' | 'sv'

export interface LanguageOption {
  code: SupportedLanguage
  name: string
  nativeName: string
  flag: string
  rtl: boolean
}

// Theme Types
export type Theme = 'light' | 'dark' | 'system'

// Navigation Types
export interface NavigationItem {
  name: string
  href: string
  icon?: any
  children?: NavigationItem[]
  requiresAuth?: boolean
  adminOnly?: boolean
}

// Analytics Types
export interface AnalyticsEvent {
  event: string
  category?: string
  label?: string
  value?: number
  user_id?: string
  session_id?: string
  timestamp?: string
}

// Error Types
export interface AppError extends Error {
  code?: string
  statusCode?: number
  details?: any
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// Component Props Types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface PageProps {
  params: { [key: string]: string | string[] | undefined }
  searchParams: { [key: string]: string | string[] | undefined }
}

// Export empty object to make this a module
export {}
