# Light Upon Light - Database Setup Guide

This guide will help you set up the database for the Light Upon Light website with full read/write functionality.

## Prerequisites

- Supabase account (recommended) or PostgreSQL database
- Node.js and npm/yarn installed
- Basic knowledge of SQL and database management

## Option 1: Supabase Setup (Recommended)

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and create an account
2. Create a new project
3. Wait for the project to be fully provisioned
4. Note down your project URL and anon key from the project settings

### 2. Run Database Schema

1. Go to the SQL Editor in your Supabase dashboard
2. Copy the contents of `src/lib/database-schema.sql`
3. Paste and run the SQL to create all tables, functions, and policies

### 3. Configure Environment Variables

1. Copy `.env.local.example` to `.env.local`
2. Fill in your Supabase credentials:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

### 4. Enable Authentication

1. In Supabase dashboard, go to Authentication > Settings
2. Enable email authentication
3. Configure email templates if needed
4. Set up OAuth providers if desired (Google, GitHub, etc.)

### 5. Configure Row Level Security (RLS)

The schema automatically enables RLS with appropriate policies:
- Public read access for forum posts, learning paths, events
- Authenticated users can create posts and replies
- Users can only edit their own content
- Admins have full access to moderate content

## Option 2: Self-Hosted PostgreSQL

### 1. Install PostgreSQL

Install PostgreSQL 14+ on your server or local machine.

### 2. Create Database

```sql
CREATE DATABASE lightoflight;
CREATE USER lightoflight_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE lightoflight TO lightoflight_user;
```

### 3. Run Schema

```bash
psql -U lightoflight_user -d lightoflight -f src/lib/database-schema.sql
```

### 4. Configure Environment

```
DATABASE_URL=postgresql://lightoflight_user:your_password@localhost:5432/lightoflight
```

## Database Schema Overview

### Core Tables

- **users**: User accounts and profiles
- **forum_categories**: Forum category definitions
- **forum_posts**: Forum posts with metadata
- **forum_replies**: Replies to forum posts
- **post_likes**: Like/dislike tracking
- **live_streams**: Live streaming sessions
- **chat_messages**: Live stream chat messages
- **stream_archive**: Recorded stream storage
- **learning_paths**: Educational course paths
- **learning_enrollments**: User course enrollments
- **events**: Workshops and live events
- **event_registrations**: Event attendance tracking
- **achievements**: Gamification system
- **user_achievements**: User progress tracking
- **analytics_events**: Website analytics

### Key Features

1. **Forum System**
   - Categories with post counts
   - Nested replies with threading
   - Like/dislike system with user tracking
   - Admin moderation capabilities
   - View counting and statistics

2. **Live Streaming**
   - Real-time chat with role-based permissions
   - Stream recording and archival
   - User management (block/unblock, moderator assignment)
   - Viewer analytics and engagement tracking

3. **Learning Management**
   - Structured learning paths with modules
   - Progress tracking and completion rates
   - Achievement system with different rarities
   - Event registration and attendance

4. **Analytics & Insights**
   - User engagement tracking
   - Content performance metrics
   - Real-time statistics dashboard
   - Custom event tracking

## Database Functions

The schema includes several PostgreSQL functions for common operations:

- `increment_enrollment_count()`: Updates learning path enrollment counts
- `increment_event_attendees()`: Updates event attendance counts
- `increment_post_views()`: Tracks post view counts
- `get_forum_stats()`: Returns comprehensive forum statistics

## Security Features

### Row Level Security (RLS)

All sensitive tables have RLS enabled with policies that:
- Allow public read access to appropriate content
- Restrict write access to authenticated users
- Ensure users can only modify their own content
- Give admins full moderation capabilities

### Data Validation

- Email format validation
- Role-based access control
- Content length limits
- File size and type restrictions

## Performance Optimizations

### Indexes

The schema includes optimized indexes for:
- Forum post queries by category and date
- Chat message retrieval by stream
- User session management
- Analytics event tracking

### Caching Strategy

Consider implementing:
- Redis for session storage
- CDN for static assets
- Database query caching
- Real-time subscriptions for live features

## Backup and Maintenance

### Regular Backups

Set up automated backups:
```bash
# Daily backup script
pg_dump -U lightoflight_user lightoflight > backup_$(date +%Y%m%d).sql
```

### Maintenance Tasks

- Regular VACUUM and ANALYZE operations
- Monitor slow queries and optimize
- Archive old analytics data
- Clean up expired sessions

## Monitoring

### Key Metrics to Track

- Database connection pool usage
- Query performance and slow queries
- Storage usage and growth
- User activity patterns
- Error rates and failed operations

### Recommended Tools

- Supabase built-in monitoring (if using Supabase)
- pgAdmin for PostgreSQL management
- Grafana + Prometheus for custom metrics
- Application-level logging and monitoring

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Check environment variables
   - Verify database credentials
   - Ensure database is running and accessible

2. **Permission Errors**
   - Review RLS policies
   - Check user roles and permissions
   - Verify authentication status

3. **Performance Issues**
   - Analyze slow queries
   - Check index usage
   - Monitor connection pool

### Support

For database-related issues:
1. Check the application logs
2. Review Supabase dashboard (if using Supabase)
3. Test database connectivity
4. Verify schema integrity

## Migration Guide

When updating the schema:

1. Always backup before migrations
2. Test migrations on staging environment
3. Use transactions for complex changes
4. Update application code accordingly
5. Monitor for issues after deployment

This database setup provides a robust foundation for the Light Upon Light website with full CRUD operations, real-time features, and comprehensive analytics.
