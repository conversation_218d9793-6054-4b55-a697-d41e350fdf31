import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// GET /api/cms/content/[id]/versions - Get content versions
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if we're in build mode
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      return NextResponse.json(
        { success: false, error: 'Service unavailable during build' },
        { status: 503 }
      );
    }

    const supabase = createSupabaseServerClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin/moderator
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || !['admin', 'moderator'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get content versions
    const { data, error } = await supabase
      .from('cms_content_versions')
      .select(`
        *,
        author:users!author_id(id, full_name, email)
      `)
      .eq('content_id', params.id)
      .order('version_number', { ascending: false });

    if (error) {
      console.error('Error fetching content versions:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch content versions' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || [],
    });
  } catch (error) {
    console.error('Error in GET /api/cms/content/[id]/versions:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch content versions' },
      { status: 500 }
    );
  }
}

// POST /api/cms/content/[id]/versions - Create new version
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin/moderator
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || !['admin', 'moderator'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await request.json();

    // Get current content
    const { data: currentContent } = await supabase
      .from('cms_content')
      .select('*')
      .eq('id', params.id)
      .single();

    if (!currentContent) {
      return NextResponse.json(
        { success: false, error: 'Content not found' },
        { status: 404 }
      );
    }

    // Get next version number
    const { data: versions } = await supabase
      .from('cms_content_versions')
      .select('version_number')
      .eq('content_id', params.id)
      .order('version_number', { ascending: false })
      .limit(1);

    const nextVersion = versions && versions.length > 0 ? versions[0].version_number + 1 : 1;

    // Create new version
    const { data, error } = await supabase
      .from('cms_content_versions')
      .insert({
        content_id: params.id,
        version_number: nextVersion,
        title: body.title || currentContent.title,
        content: body.content || currentContent.content,
        meta_title: body.meta_title || currentContent.meta_title,
        meta_description: body.meta_description || currentContent.meta_description,
        author_id: user.id,
        change_summary: body.change_summary || `Version ${nextVersion}`,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating content version:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create content version' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Content version created successfully',
    });
  } catch (error) {
    console.error('Error in POST /api/cms/content/[id]/versions:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create content version' },
      { status: 500 }
    );
  }
}
