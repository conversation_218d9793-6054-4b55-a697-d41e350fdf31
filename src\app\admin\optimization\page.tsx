import { Metadata } from 'next';
import { generatePageMetadata, pagesSEOData } from '@/lib/seo-metadata';
import WebsiteOptimization from '@/components/website-optimization';
import { AnalyticsStatus } from '@/components/analytics-integration';
import { TranslationConsentStatus } from '@/components/translation-consent';

// Generate metadata for SEO
export const metadata: Metadata = generatePageMetadata({
  title: 'Website Optimization',
  description: 'Comprehensive performance, accessibility, and privacy optimization tools for Light Upon Light website.',
  keywords: 'optimization, performance, accessibility, privacy, GDPR, WCAG, Core Web Vitals',
  type: 'website'
});

export default function OptimizationPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <WebsiteOptimization />
        </div>
        <div className="lg:col-span-1 space-y-6">
          <div className="bg-gray-900 rounded-lg p-4 border border-gray-700">
            <h3 className="text-lg font-semibold text-white mb-4">Analytics Overview</h3>
            <AnalyticsStatus />
          </div>

          <div className="bg-gray-900 rounded-lg p-4 border border-gray-700">
            <h3 className="text-lg font-semibold text-white mb-4">Translation Status</h3>
            <div className="text-xs text-gray-400 mb-4">
              <p>Google Translate widget is now integrated directly into the website header.</p>
              <p>Users can select from 100+ languages using Google's simple translation interface.</p>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-700">
              <h4 className="text-sm font-medium text-gray-300 mb-2">User Consent Status:</h4>
              <TranslationConsentStatus />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
