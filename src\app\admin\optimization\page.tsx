import { Metadata } from 'next';
import { generatePageMetadata, pagesSEOData } from '@/lib/seo-metadata';
import WebsiteOptimization from '@/components/website-optimization';
import { AnalyticsStatus } from '@/components/analytics-integration';

// Generate metadata for SEO
export const metadata: Metadata = generatePageMetadata({
  title: 'Website Optimization',
  description: 'Comprehensive performance, accessibility, and privacy optimization tools for Light Upon Light website.',
  keywords: 'optimization, performance, accessibility, privacy, GDPR, WCAG, Core Web Vitals',
  type: 'website'
});

export default function OptimizationPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <WebsiteOptimization />
        </div>
        <div className="lg:col-span-1">
          <div className="bg-gray-900 rounded-lg p-4 border border-gray-700">
            <h3 className="text-lg font-semibold text-white mb-4">Analytics Overview</h3>
            <AnalyticsStatus />
          </div>
        </div>
      </div>
    </div>
  );
}
