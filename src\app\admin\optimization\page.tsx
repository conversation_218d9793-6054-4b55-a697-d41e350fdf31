import { Metadata } from 'next';
import { generatePageMetadata, pagesSEOData } from '@/lib/seo-metadata';
import WebsiteOptimization from '@/components/website-optimization';

// Generate metadata for SEO
export const metadata: Metadata = generatePageMetadata({
  title: 'Website Optimization',
  description: 'Comprehensive performance, accessibility, and privacy optimization tools for Light Upon Light website.',
  keywords: 'optimization, performance, accessibility, privacy, GDPR, WCAG, Core Web Vitals',
  type: 'website'
});

export default function OptimizationPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <WebsiteOptimization />
    </div>
  );
}
