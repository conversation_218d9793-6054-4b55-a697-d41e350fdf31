'use client'

import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/components/providers'
import { useLanguage } from '@/components/language-provider'
import { FunctionalButton, PlayPauseButton, MuteButton } from '@/components/functional-buttons'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Video, 
  Mic, 
  MicOff, 
  Camera, 
  CameraOff, 
  Users, 
  MessageCircle, 
  Send, 
  Settings, 
  Download, 
  Share2, 
  Eye, 
  Heart, 
  Star,
  Volume2,
  VolumeX,
  Play,
  Pause,
  Square,
  Circle,
  Maximize,
  Minimize,
  MoreVertical,
  Shield,
  Trash2,
  Save,
  Upload,
  AlertCircle,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff,
  Radio,
  Monitor
} from 'lucide-react'

interface ChatMessage {
  id: string
  user: string
  message: string
  timestamp: Date
  isAdmin?: boolean
  isModerator?: boolean
  isBlocked?: boolean
  userId?: string
}

interface UserRole {
  userId: string
  username: string
  email: string
  role: 'viewer' | 'moderator' | 'admin'
  joinedAt: Date
  isOnline: boolean
}

interface StreamSettings {
  quality: 'low' | 'medium' | 'high' | 'ultra'
  bitrate: number
  fps: number
  resolution: string
  audioQuality: 'low' | 'medium' | 'high'
}

interface SavedStream {
  id: string
  title: string
  date: string
  duration: string
  size: string
  thumbnail: string
  viewers: number
  likes: number
}

export default function LiveStreamPage() {
  const { user } = useAuth()
  const { t } = useLanguage()
  const videoRef = useRef<HTMLVideoElement>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const recordedChunksRef = useRef<Blob[]>([])

  // Prevent hydration issues
  const [mounted, setMounted] = useState(false)

  // Stream state
  const [isLive, setIsLive] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [viewerCount, setViewerCount] = useState(247)
  const [streamDuration, setStreamDuration] = useState(0)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('disconnected')
  const [streamTitle, setStreamTitle] = useState('Natural Healing & Wisdom Session')
  
  // Media controls
  const [isCameraOn, setIsCameraOn] = useState(false)
  const [isMicOn, setIsMicOn] = useState(false)
  const [volume, setVolume] = useState(50)
  const [isMuted, setIsMuted] = useState(false)
  const [cameraError, setCameraError] = useState('')
  const [micError, setMicError] = useState('')
  
  // Chat and User Management - Start with empty chat for real user interactions
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [chatVisible, setChatVisible] = useState(true)
  const [blockedUsers, setBlockedUsers] = useState<string[]>([])
  const [onlineUsers, setOnlineUsers] = useState<UserRole[]>([])
  const [showUserManagement, setShowUserManagement] = useState(false)
  const [selectedUser, setSelectedUser] = useState<string | null>(null)
  
  // Stream settings
  const [streamSettings, setStreamSettings] = useState<StreamSettings>({
    quality: 'high',
    bitrate: 2500,
    fps: 30,
    resolution: '1280x720',
    audioQuality: 'high'
  })
  const [showSettings, setShowSettings] = useState(false)
  
  // Admin controls (<NAME_EMAIL>)
  const isAdmin = user?.email === '<EMAIL>'
  const [savedStreams, setSavedStreams] = useState<SavedStream[]>([])

  // Set mounted state to prevent hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  // Auto-update viewer count and duration
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isLive && mounted) {
      interval = setInterval(() => {
        setStreamDuration(prev => prev + 1)
        // Simulate realistic viewer count changes
        setViewerCount(prev => {
          const change = Math.floor(Math.random() * 10) - 5
          const newCount = Math.max(100, prev + change)
          return Math.min(1000, newCount)
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isLive, mounted])

  // No auto-generated messages - only real user interactions

  // Request camera and microphone access
  const requestMediaAccess = async (): Promise<boolean> => {
    try {
      setCameraError('')
      setMicError('')
      setConnectionStatus('connecting')
      
      const constraints = {
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      }
      
      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
      }
      
      streamRef.current = stream
      setIsCameraOn(true)
      setIsMicOn(true)
      setConnectionStatus('connected')
      
      return true
    } catch (error: any) {
      console.error('Error accessing media devices:', error)
      setConnectionStatus('disconnected')
      
      if (error.name === 'NotAllowedError') {
        setCameraError('Camera/microphone access denied. Please allow permissions and try again.')
      } else if (error.name === 'NotFoundError') {
        setCameraError('No camera or microphone found. Please connect devices and try again.')
      } else {
        setCameraError('Unable to access camera/microphone. Please check your devices.')
      }
      
      return false
    }
  }

  // Start live stream
  const startStream = async () => {
    const hasAccess = await requestMediaAccess()
    if (hasAccess) {
      setIsLive(true)
      setStreamDuration(0)
      
      // Add admin message
      const adminMessage: ChatMessage = {
        id: Date.now().toString(),
        user: 'Light Upon Light Admin',
        message: '🔴 Live stream started! Welcome everyone to our healing session!',
        timestamp: new Date(),
        isAdmin: true
      }
      setChatMessages(prev => [...prev, adminMessage])
      
      alert('🔴 Live stream started! You are now broadcasting to viewers.')
    }
  }

  // Stop live stream
  const stopStream = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
    }
    
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
    }
    
    setIsLive(false)
    setIsRecording(false)
    setIsCameraOn(false)
    setIsMicOn(false)
    setConnectionStatus('disconnected')
    
    // Add end message
    const endMessage: ChatMessage = {
      id: Date.now().toString(),
      user: 'Light Upon Light Admin',
      message: '⏹️ Stream ended. Thank you all for joining! May you be blessed with healing and wisdom.',
      timestamp: new Date(),
      isAdmin: true
    }
    setChatMessages(prev => [...prev, endMessage])
    
    alert('⏹️ Stream ended. Thank you for watching!')
  }

  // Toggle camera
  const toggleCamera = () => {
    if (streamRef.current) {
      const videoTrack = streamRef.current.getVideoTracks()[0]
      if (videoTrack) {
        videoTrack.enabled = !isCameraOn
        setIsCameraOn(!isCameraOn)
      }
    }
  }

  // Toggle microphone
  const toggleMicrophone = () => {
    if (streamRef.current) {
      const audioTrack = streamRef.current.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = !isMicOn
        setIsMicOn(!isMicOn)
      }
    }
  }

  // Start/stop recording
  const toggleRecording = () => {
    if (!streamRef.current) return
    
    if (!isRecording) {
      // Start recording
      recordedChunksRef.current = []
      const mediaRecorder = new MediaRecorder(streamRef.current, {
        mimeType: 'video/webm;codecs=vp9'
      })
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunksRef.current.push(event.data)
        }
      }
      
      mediaRecorder.onstop = () => {
        const blob = new Blob(recordedChunksRef.current, { type: 'video/webm' })
        const url = URL.createObjectURL(blob)
        
        // Create download link
        const a = document.createElement('a')
        a.href = url
        a.download = `stream-${new Date().toISOString().slice(0, 19)}.webm`
        a.click()
        
        // Add to saved streams
        const newStream: SavedStream = {
          id: Date.now().toString(),
          title: streamTitle,
          date: new Date().toISOString().slice(0, 10),
          duration: formatDuration(streamDuration),
          size: `${(blob.size / (1024 * 1024 * 1024)).toFixed(1)} GB`,
          thumbnail: '/api/placeholder/200/120',
          viewers: viewerCount,
          likes: 0
        }
        
        setSavedStreams(prev => [newStream, ...prev])
        alert('🎥 Recording saved successfully!')
      }
      
      mediaRecorderRef.current = mediaRecorder
      mediaRecorder.start(1000) // Record in 1-second chunks
      setIsRecording(true)
      alert('🔴 Recording started')
    } else {
      // Stop recording
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop()
      }
      setIsRecording(false)
      alert('⏹️ Recording stopped and saved')
    }
  }

  // Send chat message
  const sendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (!newMessage.trim() || !user) return

    const currentUserRole = getUserRole(user.email)
    const currentUser = onlineUsers.find(u => u.email === user.email)

    const message: ChatMessage = {
      id: Date.now().toString(),
      user: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous',
      message: newMessage.trim(),
      timestamp: new Date(),
      isAdmin: currentUserRole === 'admin',
      isModerator: currentUserRole === 'moderator',
      userId: currentUser?.userId || Date.now().toString()
    }

    setChatMessages(prev => [...prev, message])
    setNewMessage('')
  }

  // Delete message (admin only)
  const deleteMessage = (messageId: string) => {
    if (!isAdmin) return
    setChatMessages(prev => prev.filter(msg => msg.id !== messageId))
  }

  // Block user (admin/moderator)
  const blockUser = (username: string) => {
    const currentUser = onlineUsers.find(u => u.email === user?.email)
    if (!currentUser || (currentUser.role !== 'admin' && currentUser.role !== 'moderator')) return

    setBlockedUsers(prev => [...prev, username])
    setChatMessages(prev => prev.map(msg =>
      msg.user === username ? { ...msg, isBlocked: true } : msg
    ))

    // Add system message
    const systemMessage: ChatMessage = {
      id: Date.now().toString(),
      user: 'System',
      message: `${username} has been blocked by ${currentUser.username}`,
      timestamp: new Date(),
      isAdmin: true,
      userId: 'system'
    }
    setChatMessages(prev => [...prev, systemMessage])
    alert(`User ${username} has been blocked`)
  }

  // Unblock user (admin only)
  const unblockUser = (username: string) => {
    if (!isAdmin) return
    setBlockedUsers(prev => prev.filter(u => u !== username))
    setChatMessages(prev => prev.map(msg =>
      msg.user === username ? { ...msg, isBlocked: false } : msg
    ))
    alert(`User ${username} has been unblocked`)
  }

  // Assign moderator role (admin only)
  const assignModerator = (userId: string) => {
    if (!isAdmin) return

    setOnlineUsers(prev => prev.map(user =>
      user.userId === userId ? { ...user, role: 'moderator' } : user
    ))

    setChatMessages(prev => prev.map(msg =>
      msg.userId === userId ? { ...msg, isModerator: true } : msg
    ))

    const user = onlineUsers.find(u => u.userId === userId)
    if (user) {
      const systemMessage: ChatMessage = {
        id: Date.now().toString(),
        user: 'System',
        message: `${user.username} has been assigned as a moderator`,
        timestamp: new Date(),
        isAdmin: true,
        userId: 'system'
      }
      setChatMessages(prev => [...prev, systemMessage])
      alert(`${user.username} has been assigned as a moderator`)
    }
  }

  // Remove moderator role (admin only)
  const removeModerator = (userId: string) => {
    if (!isAdmin) return

    setOnlineUsers(prev => prev.map(user =>
      user.userId === userId ? { ...user, role: 'viewer' } : user
    ))

    setChatMessages(prev => prev.map(msg =>
      msg.userId === userId ? { ...msg, isModerator: false } : msg
    ))

    const user = onlineUsers.find(u => u.userId === userId)
    if (user) {
      const systemMessage: ChatMessage = {
        id: Date.now().toString(),
        user: 'System',
        message: `${user.username} is no longer a moderator`,
        timestamp: new Date(),
        isAdmin: true,
        userId: 'system'
      }
      setChatMessages(prev => [...prev, systemMessage])
      alert(`${user.username} is no longer a moderator`)
    }
  }

  // Kick user from stream (admin/moderator)
  const kickUser = (userId: string) => {
    const currentUser = onlineUsers.find(u => u.email === user?.email)
    if (!currentUser || (currentUser.role !== 'admin' && currentUser.role !== 'moderator')) return

    const targetUser = onlineUsers.find(u => u.userId === userId)
    if (targetUser && targetUser.role !== 'admin') {
      setOnlineUsers(prev => prev.filter(u => u.userId !== userId))

      const systemMessage: ChatMessage = {
        id: Date.now().toString(),
        user: 'System',
        message: `${targetUser.username} has been removed from the stream`,
        timestamp: new Date(),
        isAdmin: true,
        userId: 'system'
      }
      setChatMessages(prev => [...prev, systemMessage])
      alert(`${targetUser.username} has been kicked from the stream`)
    }
  }

  // Get user role
  const getUserRole = (userEmail?: string) => {
    if (!userEmail) return 'viewer'
    const userRole = onlineUsers.find(u => u.email === userEmail)
    return userRole?.role || 'viewer'
  }

  // Check if user can moderate
  const canModerate = () => {
    const currentUserRole = getUserRole(user?.email)
    return currentUserRole === 'admin' || currentUserRole === 'moderator'
  }

  // Format duration
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return hours > 0 
      ? `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      : `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  // Download stream (admin only)
  const downloadStream = (streamId: string) => {
    if (!isAdmin) {
      alert('Only admin can download streams')
      return
    }
    
    const stream = savedStreams.find(s => s.id === streamId)
    if (stream) {
      // Simulate download
      alert(`Downloading "${stream.title}"...\nSize: ${stream.size}\nThis may take a few minutes.`)
    }
  }

  // Delete stream (admin only)
  const deleteStream = (streamId: string) => {
    if (!isAdmin) {
      alert('Only admin can delete streams')
      return
    }
    
    const stream = savedStreams.find(s => s.id === streamId)
    if (stream && confirm(`Are you sure you want to delete "${stream.title}"?`)) {
      setSavedStreams(prev => prev.filter(s => s.id !== streamId))
      alert('Stream deleted successfully')
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Video className="h-8 w-8 text-red-500" />
                {isLive && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                )}
              </div>
              <div>
                <h1 className="text-2xl font-bold">Live Stream</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-300">
                  <div className="flex items-center space-x-1">
                    <div className={`w-2 h-2 rounded-full ${
                      connectionStatus === 'connected' ? 'bg-green-500' :
                      connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' :
                      'bg-red-500'
                    }`}></div>
                    <span className="capitalize">{connectionStatus}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className={`w-2 h-2 rounded-full ${isLive ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>
                    <span>{isLive ? 'LIVE' : 'OFFLINE'}</span>
                  </div>
                  {isLive && (
                    <>
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4" />
                        <span>{viewerCount.toLocaleString()} viewers</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{formatDuration(streamDuration)}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <FunctionalButton type="share" size="sm" variant="outline">
                <Share2 className="h-4 w-4 mr-1" />
                Share Stream
              </FunctionalButton>
              {isAdmin && (
                <Button variant="outline" size="sm" onClick={() => setShowSettings(!showSettings)}>
                  <Settings className="h-4 w-4 mr-1" />
                  Settings
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Video Area */}
          <div className="lg:col-span-3">
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-0">
                <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
                  <video
                    ref={videoRef}
                    autoPlay
                    muted
                    className="w-full h-full object-cover"
                    style={{ display: isCameraOn ? 'block' : 'none' }}
                  />

                  {!isCameraOn && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
                      <div className="text-center">
                        <CameraOff className="h-16 w-16 text-gray-500 mx-auto mb-4" />
                        <p className="text-gray-400 mb-2">Camera is off</p>
                        {cameraError && (
                          <p className="text-red-400 text-sm max-w-md">{cameraError}</p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Stream Controls Overlay */}
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center justify-between bg-black bg-opacity-75 rounded-lg p-3">
                      <div className="flex items-center space-x-2">
                        {!isLive ? (
                          <Button onClick={startStream} className="bg-red-600 hover:bg-red-700">
                            <Circle className="h-4 w-4 mr-2" />
                            Go Live
                          </Button>
                        ) : (
                          <Button onClick={stopStream} variant="destructive">
                            <Square className="h-4 w-4 mr-2" />
                            End Stream
                          </Button>
                        )}

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={toggleCamera}
                          className={`${isCameraOn ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'} border-0`}
                          title={isCameraOn ? 'Turn camera off' : 'Turn camera on'}
                        >
                          {isCameraOn ? <Camera className="h-4 w-4" /> : <CameraOff className="h-4 w-4" />}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={toggleMicrophone}
                          className={`${isMicOn ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'} border-0`}
                          title={isMicOn ? 'Mute microphone' : 'Unmute microphone'}
                        >
                          {isMicOn ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                        </Button>

                        {isAdmin && isLive && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={toggleRecording}
                            className={`${isRecording ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'} border-0`}
                            title={isRecording ? 'Stop recording' : 'Start recording'}
                          >
                            <Circle className={`h-4 w-4 ${isRecording ? 'animate-pulse' : ''}`} />
                          </Button>
                        )}
                      </div>

                      <div className="flex items-center space-x-2">
                        <div className="flex items-center space-x-2">
                          <Volume2 className="h-4 w-4 text-gray-300" />
                          <input
                            type="range"
                            min="0"
                            max="100"
                            value={volume}
                            onChange={(e) => setVolume(Number(e.target.value))}
                            className="w-20 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                          />
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsMuted(!isMuted)}
                          className="border-0"
                          title={isMuted ? 'Unmute' : 'Mute'}
                        >
                          {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsFullscreen(!isFullscreen)}
                          className="border-0"
                          title="Toggle fullscreen"
                        >
                          {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Live Indicator */}
                  {isLive && (
                    <div className="absolute top-4 left-4">
                      <div className="bg-red-600 px-3 py-1 rounded-full flex items-center space-x-2">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        <span className="text-sm font-bold">LIVE</span>
                      </div>
                    </div>
                  )}

                  {/* Recording Indicator */}
                  {isRecording && (
                    <div className="absolute top-4 left-20">
                      <div className="bg-red-800 px-3 py-1 rounded-full flex items-center space-x-2">
                        <Circle className="w-2 h-2 bg-white rounded-full animate-pulse" />
                        <span className="text-sm font-bold">REC</span>
                      </div>
                    </div>
                  )}

                  {/* Viewer Count */}
                  {isLive && (
                    <div className="absolute top-4 right-4">
                      <div className="bg-black bg-opacity-50 px-3 py-1 rounded-full flex items-center space-x-2">
                        <Eye className="h-4 w-4" />
                        <span className="text-sm">{viewerCount.toLocaleString()}</span>
                      </div>
                    </div>
                  )}

                  {/* Connection Status */}
                  <div className="absolute top-16 right-4">
                    <div className={`px-3 py-1 rounded-full flex items-center space-x-2 text-xs ${
                      connectionStatus === 'connected' ? 'bg-green-600' :
                      connectionStatus === 'connecting' ? 'bg-yellow-600' :
                      'bg-red-600'
                    }`}>
                      {connectionStatus === 'connected' ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
                      <span className="capitalize">{connectionStatus}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Stream Info */}
            <Card className="mt-4 bg-gray-800 border-gray-700">
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    {isAdmin ? (
                      <input
                        type="text"
                        value={streamTitle}
                        onChange={(e) => setStreamTitle(e.target.value)}
                        className="text-xl font-bold bg-transparent border-b border-gray-600 focus:border-blue-500 outline-none w-full"
                        placeholder="Enter stream title..."
                      />
                    ) : (
                      <h2 className="text-xl font-bold">{streamTitle}</h2>
                    )}
                    <div className="text-gray-300 mt-2 space-y-2">
                      <p className="text-base">
                        🌟 <strong>Welcome to Light Upon Light Educational Sessions!</strong>
                      </p>
                      <p className="text-sm">
                        <strong>For Young Learners:</strong> Discover amazing natural remedies that have helped people feel better for thousands of years!
                        Learn simple ways to stay healthy using plants, honey, and gentle healing methods.
                      </p>
                      <p className="text-sm">
                        <strong>For Adults:</strong> Explore the scientific wisdom behind traditional healing practices including cupping therapy,
                        herbal medicine, and spiritual wellness. Understand how ancient knowledge meets modern health science.
                      </p>
                      <p className="text-sm">
                        <strong>Today's Topics:</strong> Natural healing methods, plant medicine benefits, mindful living practices,
                        and the logical wisdom found in traditional remedies that work for all ages.
                      </p>
                    </div>
                  </div>
                  <div className="ml-4 text-right">
                    <div className="text-2xl font-bold text-green-400">{viewerCount.toLocaleString()}</div>
                    <div className="text-sm text-gray-400">viewers</div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <FunctionalButton type="like" itemId="live-stream" count={89} size="sm" />
                    <FunctionalButton type="bookmark" itemId="live-stream" size="sm" />
                    <FunctionalButton type="share" size="sm" />
                    {isAdmin && (
                      <FunctionalButton type="download" size="sm" onClick={() => alert('Stream will be saved automatically')}>
                        Save Stream
                      </FunctionalButton>
                    )}
                  </div>

                  {isLive && (
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Radio className="h-4 w-4" />
                        <span>{streamSettings.quality} quality</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Monitor className="h-4 w-4" />
                        <span>{streamSettings.resolution}</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Chat Sidebar */}
          <div className="lg:col-span-1">
            <Card className="bg-gray-800 border-gray-700 h-[600px] flex flex-col">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center">
                    <MessageCircle className="h-5 w-5 mr-2" />
                    Live Chat
                    {isLive && (
                      <span className="ml-2 px-2 py-1 bg-green-600 text-xs rounded-full">
                        {chatMessages.length}
                      </span>
                    )}
                  </CardTitle>
                  <Button variant="ghost" size="sm" onClick={() => setChatVisible(!chatVisible)}>
                    {chatVisible ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
                  </Button>
                </div>
              </CardHeader>

              {chatVisible && (
                <>
                  <CardContent className="flex-1 overflow-y-auto p-4 space-y-3">
                    {chatMessages.filter(msg => !msg.isBlocked).map((msg) => (
                      <div key={msg.id} className="group">
                        <div className="flex items-start space-x-2">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <span className={`text-sm font-medium ${
                                msg.isAdmin ? 'text-red-400' : msg.isModerator ? 'text-blue-400' : 'text-gray-300'
                              }`}>
                                {msg.user}
                                {msg.isAdmin && <Shield className="h-3 w-3 text-red-400" />}
                                {msg.isModerator && <Star className="h-3 w-3 text-blue-400" />}
                              </span>
                              <span className="text-xs text-gray-500">
                                {mounted ? msg.timestamp.toLocaleTimeString() : '--:--:--'}
                              </span>
                            </div>
                            <p className="text-sm text-gray-200 mt-1 break-words">{msg.message}</p>
                          </div>
                          {canModerate() && !msg.isAdmin && msg.user !== 'System' && (
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => deleteMessage(msg.id)}
                                className="h-6 w-6 p-0"
                                title="Delete message"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => blockUser(msg.user)}
                                className="h-6 w-6 p-0"
                                title="Block user"
                              >
                                <Shield className="h-3 w-3" />
                              </Button>
                              {isAdmin && !msg.isModerator && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => assignModerator(msg.userId || '')}
                                  className="h-6 w-6 p-0"
                                  title="Make moderator"
                                >
                                  <Star className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </CardContent>

                  {/* Chat Input */}
                  <div className="p-4 border-t border-gray-700">
                    {user ? (
                      <form onSubmit={sendMessage} className="space-y-2">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-sm text-gray-400">Chatting as:</span>
                          <span className={`text-sm font-medium ${
                            getUserRole(user.email) === 'admin' ? 'text-red-400' :
                            getUserRole(user.email) === 'moderator' ? 'text-blue-400' :
                            'text-gray-300'
                          }`}>
                            {user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous'}
                            {getUserRole(user.email) === 'admin' && <Shield className="h-3 w-3 text-red-400 inline ml-1" />}
                            {getUserRole(user.email) === 'moderator' && <Star className="h-3 w-3 text-blue-400 inline ml-1" />}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs ${
                            getUserRole(user.email) === 'admin' ? 'bg-red-600 text-white' :
                            getUserRole(user.email) === 'moderator' ? 'bg-blue-600 text-white' :
                            'bg-gray-600 text-white'
                          }`}>
                            {getUserRole(user.email).toUpperCase()}
                          </span>
                        </div>
                        <div className="flex space-x-2">
                          <input
                            type="text"
                            value={newMessage}
                            onChange={(e) => setNewMessage(e.target.value)}
                            placeholder={`Type a message as ${getUserRole(user.email)}...`}
                            className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            maxLength={200}
                          />
                          <Button type="submit" size="sm" disabled={!newMessage.trim()}>
                            <Send className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>{newMessage.length}/200 characters</span>
                          {canModerate() && (
                            <span className="text-blue-400">Moderation enabled</span>
                          )}
                        </div>
                      </form>
                    ) : (
                      <div className="text-center text-gray-400 text-sm">
                        <p className="mb-2">👁️ You can watch the stream without signing in</p>
                        <p className="mb-3">💬 Sign in to participate in chat and interact</p>
                        <Button size="sm" variant="outline">
                          Sign In to Chat
                        </Button>
                      </div>
                    )}
                  </div>
                </>
              )}
            </Card>
          </div>
        </div>



        {/* Admin Panel */}
        {isAdmin && (
          <Card className="mt-6 bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2 text-red-400" />
                Admin Controls
                <span className="ml-2 px-2 py-1 bg-red-600 text-xs rounded-full">ADMIN</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                <Button variant="outline" onClick={() => setShowUserManagement(!showUserManagement)}>
                  <Users className="h-4 w-4 mr-2" />
                  Manage Users ({onlineUsers.length})
                </Button>
                <Button variant="outline" onClick={() => alert('Chat moderation panel opened')}>
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Moderate Chat
                </Button>
                <Button variant="outline" onClick={() => alert('Stream analytics opened')}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Analytics
                </Button>
                <Button variant="outline" onClick={() => setShowSettings(!showSettings)}>
                  <Settings className="h-4 w-4 mr-2" />
                  Stream Settings
                </Button>
                <Button variant="outline" onClick={() => alert('Blocked users: ' + blockedUsers.join(', '))}>
                  <Shield className="h-4 w-4 mr-2" />
                  Blocked Users ({blockedUsers.length})
                </Button>
              </div>

              {/* User Management Panel */}
              {showUserManagement && (
                <Card className="mb-6 bg-gray-700 border-gray-600">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Users className="h-5 w-5 mr-2" />
                      User Management & Moderation
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="text-center p-3 bg-gray-600 rounded-lg">
                          <div className="text-2xl font-bold text-green-400">{onlineUsers.filter(u => u.role === 'viewer').length}</div>
                          <div className="text-sm text-gray-300">Viewers</div>
                        </div>
                        <div className="text-center p-3 bg-gray-600 rounded-lg">
                          <div className="text-2xl font-bold text-blue-400">{onlineUsers.filter(u => u.role === 'moderator').length}</div>
                          <div className="text-sm text-gray-300">Moderators</div>
                        </div>
                        <div className="text-center p-3 bg-gray-600 rounded-lg">
                          <div className="text-2xl font-bold text-red-400">{blockedUsers.length}</div>
                          <div className="text-sm text-gray-300">Blocked</div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h4 className="font-semibold text-white">Online Users</h4>
                        {onlineUsers.map((user) => (
                          <div key={user.userId} className="flex items-center justify-between p-3 bg-gray-600 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                              <div>
                                <div className="flex items-center space-x-2">
                                  <span className="font-medium text-white">{user.username}</span>
                                  {user.role === 'admin' && <Shield className="h-4 w-4 text-red-400" />}
                                  {user.role === 'moderator' && <Star className="h-4 w-4 text-blue-400" />}
                                  <span className={`px-2 py-1 rounded text-xs ${
                                    user.role === 'admin' ? 'bg-red-600 text-white' :
                                    user.role === 'moderator' ? 'bg-blue-600 text-white' :
                                    'bg-gray-500 text-white'
                                  }`}>
                                    {user.role.toUpperCase()}
                                  </span>
                                </div>
                                <div className="text-sm text-gray-300">{user.email}</div>
                                <div className="text-xs text-gray-400">
                                  Joined: {mounted ? user.joinedAt.toLocaleTimeString() : '--:--:--'}
                                </div>
                              </div>
                            </div>

                            {user.role !== 'admin' && (
                              <div className="flex space-x-2">
                                {user.role === 'viewer' ? (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => assignModerator(user.userId)}
                                    className="text-blue-400 border-blue-400 hover:bg-blue-400 hover:text-white"
                                  >
                                    Make Moderator
                                  </Button>
                                ) : (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => removeModerator(user.userId)}
                                    className="text-yellow-400 border-yellow-400 hover:bg-yellow-400 hover:text-black"
                                  >
                                    Remove Moderator
                                  </Button>
                                )}

                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => blockUser(user.username)}
                                  className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                                >
                                  Block
                                </Button>

                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => kickUser(user.userId)}
                                  className="text-orange-400 border-orange-400 hover:bg-orange-400 hover:text-white"
                                >
                                  Kick
                                </Button>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>

                      {blockedUsers.length > 0 && (
                        <div className="space-y-3">
                          <h4 className="font-semibold text-white">Blocked Users</h4>
                          {blockedUsers.map((username) => (
                            <div key={username} className="flex items-center justify-between p-3 bg-red-900/20 border border-red-600 rounded-lg">
                              <div className="flex items-center space-x-3">
                                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                                <span className="text-red-300">{username}</span>
                                <span className="px-2 py-1 bg-red-600 text-white rounded text-xs">BLOCKED</span>
                              </div>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => unblockUser(username)}
                                className="text-green-400 border-green-400 hover:bg-green-400 hover:text-white"
                              >
                                Unblock
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Stream Settings */}
              {showSettings && (
                <Card className="mb-6 bg-gray-700 border-gray-600">
                  <CardHeader>
                    <CardTitle className="text-lg">Stream Settings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Video Quality</label>
                        <select
                          value={streamSettings.quality}
                          onChange={(e) => setStreamSettings(prev => ({ ...prev, quality: e.target.value as any }))}
                          className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white"
                        >
                          <option value="low">Low (480p)</option>
                          <option value="medium">Medium (720p)</option>
                          <option value="high">High (1080p)</option>
                          <option value="ultra">Ultra (4K)</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Frame Rate</label>
                        <select
                          value={streamSettings.fps}
                          onChange={(e) => setStreamSettings(prev => ({ ...prev, fps: Number(e.target.value) }))}
                          className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white"
                        >
                          <option value={24}>24 FPS</option>
                          <option value={30}>30 FPS</option>
                          <option value={60}>60 FPS</option>
                        </select>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Saved Streams */}
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <Save className="h-5 w-5 mr-2" />
                  Saved Streams ({savedStreams.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {savedStreams.map((stream) => (
                    <Card key={stream.id} className="bg-gray-700 border-gray-600">
                      <CardContent className="p-4">
                        <div className="aspect-video bg-gray-600 rounded-lg mb-3 overflow-hidden">
                          <img
                            src={stream.thumbnail}
                            alt={stream.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <h4 className="font-medium text-white mb-2 line-clamp-2">{stream.title}</h4>
                        <div className="space-y-1 text-sm text-gray-400 mb-3">
                          <div className="flex justify-between">
                            <span>Date:</span>
                            <span>{stream.date}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Duration:</span>
                            <span>{stream.duration}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Viewers:</span>
                            <span>{stream.viewers.toLocaleString()}</span>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline" onClick={() => downloadStream(stream.id)} className="flex-1">
                            <Download className="h-4 w-4 mr-1" />
                            Download
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => deleteStream(stream.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Stream Statistics */}
        {isLive && (
          <Card className="mt-6 bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                Live Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{viewerCount.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">Current Viewers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{formatDuration(streamDuration)}</div>
                  <div className="text-sm text-gray-400">Stream Duration</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{chatMessages.length}</div>
                  <div className="text-sm text-gray-400">Chat Messages</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-400">89</div>
                  <div className="text-sm text-gray-400">Likes</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Visitor Information */}
        <Card className="mt-6 bg-blue-900/20 border-blue-600">
          <CardContent className="p-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-blue-300 mb-2">
                👁️ All visitors can watch the live stream
              </h3>
              <p className="text-blue-200 mb-4">
                Sign in to participate in chat and interact with the community
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center justify-center space-x-2">
                  <Eye className="h-4 w-4 text-blue-400" />
                  <span>Watch without account</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <MessageCircle className="h-4 w-4 text-green-400" />
                  <span>Chat with account</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <Shield className="h-4 w-4 text-red-400" />
                  <span>Admin moderation</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Community Guidelines */}
        <Card className="mt-6 bg-gradient-to-r from-blue-900/20 to-purple-900/20 border-blue-600">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-300">
              <Users className="h-5 w-5 mr-2" />
              Community Guidelines & Inspiration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="font-semibold text-blue-200 flex items-center">
                  <Heart className="h-4 w-4 mr-2" />
                  Our Community Values
                </h3>
                <ul className="space-y-2 text-sm text-blue-100">
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    <span><strong>Kindness & Respect:</strong> Treat everyone with gentleness and understanding</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    <span><strong>Humble Learning:</strong> Approach knowledge with openness and humility</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    <span><strong>Peaceful Discussion:</strong> Share ideas with wisdom and patience</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    <span><strong>Natural Growth:</strong> Support each other's journey of development</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    <span><strong>Beneficial Knowledge:</strong> Focus on learning that helps and heals</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-4">
                <h3 className="font-semibold text-purple-200 flex items-center">
                  <Star className="h-4 w-4 mr-2" />
                  Inspiring Wisdom
                </h3>
                <div className="space-y-3">
                  <div className="bg-purple-900/30 p-3 rounded-lg">
                    <p className="text-sm text-purple-100 italic mb-2">
                      "And whoever is given wisdom has certainly been given much good"
                    </p>
                    <p className="text-xs text-purple-300">- Natural wisdom brings abundant blessings</p>
                  </div>

                  <div className="bg-blue-900/30 p-3 rounded-lg">
                    <p className="text-sm text-blue-100 italic mb-2">
                      "And it is He who sends down rain from heaven, and We produce thereby the vegetation of every kind"
                    </p>
                    <p className="text-xs text-blue-300">- Knowledge nourishes growth like rain nourishes plants</p>
                  </div>

                  <div className="bg-green-900/30 p-3 rounded-lg">
                    <p className="text-sm text-green-100 italic mb-2">
                      "And say: My Lord, increase me in knowledge"
                    </p>
                    <p className="text-xs text-green-300">- The beautiful prayer for continuous learning</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="border-t border-blue-700 pt-4">
              <h4 className="font-semibold text-blue-200 mb-3">Chat Guidelines</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-100">
                <div>
                  <span className="text-green-400">✓</span> Ask questions with curiosity
                </div>
                <div>
                  <span className="text-green-400">✓</span> Share beneficial insights
                </div>
                <div>
                  <span className="text-green-400">✓</span> Listen with an open heart
                </div>
                <div>
                  <span className="text-red-400">✗</span> Avoid arguments or debates
                </div>
                <div>
                  <span className="text-red-400">✗</span> No negative or harmful content
                </div>
                <div>
                  <span className="text-red-400">✗</span> Respect others' learning pace
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
