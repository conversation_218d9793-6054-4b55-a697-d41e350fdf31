<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="lightGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:0.7" />
    </radialGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="96" cy="96" r="96" fill="url(#bgGradient)"/>
  
  <!-- Central Light Source -->
  <circle cx="96" cy="96" r="40" fill="url(#lightGradient)"/>
  <circle cx="96" cy="96" r="28" fill="#fbbf24"/>
  <circle cx="96" cy="96" r="16" fill="#fef3c7"/>
  
  <!-- Light Rays -->
  <g stroke="#fbbf24" stroke-width="3" stroke-linecap="round" opacity="0.8">
    <!-- Top rays -->
    <line x1="96" y1="20" x2="96" y2="40"/>
    <line x1="135" y1="30" x2="125" y2="40"/>
    <line x1="162" y1="57" x2="152" y2="67"/>
    <line x1="172" y1="96" x2="152" y2="96"/>
    <line x1="162" y1="135" x2="152" y2="125"/>
    <line x1="135" y1="162" x2="125" y2="152"/>
    
    <!-- Bottom rays -->
    <line x1="96" y1="172" x2="96" y2="152"/>
    <line x1="57" y1="162" x2="67" y2="152"/>
    <line x1="30" y1="135" x2="40" y2="125"/>
    <line x1="20" y1="96" x2="40" y2="96"/>
    <line x1="30" y1="57" x2="40" y2="67"/>
    <line x1="57" y1="30" x2="67" y2="40"/>
  </g>
  
  <!-- Inner glow effect -->
  <circle cx="96" cy="96" r="8" fill="#fff" opacity="0.9"/>
  <circle cx="96" cy="96" r="4" fill="#fbbf24"/>
</svg>
