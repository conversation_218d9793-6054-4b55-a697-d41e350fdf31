import {
  Heart,
  Star,
  Users,
  MessageCircle,
  Calendar,
  Award,
  TrendingUp,
  CheckCircle,
} from "lucide-react";

const patientStories = [
  {
    name: "<PERSON>",
    age: 34,
    condition: "Chronic Back Pain",
    treatment: "Cupping Therapy + Natural Remedies",
    duration: "3 months",
    improvement: "85%",
    story:
      "After years of chronic back pain, I discovered cupping therapy through this platform. The scientific explanations helped me understand how it works. Combined with the natural remedies and mindfulness practices, my pain reduced significantly.",
    benefits: [
      "Pain reduction",
      "Better sleep",
      "Increased mobility",
      "Reduced medication dependency",
    ],

    rating: 5,
  },
  {
    name: "<PERSON>",
    age: 42,
    condition: "Stress & Anxiety",
    treatment: "Heart-Mind Development + Meditation",
    duration: "6 months",
    improvement: "90%",
    story:
      "The heart-mind development program transformed my approach to stress. Learning to integrate emotional intelligence with logical thinking gave me tools to manage anxiety effectively.",
    benefits: [
      "Stress management",
      "Better decision-making",
      "Emotional balance",
      "Career advancement",
    ],

    rating: 5,
  },
  {
    name: "<PERSON>",
    age: 28,
    condition: "Digestive Issues",
    treatment: "Honey Therapy + Plant Medicine",
    duration: "4 months",
    improvement: "80%",
    story:
      "The natural food healing approach using honey and medicinal plants helped restore my digestive health. The scientific backing gave me confidence in the treatment.",
    benefits: [
      "Digestive health",
      "Energy increase",
      "Natural healing",
      "Nutritional awareness",
    ],

    rating: 5,
  },
  {
    name: "David R.",
    age: 55,
    condition: "High Blood Pressure",
    treatment: "Lifestyle Integration + Natural Elements",
    duration: "8 months",
    improvement: "75%",
    story:
      "Learning about natural elements and their impact on health helped me make lifestyle changes. My blood pressure improved significantly through natural methods.",
    benefits: [
      "Blood pressure control",
      "Natural approach",
      "Lifestyle improvement",
      "Reduced medication",
    ],

    rating: 4,
  },
];

const treatmentOutcomes = [
  { category: "Pain Management", successRate: 85, patients: 234 },
  { category: "Stress & Anxiety", successRate: 90, patients: 189 },
  { category: "Digestive Health", successRate: 80, patients: 156 },
  { category: "Sleep Disorders", successRate: 88, patients: 145 },
  { category: "Energy & Vitality", successRate: 92, patients: 201 },
  { category: "Mental Clarity", successRate: 87, patients: 178 },
];

const journeySteps = [
  {
    step: 1,
    title: "Initial Assessment",
    description:
      "Comprehensive evaluation of your health goals and current condition",
    duration: "1-2 sessions",
  },
  {
    step: 2,
    title: "Personalized Plan",
    description:
      "Custom treatment plan combining traditional wisdom with modern science",
    duration: "1 week",
  },
  {
    step: 3,
    title: "Active Treatment",
    description:
      "Implementation of therapies, practices, and lifestyle changes",
    duration: "2-6 months",
  },
  {
    step: 4,
    title: "Progress Monitoring",
    description: "Regular check-ins and plan adjustments based on progress",
    duration: "Ongoing",
  },
  {
    step: 5,
    title: "Maintenance",
    description: "Long-term wellness strategies and continued support",
    duration: "Lifetime",
  },
];

export default function PatientExperiencePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 dark:from-green-900/20 dark:via-blue-900/20 dark:to-purple-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Heart className="h-16 w-16 text-red-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Patient Experience & Success Stories
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Real stories from individuals who have experienced healing and
              growth through our evidence-based approach to natural wellness,
              combining ancient wisdom with modern science.
            </p>
          </div>
        </div>
      </div>

      {/* Medical Disclaimer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2">
            Important Medical Disclaimer
          </h3>
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            Patient experiences shared here are for educational purposes only.
            Individual results may vary. These stories do not constitute medical
            advice and should not replace professional healthcare consultation.
            Always consult qualified healthcare providers for medical
            conditions.
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* Success Statistics */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Treatment Outcomes & Success Rates
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {treatmentOutcomes.map((outcome, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {outcome.category}
                </h3>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-2xl font-bold text-green-600">
                    {outcome.successRate}%
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    Success Rate
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${outcome.successRate}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Based on {outcome.patients} patient experiences
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Patient Stories */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Real Patient Stories
          </h2>
          <div className="space-y-8">
            {patientStories.map((patient, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8"
              >
                <div className="flex items-start justify-between mb-6">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                      {patient.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Age {patient.age} • {patient.condition}
                    </p>
                  </div>
                  <div className="flex">
                    {[...Array(patient.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 text-yellow-500 fill-current"
                      />
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                      Patient Story:
                    </h4>
                    <p className="text-gray-700 dark:text-gray-300 mb-4 italic">
                      "{patient.story}"
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                          Treatment:
                        </h5>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {patient.treatment}
                        </p>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                          Duration:
                        </h5>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {patient.duration}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="text-center mb-4">
                      <div className="text-3xl font-bold text-green-600 mb-1">
                        {patient.improvement}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        Improvement
                      </div>
                    </div>

                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                      Key Benefits:
                    </h5>
                    <ul className="space-y-1">
                      {patient.benefits.map((benefit, benefitIndex) => (
                        <li
                          key={benefitIndex}
                          className="flex items-center text-sm"
                        >
                          <CheckCircle className="h-4 w-4 text-green-600 mr-2" />

                          <span className="text-gray-700 dark:text-gray-300">
                            {benefit}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Patient Journey */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Typical Patient Journey
          </h2>
          <div className="space-y-6">
            {journeySteps.map((step, index) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold mr-6">
                  {step.step}
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {step.title}
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300 mb-2">
                    {step.description}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">
                    Duration: {step.duration}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Start Your Healing Journey
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Join thousands of individuals who have experienced natural healing
              and personal growth through our evidence-based approach combining
              ancient wisdom with modern science.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                Begin Assessment
              </button>
              <button className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                Join Community
              </button>
              <button className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                Schedule Consultation
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
