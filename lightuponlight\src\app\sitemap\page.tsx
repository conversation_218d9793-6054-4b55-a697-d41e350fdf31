import Link from 'next/link'
import { 
  Heart, 
  Leaf, 
  Mountain, 
  Sun, 
  BookOpen, 
  Users, 
  MessageCircle, 
  Video,
  Star,
  Brain,
  Shield,
  FileText,
  Home,
  Settings
} from 'lucide-react'

const siteStructure = [
  {
    category: 'Core Learning',
    pages: [
      { name: 'Home', href: '/', icon: Home, description: 'Welcome and overview of all learning areas' },
      { name: 'Cupping Therapy', href: '/cupping', icon: Heart, description: 'Scientific approach to traditional healing therapy' },
      { name: 'Heart & Mind Development', href: '/heart-mind', icon: Brain, description: 'Emotional intelligence and logical thinking integration' },
      { name: 'Natural Healing & Plants', href: '/plants', icon: Leaf, description: 'Medicinal plants and evidence-based natural remedies' },
      { name: 'Honey & Natural Foods', href: '/honey', icon: Mountain, description: 'Nutritional science and healing properties of natural foods' },
      { name: 'Nature & Elements', href: '/nature-elements', icon: Sun, description: 'Scientific principles of natural elements and balance' }
    ]
  },
  {
    category: 'Wisdom & Intelligence',
    pages: [
      { name: 'Heavens & Earth', href: '/heavens-earth', icon: Star, description: 'Cosmic intelligence and universal laws for human development' },
      { name: 'Quranic Logic', href: '/quran', icon: BookOpen, description: 'Verses with scientific insights and logical reasoning' },
      { name: '99 Divine Attributes', href: '/names-of-god', icon: BookOpen, description: 'Perfect qualities as frameworks for character development' },
      { name: 'Ancient Wisdom', href: '/wisdom', icon: BookOpen, description: 'Universal principles from various traditions' },
      { name: 'Logic & Intelligence', href: '/logic', icon: Brain, description: 'Critical thinking and intelligence development' }
    ]
  },
  {
    category: 'Community & Learning',
    pages: [
      { name: 'Learning Community', href: '/community', icon: Users, description: 'Structured learning paths and achievements' },
      { name: 'Discussion Forum', href: '/forum', icon: MessageCircle, description: 'Community discussions and knowledge sharing' },
      { name: 'Live Learning', href: '/live', icon: Video, description: 'Interactive sessions with camera/microphone support' }
    ]
  },
  {
    category: 'Legal & Information',
    pages: [
      { name: 'Privacy Policy', href: '/privacy', icon: Shield, description: 'GDPR-compliant privacy protection and data handling' },
      { name: 'Terms of Service', href: '/terms', icon: FileText, description: 'Platform terms and community guidelines' },
      { name: 'Site Map', href: '/sitemap', icon: Settings, description: 'Complete overview of all pages and features' }
    ]
  }
]

const features = [
  'Multi-audience approach with universal wisdom',
  'Scientific backing for traditional knowledge',
  'Interactive live streaming with media access',
  'Comprehensive forum system with moderation',
  'Achievement-based learning paths',
  'GDPR-compliant privacy protection',
  'Dark/light mode support',
  'Mobile-responsive design',
  'Real-time database with Supabase',
  'Role-based access control'
]

export default function SiteMapPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Settings className="h-16 w-16 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Site Map & Overview
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Complete overview of Light Upon Light platform - all pages, features, and learning areas 
              designed for intelligence, wisdom, and human development.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Site Structure */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Platform Structure
          </h2>
          <div className="space-y-12">
            {siteStructure.map((section, sectionIndex) => (
              <div key={sectionIndex} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  {section.category}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {section.pages.map((page, pageIndex) => {
                    const Icon = page.icon
                    return (
                      <Link key={pageIndex} href={page.href}>
                        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                          <div className="flex items-center mb-3">
                            <Icon className="h-6 w-6 text-blue-600 mr-3" />
                            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                              {page.name}
                            </h4>
                          </div>
                          <p className="text-gray-600 dark:text-gray-300 text-sm">
                            {page.description}
                          </p>
                        </div>
                      </Link>
                    )
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Platform Features */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Platform Features
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                  <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Technical Information */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Technical Stack
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Frontend
              </h3>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li>• Next.js 14 with App Router</li>
                <li>• React with TypeScript</li>
                <li>• Tailwind CSS</li>
                <li>• Lucide React Icons</li>
                <li>• Responsive Design</li>
              </ul>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Backend
              </h3>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li>• Supabase Database</li>
                <li>• PostgreSQL</li>
                <li>• Row Level Security</li>
                <li>• Real-time Features</li>
                <li>• Authentication</li>
              </ul>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Features
              </h3>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li>• Live Streaming</li>
                <li>• Forum System</li>
                <li>• Learning Paths</li>
                <li>• Achievement System</li>
                <li>• GDPR Compliance</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Content Statistics */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Content Overview
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">15+</div>
              <div className="text-gray-600 dark:text-gray-300">Main Pages</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">12+</div>
              <div className="text-gray-600 dark:text-gray-300">Quranic Verses</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">99</div>
              <div className="text-gray-600 dark:text-gray-300">Divine Names</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">100+</div>
              <div className="text-gray-600 dark:text-gray-300">Benefits Listed</div>
            </div>
          </div>
        </div>

        {/* Navigation Help */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Start Your Learning Journey
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Light Upon Light offers a comprehensive approach to learning that combines ancient wisdom 
              with modern science, logical thinking with emotional intelligence, and individual growth 
              with community learning.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/community">
                <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                  Join Community
                </button>
              </Link>
              <Link href="/quran">
                <button className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                  Explore Wisdom
                </button>
              </Link>
              <Link href="/live">
                <button className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                  Live Learning
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
