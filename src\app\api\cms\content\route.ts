import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { CMSService } from '@/lib/cms-service';

// GET /api/cms/content - Get content list with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const options = {
      content_type: searchParams.get('content_type') || undefined,
      status: searchParams.get('status') || 'published',
      language: searchParams.get('language') || 'en',
      category: searchParams.get('category') || undefined,
      tag: searchParams.get('tag') || undefined,
      author: searchParams.get('author') || undefined,
      featured: searchParams.get('featured') ? searchParams.get('featured') === 'true' : undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 20,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0,
      sort_by: searchParams.get('sort_by') || 'published_at',
      sort_order: (searchParams.get('sort_order') as 'asc' | 'desc') || 'desc',
      search: searchParams.get('search') || undefined,
    };

    const result = await CMSService.getContentList(options);

    return NextResponse.json({
      success: true,
      data: result,
      meta: {
        total: result.total,
        limit: options.limit,
        offset: options.offset,
        hasMore: result.hasMore
      }
    });
  } catch (error) {
    console.error('Error in GET /api/cms/content:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch content' },
      { status: 500 }
    );
  }
}

// POST /api/cms/content - Create new content (admin only)
export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || !['admin', 'moderator'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await request.json();
    
    // Validate required fields
    if (!body.title || !body.content_type_id || !body.content) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: title, content_type_id, content' },
        { status: 400 }
      );
    }

    // Generate slug if not provided
    if (!body.slug) {
      body.slug = body.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
    }

    // Create content
    const { data, error } = await supabase
      .from('cms_content')
      .insert({
        ...body,
        author_id: user.id,
        language: body.language || 'en',
        status: body.status || 'draft',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating content:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create content' },
        { status: 500 }
      );
    }

    // Handle categories and tags if provided
    if (body.categories && Array.isArray(body.categories)) {
      const categoryInserts = body.categories.map((categoryId: string) => ({
        content_id: data.id,
        category_id: categoryId
      }));
      
      await supabase
        .from('cms_content_categories')
        .insert(categoryInserts);
    }

    if (body.tags && Array.isArray(body.tags)) {
      const tagInserts = body.tags.map((tagId: string) => ({
        content_id: data.id,
        tag_id: tagId
      }));
      
      await supabase
        .from('cms_content_tags')
        .insert(tagInserts);
    }

    // Create initial version
    await supabase
      .from('cms_content_versions')
      .insert({
        content_id: data.id,
        version_number: 1,
        title: data.title,
        content: data.content,
        meta_title: data.meta_title,
        meta_description: data.meta_description,
        author_id: user.id,
        change_summary: 'Initial version'
      });

    // Clear caches
    CMSService.clearAllCaches();

    return NextResponse.json({
      success: true,
      data,
      message: 'Content created successfully'
    });
  } catch (error) {
    console.error('Error in POST /api/cms/content:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create content' },
      { status: 500 }
    );
  }
}

// PUT /api/cms/content - Update content (admin only)
export async function PUT(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || !['admin', 'moderator'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await request.json();
    
    if (!body.id) {
      return NextResponse.json(
        { success: false, error: 'Content ID is required' },
        { status: 400 }
      );
    }

    // Get current content for versioning
    const { data: currentContent } = await supabase
      .from('cms_content')
      .select('*')
      .eq('id', body.id)
      .single();

    if (!currentContent) {
      return NextResponse.json(
        { success: false, error: 'Content not found' },
        { status: 404 }
      );
    }

    // Update content
    const { data, error } = await supabase
      .from('cms_content')
      .update({
        ...body,
        updated_at: new Date().toISOString()
      })
      .eq('id', body.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating content:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update content' },
        { status: 500 }
      );
    }

    // Create new version if content changed
    if (currentContent.content !== body.content || currentContent.title !== body.title) {
      // Get next version number
      const { data: versions } = await supabase
        .from('cms_content_versions')
        .select('version_number')
        .eq('content_id', body.id)
        .order('version_number', { ascending: false })
        .limit(1);

      const nextVersion = versions && versions.length > 0 ? versions[0].version_number + 1 : 1;

      await supabase
        .from('cms_content_versions')
        .insert({
          content_id: body.id,
          version_number: nextVersion,
          title: body.title,
          content: body.content,
          meta_title: body.meta_title,
          meta_description: body.meta_description,
          author_id: user.id,
          change_summary: body.change_summary || `Version ${nextVersion}`
        });
    }

    // Update categories if provided
    if (body.categories !== undefined) {
      // Remove existing categories
      await supabase
        .from('cms_content_categories')
        .delete()
        .eq('content_id', body.id);

      // Add new categories
      if (Array.isArray(body.categories) && body.categories.length > 0) {
        const categoryInserts = body.categories.map((categoryId: string) => ({
          content_id: body.id,
          category_id: categoryId
        }));
        
        await supabase
          .from('cms_content_categories')
          .insert(categoryInserts);
      }
    }

    // Update tags if provided
    if (body.tags !== undefined) {
      // Remove existing tags
      await supabase
        .from('cms_content_tags')
        .delete()
        .eq('content_id', body.id);

      // Add new tags
      if (Array.isArray(body.tags) && body.tags.length > 0) {
        const tagInserts = body.tags.map((tagId: string) => ({
          content_id: body.id,
          tag_id: tagId
        }));
        
        await supabase
          .from('cms_content_tags')
          .insert(tagInserts);
      }
    }

    // Clear caches
    CMSService.clearAllCaches();

    return NextResponse.json({
      success: true,
      data,
      message: 'Content updated successfully'
    });
  } catch (error) {
    console.error('Error in PUT /api/cms/content:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update content' },
      { status: 500 }
    );
  }
}

// DELETE /api/cms/content - Delete content (admin only)
export async function DELETE(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const contentId = searchParams.get('id');

    if (!contentId) {
      return NextResponse.json(
        { success: false, error: 'Content ID is required' },
        { status: 400 }
      );
    }

    // Soft delete by setting status to archived
    const { error } = await supabase
      .from('cms_content')
      .update({ 
        status: 'archived',
        updated_at: new Date().toISOString()
      })
      .eq('id', contentId);

    if (error) {
      console.error('Error deleting content:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete content' },
        { status: 500 }
      );
    }

    // Clear caches
    CMSService.clearAllCaches();

    return NextResponse.json({
      success: true,
      message: 'Content deleted successfully'
    });
  } catch (error) {
    console.error('Error in DELETE /api/cms/content:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete content' },
      { status: 500 }
    );
  }
}
