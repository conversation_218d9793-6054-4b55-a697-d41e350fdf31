-- Light Upon Light CMS Database Schema
-- Comprehensive Content Management System with versioning, media, and multi-language support

-- Content Types for different kinds of content
CREATE TABLE cms_content_types (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    schema JSONB NOT NULL, -- <PERSON><PERSON><PERSON> schema for content fields
    template VARCHAR(255), -- Template file name
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Main content table with versioning
CREATE TABLE cms_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_type_id VARCHAR(50) REFERENCES cms_content_types(id),
    slug VARCHAR(255) NOT NULL,
    title VARCHAR(500) NOT NULL,
    excerpt TEXT,
    content JSONB NOT NULL, -- Flexible content storage
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords TEXT,
    featured_image_id UUID,
    author_id UUID REFERENCES users(id),
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived', 'scheduled')),
    published_at TIMESTAMP WITH TIME ZONE,
    scheduled_at TIMESTAMP WITH TIME ZONE,
    language VARCHAR(10) DEFAULT 'en',
    parent_id UUID REFERENCES cms_content(id), -- For translations
    sort_order INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT false,
    is_sticky BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(slug, language)
);

-- Content versions for revision history
CREATE TABLE cms_content_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID REFERENCES cms_content(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    title VARCHAR(500) NOT NULL,
    content JSONB NOT NULL,
    meta_title VARCHAR(255),
    meta_description TEXT,
    author_id UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    change_summary TEXT,
    UNIQUE(content_id, version_number)
);

-- Media library for files, images, videos
CREATE TABLE cms_media (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_type VARCHAR(50) NOT NULL, -- image, video, audio, document
    width INTEGER, -- for images/videos
    height INTEGER, -- for images/videos
    duration INTEGER, -- for videos/audio in seconds
    alt_text TEXT,
    caption TEXT,
    description TEXT,
    uploaded_by UUID REFERENCES users(id),
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories for content organization
CREATE TABLE cms_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES cms_categories(id),
    color VARCHAR(50),
    icon VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    language VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tags for content tagging
CREATE TABLE cms_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(50),
    usage_count INTEGER DEFAULT 0,
    language VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content-Category relationships (many-to-many)
CREATE TABLE cms_content_categories (
    content_id UUID REFERENCES cms_content(id) ON DELETE CASCADE,
    category_id UUID REFERENCES cms_categories(id) ON DELETE CASCADE,
    PRIMARY KEY (content_id, category_id)
);

-- Content-Tag relationships (many-to-many)
CREATE TABLE cms_content_tags (
    content_id UUID REFERENCES cms_content(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES cms_tags(id) ON DELETE CASCADE,
    PRIMARY KEY (content_id, tag_id)
);

-- Menu system for navigation
CREATE TABLE cms_menus (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    location VARCHAR(100), -- header, footer, sidebar
    is_active BOOLEAN DEFAULT true,
    language VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Menu items
CREATE TABLE cms_menu_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    menu_id UUID REFERENCES cms_menus(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES cms_menu_items(id),
    title VARCHAR(255) NOT NULL,
    url TEXT,
    content_id UUID REFERENCES cms_content(id),
    icon VARCHAR(100),
    css_class VARCHAR(255),
    target VARCHAR(50) DEFAULT '_self',
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content blocks for reusable components
CREATE TABLE cms_blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    content JSONB NOT NULL,
    block_type VARCHAR(100) NOT NULL, -- text, image, video, form, custom
    is_global BOOLEAN DEFAULT false, -- can be used across pages
    is_active BOOLEAN DEFAULT true,
    language VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Page layouts and templates
CREATE TABLE cms_layouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    template_file VARCHAR(255) NOT NULL,
    regions JSONB NOT NULL, -- Define content regions
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content analytics and tracking
CREATE TABLE cms_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID REFERENCES cms_content(id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL, -- view, like, share, download
    user_id UUID REFERENCES users(id),
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    session_id VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content comments (if enabled)
CREATE TABLE cms_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID REFERENCES cms_content(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES cms_comments(id),
    author_name VARCHAR(255),
    author_email VARCHAR(255),
    author_id UUID REFERENCES users(id),
    content TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'spam')),
    ip_address INET,
    user_agent TEXT,
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- CMS Settings and configuration
CREATE TABLE cms_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value JSONB,
    description TEXT,
    type VARCHAR(50) DEFAULT 'string', -- string, number, boolean, json, array
    is_public BOOLEAN DEFAULT false, -- can be accessed by frontend
    group_name VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key for featured image
ALTER TABLE cms_content ADD CONSTRAINT fk_cms_content_featured_image 
    FOREIGN KEY (featured_image_id) REFERENCES cms_media(id);

-- Create indexes for performance
CREATE INDEX idx_cms_content_slug ON cms_content(slug);
CREATE INDEX idx_cms_content_status ON cms_content(status);
CREATE INDEX idx_cms_content_published_at ON cms_content(published_at);
CREATE INDEX idx_cms_content_language ON cms_content(language);
CREATE INDEX idx_cms_content_author ON cms_content(author_id);
CREATE INDEX idx_cms_content_type ON cms_content(content_type_id);
CREATE INDEX idx_cms_content_featured ON cms_content(is_featured);
CREATE INDEX idx_cms_media_type ON cms_media(file_type);
CREATE INDEX idx_cms_media_public ON cms_media(is_public);
CREATE INDEX idx_cms_analytics_content ON cms_analytics(content_id);
CREATE INDEX idx_cms_analytics_event ON cms_analytics(event_type);
CREATE INDEX idx_cms_analytics_created ON cms_analytics(created_at);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_cms_content_updated_at BEFORE UPDATE ON cms_content FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cms_media_updated_at BEFORE UPDATE ON cms_media FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cms_categories_updated_at BEFORE UPDATE ON cms_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cms_tags_updated_at BEFORE UPDATE ON cms_tags FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cms_blocks_updated_at BEFORE UPDATE ON cms_blocks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cms_settings_updated_at BEFORE UPDATE ON cms_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default content types
INSERT INTO cms_content_types (id, name, description, schema, template) VALUES
('page', 'Page', 'Standard web page', '{"fields": [{"name": "content", "type": "richtext", "required": true}]}', 'page'),
('post', 'Blog Post', 'Blog post or article', '{"fields": [{"name": "content", "type": "richtext", "required": true}, {"name": "excerpt", "type": "textarea"}]}', 'post'),
('educational', 'Educational Content', 'Educational page content', '{"fields": [{"name": "content", "type": "richtext", "required": true}, {"name": "difficulty", "type": "select", "options": ["beginner", "intermediate", "advanced"]}]}', 'educational'),
('legal', 'Legal Document', 'Legal and compliance pages', '{"fields": [{"name": "content", "type": "richtext", "required": true}, {"name": "effective_date", "type": "date"}]}', 'legal');

-- Insert default categories
INSERT INTO cms_categories (name, slug, description, language) VALUES
('General', 'general', 'General content', 'en'),
('Educational', 'educational', 'Educational content', 'en'),
('Health & Healing', 'health-healing', 'Health and healing content', 'en'),
('Spiritual', 'spiritual', 'Spiritual content', 'en'),
('Legal', 'legal', 'Legal and compliance content', 'en');

-- Insert default settings
INSERT INTO cms_settings (key, value, description, type, is_public, group_name) VALUES
('site_title', '"Light Upon Light"', 'Website title', 'string', true, 'general'),
('site_description', '"A place to learn wisdom, natural healing, and grow your mind through simple thinking and nature''s lessons"', 'Website description', 'string', true, 'general'),
('default_language', '"en"', 'Default website language', 'string', true, 'general'),
('enable_comments', 'false', 'Enable comments on content', 'boolean', false, 'content'),
('enable_analytics', 'true', 'Enable content analytics', 'boolean', false, 'analytics'),
('cache_duration', '3600', 'Content cache duration in seconds', 'number', false, 'performance');

-- Database functions for CMS operations

-- Function to increment content view count
CREATE OR REPLACE FUNCTION increment_content_views(content_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE cms_content
    SET view_count = view_count + 1,
        updated_at = NOW()
    WHERE id = content_id;

    -- Insert analytics record
    INSERT INTO cms_analytics (content_id, event_type, created_at)
    VALUES (content_id, 'view', NOW());
END;
$$ LANGUAGE plpgsql;

-- Function to update tag usage count
CREATE OR REPLACE FUNCTION update_tag_usage_count(tag_id UUID, increment_by INTEGER DEFAULT 1)
RETURNS void AS $$
BEGIN
    UPDATE cms_tags
    SET usage_count = GREATEST(0, usage_count + increment_by),
        updated_at = NOW()
    WHERE id = tag_id;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old analytics data
CREATE OR REPLACE FUNCTION cleanup_old_analytics(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cms_analytics
    WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get content statistics
CREATE OR REPLACE FUNCTION get_content_statistics()
RETURNS TABLE (
    total_content BIGINT,
    published_content BIGINT,
    draft_content BIGINT,
    scheduled_content BIGINT,
    total_views BIGINT,
    total_categories BIGINT,
    total_tags BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM cms_content WHERE status != 'archived') as total_content,
        (SELECT COUNT(*) FROM cms_content WHERE status = 'published') as published_content,
        (SELECT COUNT(*) FROM cms_content WHERE status = 'draft') as draft_content,
        (SELECT COUNT(*) FROM cms_content WHERE status = 'scheduled') as scheduled_content,
        (SELECT COALESCE(SUM(view_count), 0) FROM cms_content) as total_views,
        (SELECT COUNT(*) FROM cms_categories WHERE is_active = true) as total_categories,
        (SELECT COUNT(*) FROM cms_tags) as total_tags;
END;
$$ LANGUAGE plpgsql;

-- Function to publish scheduled content
CREATE OR REPLACE FUNCTION publish_scheduled_content()
RETURNS TABLE (
    content_id UUID,
    title VARCHAR(500),
    published_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    current_time TIMESTAMP WITH TIME ZONE := NOW();
BEGIN
    -- Update scheduled content that should be published
    UPDATE cms_content
    SET
        status = 'published',
        published_at = current_time,
        scheduled_at = NULL,
        updated_at = current_time
    WHERE
        status = 'scheduled'
        AND scheduled_at <= current_time;

    -- Return the published content
    RETURN QUERY
    SELECT
        c.id as content_id,
        c.title,
        c.published_at
    FROM cms_content c
    WHERE c.published_at = current_time;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update tag usage count when content-tag relationships change
CREATE OR REPLACE FUNCTION update_tag_usage_on_content_tag_change()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM update_tag_usage_count(NEW.tag_id, 1);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_tag_usage_count(OLD.tag_id, -1);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_tag_usage
    AFTER INSERT OR DELETE ON cms_content_tags
    FOR EACH ROW EXECUTE FUNCTION update_tag_usage_on_content_tag_change();

-- RLS (Row Level Security) policies for CMS tables

-- Enable RLS on CMS tables
ALTER TABLE cms_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_content_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_media ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_settings ENABLE ROW LEVEL SECURITY;

-- Public read access for published content
CREATE POLICY "Public can view published content" ON cms_content
    FOR SELECT USING (status = 'published');

-- Public read access for active categories and tags
CREATE POLICY "Public can view active categories" ON cms_categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "Public can view tags" ON cms_tags
    FOR SELECT USING (true);

-- Public read access for public media
CREATE POLICY "Public can view public media" ON cms_media
    FOR SELECT USING (is_public = true);

-- Public read access for public settings
CREATE POLICY "Public can view public settings" ON cms_settings
    FOR SELECT USING (is_public = true);

-- Admin/moderator full access
CREATE POLICY "Admins can manage all content" ON cms_content
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()
            AND users.role IN ('admin', 'moderator')
        )
    );

CREATE POLICY "Admins can manage content versions" ON cms_content_versions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()
            AND users.role IN ('admin', 'moderator')
        )
    );

CREATE POLICY "Admins can manage media" ON cms_media
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()
            AND users.role IN ('admin', 'moderator')
        )
    );

CREATE POLICY "Admins can manage categories" ON cms_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()
            AND users.role IN ('admin', 'moderator')
        )
    );

CREATE POLICY "Admins can manage tags" ON cms_tags
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()
            AND users.role IN ('admin', 'moderator')
        )
    );

CREATE POLICY "Admins can manage settings" ON cms_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()
            AND users.role = 'admin'
        )
    );
