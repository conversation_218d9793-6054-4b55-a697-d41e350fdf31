"use client";

import { useState, useEffect } from 'react';
import { Shield, Globe, Info, CheckCircle, AlertTriangle, ExternalLink } from 'lucide-react';
import { Button } from './ui/button';

interface TranslationConsentProps {
  onConsent: (granted: boolean) => void;
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Translation Consent Modal
 * Handles consent for Google Translate integration with GDPR compliance
 */
export function TranslationConsent({ onConsent, isOpen, onClose }: TranslationConsentProps) {
  const [hasConsented, setHasConsented] = useState<boolean | null>(null);

  useEffect(() => {
    // Check if user has already given consent
    const consent = localStorage.getItem('google-translate-consent');
    if (consent) {
      const consentData = JSON.parse(consent);
      setHasConsented(consentData.granted);
    }
  }, []);

  const handleConsent = (granted: boolean) => {
    const consentData = {
      granted,
      timestamp: Date.now(),
      version: '1.0'
    };
    
    localStorage.setItem('google-translate-consent', JSON.stringify(consentData));
    setHasConsented(granted);
    onConsent(granted);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-sm" 
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 p-6 w-full max-w-lg mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <Shield className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              Translation Service Consent
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              GDPR Compliance Required
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-4 mb-6">
          <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Info className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-amber-800 dark:text-amber-200">
                <p className="font-medium mb-2">Third-Party Service Notice</p>
                <p className="text-sm leading-relaxed">
                  Google Translate is a third-party service provided by Google LLC. 
                  By using this feature, your content will be sent to Google's servers for translation.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="font-medium text-gray-900 dark:text-white">What happens when you use Google Translate:</h4>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
              <li className="flex items-start space-x-2">
                <Globe className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <span>Page content is sent to Google's translation servers</span>
              </li>
              <li className="flex items-start space-x-2">
                <Shield className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>No personal data is stored by our website during translation</span>
              </li>
              <li className="flex items-start space-x-2">
                <ExternalLink className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                <span>Google's privacy policy applies to the translation service</span>
              </li>
            </ul>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Your Rights:</h4>
            <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
              <li>• You can withdraw consent at any time</li>
              <li>• Translation is optional - our site works without it</li>
              <li>• We have 6 built-in languages that don't require Google services</li>
              <li>• Your choice is saved and can be changed in privacy settings</li>
            </ul>
          </div>
        </div>

        {/* Privacy Links */}
        <div className="mb-6 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <p className="text-sm text-blue-800 dark:text-blue-200 mb-2">
            <strong>Privacy Information:</strong>
          </p>
          <div className="flex flex-wrap gap-2 text-xs">
            <a 
              href="/privacy" 
              className="text-blue-600 hover:text-blue-800 underline"
              target="_blank"
            >
              Our Privacy Policy
            </a>
            <span className="text-gray-400">•</span>
            <a 
              href="https://policies.google.com/privacy" 
              className="text-blue-600 hover:text-blue-800 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Google Privacy Policy
            </a>
            <span className="text-gray-400">•</span>
            <a 
              href="/gdpr" 
              className="text-blue-600 hover:text-blue-800 underline"
              target="_blank"
            >
              GDPR Rights
            </a>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={() => handleConsent(true)}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Accept & Enable Translation
          </Button>
          <Button
            onClick={() => handleConsent(false)}
            variant="outline"
            className="flex-1"
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Decline
          </Button>
        </div>

        <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-4">
          This consent is required under GDPR for third-party data processing
        </p>
      </div>
    </div>
  );
}

/**
 * Translation Consent Status Component
 * Shows current consent status in admin/privacy settings
 */
export function TranslationConsentStatus() {
  const [consentStatus, setConsentStatus] = useState<{
    granted: boolean;
    timestamp: number;
    version: string;
  } | null>(null);

  useEffect(() => {
    const consent = localStorage.getItem('google-translate-consent');
    if (consent) {
      setConsentStatus(JSON.parse(consent));
    }
  }, []);

  const revokeConsent = () => {
    localStorage.removeItem('google-translate-consent');
    setConsentStatus(null);
    // Reload page to disable Google Translate
    window.location.reload();
  };

  if (!consentStatus) {
    return (
      <div className="text-sm text-gray-500">
        No translation consent given
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">Google Translate Consent:</span>
        <span className={`text-sm px-2 py-1 rounded ${
          consentStatus.granted 
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' 
            : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
        }`}>
          {consentStatus.granted ? 'Granted' : 'Denied'}
        </span>
      </div>
      <div className="text-xs text-gray-500">
        Given: {new Date(consentStatus.timestamp).toLocaleDateString()}
      </div>
      {consentStatus.granted && (
        <Button
          onClick={revokeConsent}
          size="sm"
          variant="outline"
          className="text-xs"
        >
          Revoke Consent
        </Button>
      )}
    </div>
  );
}
