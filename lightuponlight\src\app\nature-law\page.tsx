import { <PERSON>, Sun, Mountain, <PERSON>s, Wind, Zap, <PERSON>, Heart, Star, Globe } from 'lucide-react'

const naturalLaws = [
  {
    law: 'Law of Balance',
    principle: 'Every system seeks equilibrium and harmony',
    examples: [
      'Homeostasis in human body',
      'Ecosystem balance in nature',
      'Work-life balance for well-being',
      'Emotional regulation for mental health'
    ],
    applications: [
      'Maintain balanced nutrition',
      'Balance activity with rest',
      'Integrate logic with emotion',
      'Practice moderation in all things'
    ],
    benefits: ['Sustainable health', 'Reduced stress', 'Optimal performance', 'Long-term success'],
    icon: Waves,
    color: 'text-blue-600'
  },
  {
    law: 'Law of Cycles',
    principle: 'All natural processes follow cyclical patterns',
    examples: [
      'Day and night cycles',
      'Seasonal changes',
      'Life and death cycles',
      'Economic cycles'
    ],
    applications: [
      'Align with circadian rhythms',
      'Plan according to natural seasons',
      'Accept life transitions',
      'Prepare for cyclical changes'
    ],
    benefits: ['Better health', 'Improved planning', 'Reduced resistance', 'Natural flow'],
    icon: Sun,
    color: 'text-yellow-600'
  },
  {
    law: 'Law of Growth',
    principle: 'All living systems naturally tend toward growth and development',
    examples: [
      'Plant growth toward light',
      'Human learning and development',
      'Skill improvement through practice',
      'Consciousness expansion'
    ],
    applications: [
      'Continuous learning',
      'Skill development',
      'Personal growth practices',
      'Embrace challenges'
    ],
    benefits: ['Personal development', 'Increased capabilities', 'Life satisfaction', 'Adaptability'],
    icon: Leaf,
    color: 'text-green-600'
  },
  {
    law: 'Law of Cause and Effect',
    principle: 'Every action produces a corresponding reaction',
    examples: [
      'Physical laws of motion',
      'Health consequences of lifestyle',
      'Relationship dynamics',
      'Economic cause and effect'
    ],
    applications: [
      'Take responsibility for actions',
      'Consider long-term consequences',
      'Make conscious choices',
      'Learn from outcomes'
    ],
    benefits: ['Better decision-making', 'Personal responsibility', 'Predictable outcomes', 'Learning acceleration'],
    icon: Zap,
    color: 'text-purple-600'
  },
  {
    law: 'Law of Adaptation',
    principle: 'Systems adapt to their environment for survival and optimization',
    examples: [
      'Species evolution',
      'Immune system adaptation',
      'Skill development',
      'Cultural adaptation'
    ],
    applications: [
      'Develop resilience',
      'Learn new skills',
      'Adapt to change',
      'Build flexibility'
    ],
    benefits: ['Resilience', 'Survival skills', 'Innovation', 'Competitive advantage'],
    icon: Mountain,
    color: 'text-gray-600'
  },
  {
    law: 'Law of Energy Conservation',
    principle: 'Energy cannot be created or destroyed, only transformed',
    examples: [
      'Physical energy conservation',
      'Mental energy management',
      'Emotional energy flow',
      'Resource conservation'
    ],
    applications: [
      'Manage energy efficiently',
      'Focus on high-impact activities',
      'Rest and recovery',
      'Sustainable practices'
    ],
    benefits: ['Optimal performance', 'Sustainability', 'Reduced waste', 'Increased efficiency'],
    icon: Wind,
    color: 'text-cyan-600'
  }
]

const universalPrinciples = [
  {
    principle: 'Unity in Diversity',
    description: 'All things are interconnected despite apparent differences',
    wisdom: 'Understanding interconnectedness leads to compassion and cooperation',
    practice: 'Look for common ground and shared humanity in all interactions'
  },
  {
    principle: 'As Above, So Below',
    description: 'Patterns repeat at different scales throughout nature',
    wisdom: 'Studying one level of reality reveals truths about other levels',
    practice: 'Apply insights from one area of life to other areas'
  },
  {
    principle: 'The Middle Way',
    description: 'Optimal outcomes often lie between extremes',
    wisdom: 'Balance and moderation lead to sustainable success',
    practice: 'Seek balance in thoughts, emotions, and actions'
  },
  {
    principle: 'Natural Selection',
    description: 'What serves life and growth is naturally selected',
    wisdom: 'Align with life-supporting principles for natural success',
    practice: 'Choose thoughts and actions that support growth and well-being'
  }
]

export default function NatureLawPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 dark:from-green-900/20 dark:via-blue-900/20 dark:to-purple-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Globe className="h-16 w-16 text-green-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Nature's Universal Laws
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Discover the fundamental laws that govern all natural systems and learn how to align 
              your life with these principles for optimal health, success, and fulfillment through 
              intelligence and logical understanding.
            </p>
          </div>
        </div>
      </div>

      {/* Wisdom Quote */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="verse-container text-center">
          <div className="flex justify-center mb-4">
            <Brain className="h-8 w-8 text-green-600" />
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4">
            "Nature operates according to intelligent laws that promote harmony, growth, and sustainability. 
            By understanding and aligning with these laws, we optimize our potential and well-being."
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Universal Natural Philosophy
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* Natural Laws */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Fundamental Natural Laws
          </h2>
          <div className="space-y-8">
            {naturalLaws.map((law, index) => {
              const Icon = law.icon
              return (
                <div key={index} className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
                  <div className="flex items-center mb-6">
                    <Icon className={`h-8 w-8 ${law.color} mr-4`} />
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                        {law.law}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 italic">
                        {law.principle}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Examples */}
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Natural Examples
                      </h4>
                      <ul className="space-y-2">
                        {law.examples.map((example, exampleIndex) => (
                          <li key={exampleIndex} className="flex items-start">
                            <Star className="h-4 w-4 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700 dark:text-gray-300 text-sm">{example}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Applications */}
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Practical Applications
                      </h4>
                      <ul className="space-y-2">
                        {law.applications.map((application, appIndex) => (
                          <li key={appIndex} className="flex items-start">
                            <Brain className="h-4 w-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700 dark:text-gray-300 text-sm">{application}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Benefits */}
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Key Benefits
                      </h4>
                      <ul className="space-y-2">
                        {law.benefits.map((benefit, benefitIndex) => (
                          <li key={benefitIndex} className="flex items-start">
                            <Heart className="h-4 w-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700 dark:text-gray-300 text-sm">{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Universal Principles */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Universal Principles
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {universalPrinciples.map((principle, index) => (
              <div key={index} className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {principle.principle}
                </h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  {principle.description}
                </p>
                
                <div className="space-y-3">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded">
                    <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-1">Wisdom:</h4>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">{principle.wisdom}</p>
                  </div>
                  
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded">
                    <h4 className="font-medium text-green-800 dark:text-green-300 mb-1">Practice:</h4>
                    <p className="text-green-700 dark:text-green-300 text-sm">{principle.practice}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Integration Guide */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Living in Harmony with Natural Laws
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <Brain className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Understand
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Study and comprehend the natural laws that govern life and success.
                </p>
              </div>
              <div className="text-center">
                <Heart className="h-12 w-12 text-red-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Align
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Adjust your thoughts, actions, and lifestyle to work with natural principles.
                </p>
              </div>
              <div className="text-center">
                <Star className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Thrive
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Experience optimal health, success, and fulfillment through natural harmony.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Align with Nature's Intelligence
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Natural laws are not restrictions but guidelines for optimal living. By understanding 
              and aligning with these principles, you work with the intelligence of nature rather than against it.
            </p>
            <div className="flex justify-center space-x-8">
              <div className="text-center">
                <Globe className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Natural Harmony</p>
              </div>
              <div className="text-center">
                <Brain className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Intelligent Living</p>
              </div>
              <div className="text-center">
                <Heart className="h-8 w-8 text-red-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Optimal Well-being</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
