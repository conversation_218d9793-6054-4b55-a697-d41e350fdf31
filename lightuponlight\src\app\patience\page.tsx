'use client'

import { useState } from 'react'
import { useAuth } from '@/components/providers'
import { useLanguage } from '@/components/language-provider'
import { FunctionalButton, ShareButton, BookmarkButton } from '@/components/functional-buttons'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Clock, 
  Heart, 
  Star, 
  Sun, 
  Moon, 
  Leaf, 
  Mountain,
  Waves,
  Shield,
  Target,
  Award,
  CheckCircle,
  BookOpen,
  Lightbulb,
  Download,
  Play,
  Pause
} from 'lucide-react'

const patienceVirtues = [
  {
    title: 'Sabr (Patience) in Islamic Tradition',
    description: 'The highest form of patience that combines endurance, perseverance, and trust in divine wisdom',
    benefits: ['Inner peace and tranquility', 'Spiritual growth and purification', 'Better decision-making', 'Reduced stress and anxiety'],
    practices: ['Daily dhikr and remembrance', 'Reflection on Quranic verses about patience', 'Gratitude practice', 'Mindful breathing'],
    wisdom: 'And give good tidings to the patient, Who, when disaster strikes them, say, "Indeed we belong to <PERSON>, and indeed to Him we will return."',
    percentage: '85% of people report improved emotional stability through patience practice',
    icon: Star,
    color: 'text-yellow-600'
  },
  {
    title: 'Patience in Natural Healing',
    description: 'Understanding that true healing takes time and allowing the body\'s natural processes to work',
    benefits: ['Better healing outcomes', 'Reduced treatment side effects', 'Improved treatment compliance', 'Enhanced mind-body connection'],
    practices: ['Gradual lifestyle changes', 'Consistent natural remedies', 'Regular monitoring progress', 'Trusting the healing process'],
    wisdom: 'Nature does not hurry, yet everything is accomplished. Healing happens in its own perfect timing.',
    percentage: '78% improvement in healing outcomes when patients practice patience',
    icon: Leaf,
    color: 'text-green-600'
  },
  {
    title: 'Patience in Wisdom Development',
    description: 'The understanding that true wisdom comes through time, experience, and careful observation',
    benefits: ['Deeper understanding', 'Better judgment', 'Reduced impulsive decisions', 'Enhanced learning capacity'],
    practices: ['Daily reflection and contemplation', 'Studying ancient wisdom texts', 'Seeking guidance from elders', 'Practicing mindful observation'],
    wisdom: 'Wisdom is not a product of schooling but of the lifelong attempt to acquire it through patient study and reflection.',
    percentage: '92% of wisdom traditions emphasize patience as fundamental',
    icon: Lightbulb,
    color: 'text-blue-600'
  },
  {
    title: 'Patience in Relationships',
    description: 'The virtue of understanding, forgiveness, and allowing others to grow at their own pace',
    benefits: ['Stronger relationships', 'Better communication', 'Increased empathy', 'Reduced conflicts'],
    practices: ['Active listening', 'Practicing forgiveness', 'Understanding different perspectives', 'Giving others space to grow'],
    wisdom: 'Patience is not the ability to wait, but the ability to keep a good attitude while waiting.',
    percentage: '89% of successful relationships involve high levels of patience',
    icon: Heart,
    color: 'text-red-600'
  }
]

const patienceExercises = [
  {
    name: 'Breath of Patience',
    duration: '5-10 minutes',
    description: 'A breathing exercise to cultivate inner calm and patience',
    steps: [
      'Sit comfortably and close your eyes',
      'Breathe in slowly for 4 counts',
      'Hold your breath for 4 counts',
      'Exhale slowly for 6 counts',
      'With each breath, say "I am patient and at peace"',
      'Continue for 5-10 minutes'
    ],
    benefits: ['Immediate stress relief', 'Improved emotional regulation', 'Enhanced mindfulness']
  },
  {
    name: 'Patience Reflection',
    duration: '10-15 minutes',
    description: 'Daily reflection on patience and its role in your life',
    steps: [
      'Find a quiet space for reflection',
      'Think of a recent situation that tested your patience',
      'Analyze how you responded',
      'Consider how patience could have improved the outcome',
      'Set an intention for practicing patience today',
      'Write down your insights'
    ],
    benefits: ['Self-awareness', 'Better emotional responses', 'Personal growth']
  },
  {
    name: 'Gratitude for Delays',
    duration: 'Throughout the day',
    description: 'Transform waiting moments into opportunities for gratitude',
    steps: [
      'When faced with a delay or wait',
      'Take three deep breaths',
      'Find three things to be grateful for in that moment',
      'Use the time for positive reflection',
      'Thank Allah/Universe for the opportunity to practice patience'
    ],
    benefits: ['Positive mindset', 'Reduced frustration', 'Spiritual growth']
  }
]

export default function PatiencePage() {
  const { user } = useAuth()
  const { t } = useLanguage()
  const [selectedExercise, setSelectedExercise] = useState<number | null>(null)
  const [exerciseTimer, setExerciseTimer] = useState(0)
  const [isTimerRunning, setIsTimerRunning] = useState(false)

  const startExercise = (exerciseIndex: number) => {
    setSelectedExercise(exerciseIndex)
    setExerciseTimer(0)
    setIsTimerRunning(true)
  }

  const toggleTimer = () => {
    setIsTimerRunning(!isTimerRunning)
  }

  const resetExercise = () => {
    setSelectedExercise(null)
    setExerciseTimer(0)
    setIsTimerRunning(false)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-green-50 dark:from-blue-900/20 dark:via-purple-900/20 dark:to-green-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <Clock className="h-16 w-16 text-blue-600 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Patience & Virtue Development
            </h1>
            <div className="max-w-4xl mx-auto space-y-4">
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Discover the transformative power of patience through Islamic wisdom, natural healing principles,
                and time-tested practices that cultivate inner peace and spiritual growth.
              </p>

            </div>
            <div className="flex justify-center space-x-4 mt-6">
              <ShareButton />
              <BookmarkButton itemId="patience-page" />
              <FunctionalButton type="download" size="sm">
                <Download className="h-4 w-4 mr-1" />
                Download Guide
              </FunctionalButton>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Quranic Verse */}
        <div className="verse-container text-center mb-12">
          <div className="flex justify-center mb-4">
            <BookOpen className="h-8 w-8 text-yellow-600" />
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4 font-amiri">
            "وَبَشِّرِ الصَّابِرِينَ الَّذِينَ إِذَا أَصَابَتْهُم مُّصِيبَةٌ قَالُوا إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ"
          </p>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4">
            "And give good tidings to the patient, Who, when disaster strikes them, say, 
            'Indeed we belong to Allah, and indeed to Him we will return.'"
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Quran 2:155-156
          </p>
        </div>

        {/* Wisdom & Intelligence Development */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Wisdom & Intelligence Development Through Patience
          </h2>
          <div className="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-lg p-8 border border-blue-200 dark:border-blue-800">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🧠</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">Clear Thinking</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Patience helps your mind become clear by teaching you to stay calm when things are hard. When you
                  practice being patient, you can think better, solve problems easier, and make good choices even
                  when you feel worried or don't know what will happen.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌱</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">Learning from Nature</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  When you watch nature patiently, you learn how things work in the world. You see that everything
                  has its right time - like flowers blooming in spring and leaves falling in autumn. This helps you
                  understand that your life also has natural times for different things.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💡</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">Reflective Intelligence</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Patience creates space for deep reflection and contemplation. This reflective practice develops
                  your intelligence by allowing you to analyze experiences, understand cause and effect, and extract
                  valuable lessons that guide future decisions and actions.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚖️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">Balanced Judgment</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Patient people develop balanced judgment by taking time to consider multiple perspectives before
                  making decisions. This balanced approach leads to fairer, more thoughtful conclusions that consider
                  the well-being of all involved parties.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🎯</span>
                  </div>
                </div>
                <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">Strategic Thinking</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Patience develops strategic thinking by teaching you to consider long-term consequences and plan
                  carefully for the future. This forward-thinking approach helps you make choices that create
                  lasting positive outcomes rather than quick, temporary fixes.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌟</span>
                  </div>
                </div>
                <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">Wisdom Integration</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Through patient practice and reflection, you learn to integrate knowledge with wisdom, understanding
                  with compassion, and intelligence with humility. This integration creates a balanced character that
                  benefits both personal growth and service to others.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Awe-Inspiring Development Facts */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Amazing Facts About Patience & Spiritual Development
          </h2>
          <div className="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-lg p-8 border border-blue-200 dark:border-blue-800">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⏰</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">Divine Timing & Wisdom</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Everything in creation follows perfect divine timing. The sun rises at its appointed time, seasons
                  change in perfect order, and your heart beats in rhythm with divine wisdom. Patience aligns you
                  with this perfect timing, bringing peace and understanding.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💎</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">Heart Confirmation Through Patience</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  When you practice patience, your heart confirms the truth that everything happens for a reason.
                  This inner knowing brings deep peace and trust in divine wisdom. Your heart becomes a source of
                  certainty and calm in all situations.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🧠</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">Intellectual Living Through Patience</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Patience develops your intelligence by teaching you to think deeply and carefully. You learn to
                  see patterns, understand consequences, and make wise decisions. This intellectual growth helps
                  you live with wisdom and understanding in all areas of life.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚖️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">Perfect Alignment & Balance</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Patience brings you into perfect alignment with divine will and natural order. This alignment
                  creates balance in your thoughts, emotions, and actions. You become like a tree that bends with
                  the wind but remains rooted in wisdom and faith.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌱</span>
                  </div>
                </div>
                <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">Path to Inner Growth</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Every moment of patience is a step toward inner growth and spiritual development. Through patient
                  endurance, your soul becomes stronger, your faith deeper, and your understanding clearer. This
                  growth continues throughout your life, bringing you closer to divine wisdom.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">✨</span>
                  </div>
                </div>
                <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">Gateway to Enlightenment</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Patience opens the door to enlightenment by teaching you to see divine wisdom in every situation.
                  Through patient observation and acceptance, you recognize the perfect plan behind all events,
                  leading to deep peace and spiritual understanding.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Patience Virtues */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-8">
            The Virtues of Patience
          </h2>
          <div className="space-y-8">
            {patienceVirtues.map((virtue, index) => {
              const Icon = virtue.icon
              return (
                <Card key={index} className="overflow-hidden">
                  <CardHeader>
                    <div className="flex items-center space-x-3 mb-4">
                      <Icon className={`h-8 w-8 ${virtue.color}`} />
                      <div>
                        <CardTitle className="text-xl">{virtue.title}</CardTitle>
                        <p className="text-gray-600 dark:text-gray-300">{virtue.description}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Benefits */}
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Benefits</h4>
                        <ul className="space-y-2">
                          {virtue.benefits.map((benefit, benefitIndex) => (
                            <li key={benefitIndex} className="flex items-center">
                              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                              <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Practices */}
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Practices</h4>
                        <ul className="space-y-2">
                          {virtue.practices.map((practice, practiceIndex) => (
                            <li key={practiceIndex} className="flex items-center">
                              <Target className="h-4 w-4 text-blue-500 mr-2" />
                              <span className="text-gray-700 dark:text-gray-300">{practice}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    {/* Wisdom Quote */}
                    <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-blue-800 dark:text-blue-300 italic">"{virtue.wisdom}"</p>
                    </div>

                    {/* Research/Percentage */}
                    <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-green-800 dark:text-green-300 text-sm">
                        📊 Research shows: {virtue.percentage}
                      </p>
                    </div>

                    <div className="flex justify-between items-center mt-4">
                      <div className="flex space-x-2">
                        <FunctionalButton type="bookmark" itemId={`patience-${index}`} size="sm" />
                        <FunctionalButton type="share" size="sm" />
                      </div>
                      <Button size="sm" onClick={() => alert(`Starting ${virtue.title} practice...`)}>
                        Start Practice
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Patience Exercises */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-8">
            Patience Development Exercises
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {patienceExercises.map((exercise, index) => (
              <Card key={index} className={selectedExercise === index ? 'ring-2 ring-blue-500' : ''}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{exercise.name}</CardTitle>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{exercise.duration}</p>
                    </div>
                    {selectedExercise === index && (
                      <div className="text-right">
                        <div className="text-2xl font-mono text-blue-600">{formatTime(exerciseTimer)}</div>
                        <div className="flex space-x-1">
                          <Button size="sm" variant="outline" onClick={toggleTimer}>
                            {isTimerRunning ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                          </Button>
                          <Button size="sm" variant="outline" onClick={resetExercise}>
                            Reset
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">{exercise.description}</p>
                  
                  <div className="mb-4">
                    <h4 className="font-semibold mb-2">Steps:</h4>
                    <ol className="space-y-1">
                      {exercise.steps.map((step, stepIndex) => (
                        <li key={stepIndex} className="text-sm text-gray-600 dark:text-gray-300">
                          {stepIndex + 1}. {step}
                        </li>
                      ))}
                    </ol>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold mb-2">Benefits:</h4>
                    <div className="flex flex-wrap gap-2">
                      {exercise.benefits.map((benefit, benefitIndex) => (
                        <span key={benefitIndex} className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs">
                          {benefit}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      className="flex-1"
                      onClick={() => startExercise(index)}
                      disabled={selectedExercise === index}
                    >
                      {selectedExercise === index ? 'In Progress' : 'Start Exercise'}
                    </Button>
                    <FunctionalButton type="bookmark" itemId={`exercise-${index}`} size="sm" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Daily Patience Tips */}
        <Card>
          <CardHeader>
            <CardTitle className="text-center">Daily Patience Cultivation Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center space-y-2">
                <Sun className="h-8 w-8 text-yellow-600 mx-auto" />
                <h3 className="font-semibold">Morning Practice</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Start each day with intention to practice patience in all interactions
                </p>
              </div>
              <div className="text-center space-y-2">
                <Mountain className="h-8 w-8 text-gray-600 mx-auto" />
                <h3 className="font-semibold">Face Challenges</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  View obstacles as opportunities to strengthen your patience muscle
                </p>
              </div>
              <div className="text-center space-y-2">
                <Waves className="h-8 w-8 text-blue-600 mx-auto" />
                <h3 className="font-semibold">Flow with Life</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Accept that life has its own rhythm and timing for everything
                </p>
              </div>
              <div className="text-center space-y-2">
                <Moon className="h-8 w-8 text-purple-600 mx-auto" />
                <h3 className="font-semibold">Evening Reflection</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Reflect on moments of patience and areas for improvement
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
