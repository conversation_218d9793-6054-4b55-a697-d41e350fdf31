# Light Upon Light
 HEAD
# Light Upon Light - Universal Wisdom & Learning Platform

A comprehensive educational platform that combines ancient wisdom with modern science, natural healing with logical thinking, and spiritual insights with practical applications. Built for a multi-audience approach that emphasizes logic, intellect, benefits, and universal principles.

## 🌟 Features

### Core Learning Areas
- **Natural Healing & Plants**: Science-based exploration of medicinal plants, cupping therapy, and natural remedies
- **Honey & Natural Foods**: Nutritional science and healing properties of natural foods
- **Logic & Intelligence**: Development of critical thinking, problem-solving, and analytical skills
- **Ancient Wisdom**: Universal principles from various traditions presented through logical frameworks
- **Heart-Mind Integration**: Emotional intelligence combined with rational thinking
- **Quranic Logic**: Verses presented with scientific insights and universal benefits
- **99 Divine Attributes**: Perfect qualities as frameworks for human development

### Platform Features
- **Live Learning Sessions**: Interactive educational broadcasts with camera/microphone support
- **Community Forum**: Structured discussions across multiple categories
- **Learning Paths**: Guided educational journeys with progress tracking
- **Achievement System**: Gamified learning with community recognition
- **Multi-language Support**: Content available in multiple languages
- **GDPR Compliant**: Full privacy protection and data compliance

## 🚀 Technology Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Authentication, Real-time)
- **UI Components**: Custom components with Radix UI primitives
- **Icons**: Lucide React
- **Styling**: Tailwind CSS with dark mode support
- **Authentication**: Supabase Auth with Google OAuth
- **Database**: PostgreSQL with Row Level Security

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account
- Modern web browser with camera/microphone support (for live features)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd light-upon-light
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ADMIN_EMAIL=your_admin_email
   NEXTAUTH_SECRET=your_nextauth_secret
   NEXTAUTH_URL=http://localhost:3000
   ```

4. **Set up the database**
   - Go to your Supabase dashboard
   - Navigate to SQL Editor
   - Run the `database-setup.sql` script to create all tables and policies

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:3000`

## 🗄️ Database Schema

The platform uses a comprehensive database schema including:

- **User Management**: Profiles, roles, achievements
- **Forum System**: Categories, posts, replies with moderation
- **Learning System**: Paths, progress tracking, events
- **Live Streaming**: Sessions, chat messages, viewer management
- **Content Management**: Dynamic pages, metadata

See `database-setup.sql` for the complete schema.

## 🎯 Key Pages & Features

### Educational Content
- `/plants` - Medicinal plants and natural healing
- `/honey` - Natural foods and their benefits
- `/cupping` - Traditional cupping therapy with scientific backing
- `/heart-mind` - Emotional and intellectual development
- `/logic` - Critical thinking and intelligence development
- `/wisdom` - Ancient wisdom with modern applications
- `/quran` - Quranic verses with logical insights
- `/names-of-god` - Divine attributes as development frameworks

### Community Features
- `/community` - Learning community hub
- `/forum` - Discussion forums
- `/live` - Live streaming with camera/microphone support
- `/profile` - User profiles and progress

### Legal & Compliance
- `/privacy` - GDPR-compliant privacy policy
- `/terms` - Terms of service
- `/cookies` - Cookie policy

## 🔐 Authentication & Authorization

- **Supabase Auth**: Email/password and OAuth (Google)
- **Role-based Access**: User, Moderator, Admin roles
- **Row Level Security**: Database-level security policies
- **Admin Features**: Stream management, content moderation

## 🌐 Multi-Audience Approach

The platform is designed to appeal to diverse audiences by:

- **Universal Principles**: Presenting wisdom through logical frameworks
- **Scientific Backing**: Supporting traditional knowledge with modern research
- **Practical Benefits**: Emphasizing real-world applications and benefits
- **Inclusive Language**: Avoiding exclusive religious terminology
- **Evidence-Based**: Providing scientific explanations and logical reasoning

## 📱 Responsive Design

- Mobile-first approach
- Dark/light mode support
- Accessible design principles
- Cross-browser compatibility

## 🔧 Development

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
```

### Code Structure

```
src/
├── app/             # Next.js app router pages
├── components/      # Reusable React components
├── lib/            # Utility functions and configurations
├── types/          # TypeScript type definitions
└── styles/         # Global styles
```

## 🚀 Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Deploy to Vercel** (recommended)
   ```bash
   vercel deploy
   ```

3. **Set environment variables** in your deployment platform

4. **Configure domain** and SSL certificates

## 🔒 Security Features

- Row Level Security (RLS) on all database tables
- CSRF protection
- Input sanitization
- Secure authentication flows
- GDPR compliance
- Cookie consent management

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support, please:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information
4. Contact: <EMAIL>

## 🙏 Acknowledgments

- Ancient wisdom traditions for timeless principles
- Modern scientific research for evidence-based insights
- Open source community for tools and libraries
- Contributors and community members

---

**Light Upon Light** - Illuminating minds through wisdom, logic, and natural intelligence.

# lightuponlight
 91cb14bac30720a7dde8f092d47ee96dd7ae76fb
