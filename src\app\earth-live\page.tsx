"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollToTopWithProgress } from '@/components/scroll-to-top';
import { FunctionalButton } from '@/components/functional-buttons';
import {
  ArrowLeft,
  Globe,
  Satellite,
  BookOpen,
  Heart,
  Leaf,
  Star,
  Clock,
  Eye,
  MessageCircle,
  ExternalLink,
  Radio,
} from 'lucide-react';

export default function EarthLivePage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-6">
            <Link href="/">
              <Button variant="outline" size="sm" className="mr-4 text-white border-white hover:bg-white hover:text-blue-600">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </Link>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-6">
              <Globe className="h-12 w-12 text-white mr-4" />
              <h1 className="text-4xl md:text-5xl font-bold text-white">
                NASA Earth Live
              </h1>
            </div>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Experience our beautiful planet Earth in real-time from the International Space Station. 
              Witness the natural beauty and interconnected systems of our world from space.
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-400">
            <Link href="/" className="hover:text-white transition-colors">
              Home
            </Link>
            <span>/</span>
            <span className="text-white">NASA Earth Live</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Video Player */}
          <div className="lg:col-span-3">
            <Card className="bg-gray-800 border-gray-700 mb-6">
              <CardContent className="p-0">
                <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
                  <iframe
                    src="https://www.youtube.com/embed/DIgkvm2nmHc?autoplay=1&mute=1"
                    title="NASA Live: Earth View from the International Space Station"
                    className="w-full h-full"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    allowFullScreen
                    style={{ border: 'none' }}
                  />
                  <div className="absolute top-4 left-4 bg-black bg-opacity-75 rounded-lg px-3 py-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <span className="text-white text-sm font-medium">🌍 NASA Earth Live</span>
                    </div>
                  </div>
                  <div className="absolute top-4 right-4 bg-black bg-opacity-75 rounded-lg px-3 py-2">
                    <div className="flex items-center space-x-2 text-white text-sm">
                      <Satellite className="h-4 w-4 text-blue-400" />
                      <span>International Space Station</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Video Information */}
            <Card className="bg-gray-800 border-gray-700 mb-6">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h2 className="text-2xl font-bold text-white mb-2">
                      NASA Earth Live: View from the International Space Station
                    </h2>
                    <div className="flex items-center space-x-4 text-sm text-gray-400 mb-4">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>Live Stream</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>24/7 When ISS is in Sunlight</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span>NASA Public Domain</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="prose prose-invert max-w-none mb-6">
                  <p className="text-gray-300 leading-relaxed">
                   The space station orbits Earth about 250 miles (425 kilometers) above the surface. An international partnership of five space agencies from 15 countries operates the station, and it has been
                    continuously occupied since November 2000. It's a microgravity laboratory where science, research, and human innovation make way for new technologies and research breakthroughs not possible on Earth. More: https://go.nasa.gov/3CkVtC8 providing breathtaking views of our 
                    world's natural beauty, weather systems, and the thin atmosphere that protects all life.
                  </p>
                  
                  <h3 className="text-xl font-semibold text-white mt-6 mb-3">What You'll See:</h3>
                  <ul className="text-gray-300 space-y-2">
                    <li>• <strong>Earth's Natural Beauty:</strong> Continents, oceans, clouds, and weather patterns</li>
                    <li>• <strong>Day and Night Cycles:</strong> Sunrise and sunset every 45 minutes</li>
                    <li>• <strong>Aurora Phenomena:</strong> Northern and Southern Lights from space</li>
                    <li>• <strong>City Lights:</strong> Human civilization visible at night</li>
                    <li>• <strong>Natural Phenomena:</strong> Storms, lightning, and atmospheric effects</li>
                  </ul>

                  <h3 className="text-xl font-semibold text-white mt-6 mb-3">Educational Value:</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-300">
                    <div>
                      <h4 className="font-medium text-blue-400 mb-2">🌍 Earth Sciences</h4>
                      <ul className="text-sm space-y-1">
                        <li>• Atmospheric dynamics</li>
                        <li>• Weather pattern observation</li>
                        <li>• Ocean and land interactions</li>
                        <li>• Environmental monitoring</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-purple-400 mb-2">🚀 Space Science</h4>
                      <ul className="text-sm space-y-1">
                        <li>• Orbital mechanics</li>
                        <li>• Space station operations</li>
                        <li>• Microgravity research</li>
                        <li>• International cooperation</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap items-center gap-3 pt-4 border-t border-gray-700">
                  <FunctionalButton
                    type="like"
                    itemId="nasa-earth-live"
                    count={892}
                    size="sm"
                  />

                  <FunctionalButton
                    type="bookmark"
                    itemId="nasa-earth-live"
                    size="sm"
                  />

                  <FunctionalButton
                    type="share"
                    size="sm"
                  />

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (typeof window !== 'undefined') {
                        window.open('https://www.nasa.gov/live/', '_blank');
                      }
                    }}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    NASA Official Site
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (typeof window !== 'undefined') {
                        navigator.clipboard.writeText(window.location.href);
                      }
                    }}
                  >
                    <Radio className="h-4 w-4 mr-2" />
                    Copy Link
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Legal & Educational Context */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BookOpen className="h-5 w-5 mr-2" />
                  Legal & Educational Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 text-gray-300">
                  <div className="p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
                    <h4 className="font-semibold text-green-400 mb-2">✅ NASA Public Domain</h4>
                    <p className="text-sm">
                      This content is provided by NASA and is in the public domain. NASA content 
                      is not copyrighted and may be used for educational, informational, and 
                      commercial purposes without permission.
                    </p>
                  </div>
                  
                  <div className="p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
                    <h4 className="font-semibold text-blue-400 mb-2">🎓 Educational Purpose</h4>
                    <p className="text-sm">
                      This live stream serves educational purposes, helping viewers understand 
                      Earth's natural systems, space science, and our planet's place in the universe. 
                      It promotes scientific literacy and environmental awareness.
                    </p>
                  </div>

                  <div className="p-4 bg-purple-900/20 border border-purple-500/30 rounded-lg">
                    <h4 className="font-semibold text-purple-400 mb-2">🌍 Environmental Awareness</h4>
                    <p className="text-sm">
                      Viewing Earth from space provides a unique perspective on our planet's 
                      interconnected systems and the importance of environmental stewardship. 
                      This "overview effect" often inspires greater care for our world.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* ISS Information */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Satellite className="h-5 w-5 mr-2" />
                  ISS Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Altitude:</span>
                    <span className="text-white">~400 km</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400"></span>
                    <span className="text-white"></span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400"></span>
                    <span className="text-white"></span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400"></span>
                    <span className="text-white"></span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Related Content */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg">Related Content</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Link href="/nature-elements" className="block">
                  <div className="p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <div className="flex items-center space-x-3">
                      <Globe className="h-8 w-8 text-blue-400" />
                      <div>
                        <h4 className="font-medium text-white">Nature Elements</h4>
                        <p className="text-sm text-gray-400">Earth's natural systems</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link href="/heavens-earth" className="block">
                  <div className="p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <div className="flex items-center space-x-3">
                      <Star className="h-8 w-8 text-purple-400" />
                      <div>
                        <h4 className="font-medium text-white">Heavens & Earth</h4>
                        <p className="text-sm text-gray-400">Cosmic perspective</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link href="/plants" className="block">
                  <div className="p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <div className="flex items-center space-x-3">
                      <Leaf className="h-8 w-8 text-green-400" />
                      <div>
                        <h4 className="font-medium text-white">Plants & Life</h4>
                        <p className="text-sm text-gray-400">Earth's living systems</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link href="/live" className="block">
                  <div className="p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <div className="flex items-center space-x-3">
                      <Radio className="h-8 w-8 text-red-400" />
                      <div>
                        <h4 className="font-medium text-white">Live Sessions</h4>
                        <p className="text-sm text-gray-400">Interactive learning</p>
                      </div>
                    </div>
                  </div>
                </Link>
              </CardContent>
            </Card>

            {/* Quick Navigation */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg">Explore More</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/content">
                  <Button variant="outline" className="w-full justify-start">
                    <BookOpen className="h-4 w-4 mr-2" />
                    All Content
                  </Button>
                </Link>
                
                <Link href="/community">
                  <Button variant="outline" className="w-full justify-start">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Community
                  </Button>
                </Link>
                
                <Link href="/quiz">
                  <Button variant="outline" className="w-full justify-start">
                    <Star className="h-4 w-4 mr-2" />
                    Quiz & Learning
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Enhanced scroll to top with progress */}
      <ScrollToTopWithProgress threshold={200} />
    </div>
  );
}
