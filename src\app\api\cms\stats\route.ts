import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';

// GET /api/cms/stats - Get CMS statistics for admin dashboard
export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin/moderator
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || !['admin', 'moderator'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get content statistics
    const [
      totalContentResult,
      published<PERSON><PERSON>nt<PERSON><PERSON>ult,
      draftContentResult,
      totalViewsResult,
      contentByTypeResult,
      recentActivityResult
    ] = await Promise.all([
      // Total content count
      supabase
        .from('cms_content')
        .select('id', { count: 'exact', head: true })
        .neq('status', 'archived'),

      // Published content count
      supabase
        .from('cms_content')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'published'),

      // Draft content count
      supabase
        .from('cms_content')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'draft'),

      // Total views
      supabase
        .from('cms_content')
        .select('view_count')
        .neq('status', 'archived'),

      // Content by type
      supabase
        .from('cms_content')
        .select('content_type_id')
        .neq('status', 'archived'),

      // Recent activity (last 10 activities)
      supabase
        .from('cms_content')
        .select(`
          id,
          title,
          status,
          created_at,
          updated_at,
          author:users!author_id(full_name)
        `)
        .neq('status', 'archived')
        .order('updated_at', { ascending: false })
        .limit(10)
    ]);

    // Calculate total views
    const totalViews = totalViewsResult.data?.reduce((sum, item) => sum + (item.view_count || 0), 0) || 0;

    // Calculate content by type
    const contentByType: Record<string, number> = {};
    contentByTypeResult.data?.forEach(item => {
      contentByType[item.content_type_id] = (contentByType[item.content_type_id] || 0) + 1;
    });

    // Format recent activity
    const recentActivity = recentActivityResult.data?.map(item => ({
      id: item.id,
      action: item.created_at === item.updated_at ? 'created' : 'updated',
      content_title: item.title,
      author: (item.author as any)?.full_name || 'Unknown',
      timestamp: item.updated_at,
    })) || [];

    const stats = {
      totalContent: totalContentResult.count || 0,
      publishedContent: publishedContentResult.count || 0,
      draftContent: draftContentResult.count || 0,
      totalViews,
      contentByType,
      recentActivity,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Error in GET /api/cms/stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch statistics' },
      { status: 500 }
    );
  }
}
