import { Droplets, Wind, Flame, Mountain, Sun, Moon, Star, Zap, Leaf, Heart } from 'lucide-react'

const naturalElements = [
  {
    name: 'Water',
    symbol: '💧',
    properties: ['Fluidity', 'Adaptability', 'Purification', 'Life-giving'],
    scientificFacts: [
      'Covers 71% of Earth\'s surface',
      'Human body is 60% water',
      'Universal solvent for biological processes',
      'Exists in three states: solid, liquid, gas'
    ],
    psychologicalTraits: ['Emotional flow', 'Intuition', 'Healing', 'Cleansing'],
    practicalApplications: [
      'Hydration for optimal health',
      'Water therapy and healing',
      'Meditation by water sources',
      'Environmental conservation'
    ],
    wisdom: 'Water teaches us persistence and adaptability. It always finds a way, flowing around obstacles rather than fighting them directly.',
    icon: Droplets,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20'
  },
  {
    name: 'Air',
    symbol: '💨',
    properties: ['Movement', 'Communication', 'Breath', 'Freedom'],
    scientificFacts: [
      '78% nitrogen, 21% oxygen, 1% other gases',
      'Essential for cellular respiration',
      'Carries sound waves for communication',
      'Creates weather patterns and climate'
    ],
    psychologicalTraits: ['Mental clarity', 'Communication', 'Ideas', 'Inspiration'],
    practicalApplications: [
      'Breathing exercises for health',
      'Air quality awareness',
      'Wind energy utilization',
      'Mindful breathing practices'
    ],
    wisdom: 'Air reminds us of the invisible forces that sustain life. Like thoughts and ideas, the most powerful things are often unseen.',
    icon: Wind,
    color: 'text-gray-600',
    bgColor: 'bg-gray-50 dark:bg-gray-900/20'
  },
  {
    name: 'Fire',
    symbol: '🔥',
    properties: ['Transformation', 'Energy', 'Passion', 'Purification'],
    scientificFacts: [
      'Rapid oxidation releasing heat and light',
      'Enabled human civilization advancement',
      'Solar fire powers all life on Earth',
      'Chemical reaction converting matter to energy'
    ],
    psychologicalTraits: ['Passion', 'Willpower', 'Transformation', 'Energy'],
    practicalApplications: [
      'Controlled energy for cooking and warmth',
      'Solar energy harnessing',
      'Metabolic fire for health',
      'Focused intention and goals'
    ],
    wisdom: 'Fire teaches us about transformation and the power of controlled energy. It can create or destroy, depending on how it\'s managed.',
    icon: Flame,
    color: 'text-red-600',
    bgColor: 'bg-red-50 dark:bg-red-900/20'
  },
  {
    name: 'Earth',
    symbol: '🌍',
    properties: ['Stability', 'Grounding', 'Nourishment', 'Foundation'],
    scientificFacts: [
      'Provides minerals essential for life',
      'Supports all terrestrial ecosystems',
      'Magnetic field protects from radiation',
      'Gravity creates stable environment'
    ],
    psychologicalTraits: ['Stability', 'Practicality', 'Patience', 'Reliability'],
    practicalApplications: [
      'Grounding practices for mental health',
      'Sustainable agriculture',
      'Mineral nutrition',
      'Connection with nature'
    ],
    wisdom: 'Earth teaches us patience and the importance of strong foundations. Growth requires both deep roots and time.',
    icon: Mountain,
    color: 'text-green-600',
    bgColor: 'bg-green-50 dark:bg-green-900/20'
  },
  {
    name: 'Light',
    symbol: '☀️',
    properties: ['Illumination', 'Energy', 'Clarity', 'Growth'],
    scientificFacts: [
      'Electromagnetic radiation visible to humans',
      'Enables photosynthesis and life',
      'Travels at 299,792,458 meters per second',
      'Regulates circadian rhythms'
    ],
    psychologicalTraits: ['Awareness', 'Knowledge', 'Optimism', 'Clarity'],
    practicalApplications: [
      'Natural light for health',
      'Solar energy systems',
      'Light therapy treatments',
      'Vitamin D synthesis'
    ],
    wisdom: 'Light represents knowledge and awareness. It dispels darkness and reveals truth, enabling growth and understanding.',
    icon: Sun,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50 dark:bg-yellow-900/20'
  },
  {
    name: 'Space',
    symbol: '🌌',
    properties: ['Expansion', 'Potential', 'Silence', 'Infinity'],
    scientificFacts: [
      'Contains all matter and energy',
      'Expanding since the Big Bang',
      'Mostly empty space even in atoms',
      'Provides context for all existence'
    ],
    psychologicalTraits: ['Openness', 'Possibility', 'Peace', 'Perspective'],
    practicalApplications: [
      'Meditation and mindfulness',
      'Creating physical and mental space',
      'Astronomical observation',
      'Perspective on life challenges'
    ],
    wisdom: 'Space teaches us about potential and perspective. In emptiness, we find infinite possibility and peace.',
    icon: Star,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20'
  }
]

const elementalBalance = [
  {
    aspect: 'Physical Health',
    description: 'Balance all elements for optimal physical well-being',
    practices: [
      'Water: Proper hydration and cleansing',
      'Air: Deep breathing and fresh air',
      'Fire: Appropriate exercise and metabolism',
      'Earth: Grounding and mineral nutrition',
      'Light: Natural sunlight exposure',
      'Space: Rest and recovery time'
    ]
  },
  {
    aspect: 'Mental Clarity',
    description: 'Use elemental principles for cognitive enhancement',
    practices: [
      'Water: Emotional flow and adaptability',
      'Air: Clear communication and ideas',
      'Fire: Focused attention and passion',
      'Earth: Practical thinking and stability',
      'Light: Knowledge and understanding',
      'Space: Mental quietude and perspective'
    ]
  },
  {
    aspect: 'Spiritual Growth',
    description: 'Integrate elemental wisdom for spiritual development',
    practices: [
      'Water: Purification and healing',
      'Air: Breath awareness and spirit',
      'Fire: Transformation and purification',
      'Earth: Grounding and connection',
      'Light: Illumination and wisdom',
      'Space: Unity and transcendence'
    ]
  }
]

export default function NatureElementsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 dark:from-green-900/20 dark:via-blue-900/20 dark:to-purple-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6 space-x-2">
              <Droplets className="h-12 w-12 text-blue-600" />
              <Wind className="h-12 w-12 text-gray-600" />
              <Flame className="h-12 w-12 text-red-600" />
              <Mountain className="h-12 w-12 text-green-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Nature's Fundamental Elements
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Explore the scientific principles and universal wisdom of natural elements. 
              Understand how water, air, fire, earth, light, and space influence our physical health, 
              mental clarity, and spiritual development.
            </p>
          </div>
        </div>
      </div>

      {/* Wisdom Quote */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="verse-container text-center">
          <div className="flex justify-center mb-4">
            <Zap className="h-8 w-8 text-yellow-600" />
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4">
            "In the harmony of natural elements, we find the blueprint for balanced living. 
            Each element teaches us essential principles for health, wisdom, and growth."
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Universal Natural Philosophy
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* Elements Grid */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            The Six Fundamental Elements
          </h2>
          <div className="space-y-8">
            {naturalElements.map((element, index) => {
              const Icon = element.icon
              return (
                <div
                  key={index}
                  className={`${element.bgColor} rounded-lg shadow-lg overflow-hidden`}
                >
                  <div className="p-8">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-4">
                        <div className="text-4xl">{element.symbol}</div>
                        <Icon className={`h-8 w-8 ${element.color}`} />
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                            {element.name}
                          </h3>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {element.properties.map((property, propIndex) => (
                              <span
                                key={propIndex}
                                className={`px-2 py-1 ${element.color} bg-white dark:bg-gray-800 rounded text-sm font-medium`}
                              >
                                {property}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                      <Heart className="h-6 w-6 text-red-500" />
                    </div>

                    {/* Wisdom */}
                    <div className="mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        Elemental Wisdom
                      </h4>
                      <p className="text-gray-700 dark:text-gray-300 italic">
                        {element.wisdom}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      {/* Scientific Facts */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Scientific Facts
                        </h4>
                        <ul className="space-y-2">
                          {element.scientificFacts.map((fact, factIndex) => (
                            <li key={factIndex} className="flex items-start">
                              <Zap className="h-4 w-4 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-700 dark:text-gray-300 text-sm">{fact}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Psychological Traits */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Psychological Traits
                        </h4>
                        <ul className="space-y-2">
                          {element.psychologicalTraits.map((trait, traitIndex) => (
                            <li key={traitIndex} className="flex items-start">
                              <Heart className="h-4 w-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-700 dark:text-gray-300 text-sm">{trait}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Practical Applications */}
                      <div className="md:col-span-2">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Practical Applications
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {element.practicalApplications.map((application, appIndex) => (
                            <div key={appIndex} className="flex items-start">
                              <Leaf className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-700 dark:text-gray-300 text-sm">{application}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Elemental Balance */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Achieving Elemental Balance
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {elementalBalance.map((balance, index) => (
              <div key={index} className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {balance.aspect}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {balance.description}
                </p>
                <div className="space-y-2">
                  {balance.practices.map((practice, practiceIndex) => (
                    <div key={practiceIndex} className="text-sm text-gray-700 dark:text-gray-300">
                      <strong className="text-gray-900 dark:text-white">
                        {practice.split(':')[0]}:
                      </strong>
                      <span className="ml-1">{practice.split(':')[1]}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Integration Guide */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Daily Elemental Integration
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="text-center">
                <Sun className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Morning Practice
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Start with light (sunlight exposure), air (deep breathing), 
                  and water (hydration) to energize your day.
                </p>
              </div>
              <div className="text-center">
                <Mountain className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Midday Balance
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Ground yourself with earth connection, maintain fire energy 
                  through focused work, and create space for reflection.
                </p>
              </div>
              <div className="text-center">
                <Moon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Evening Harmony
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Wind down with water (cleansing), space (meditation), 
                  and gentle air practices for peaceful rest.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Live in Harmony with Nature
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Understanding and working with natural elements creates balance in all aspects of life. 
              Each element offers unique gifts and lessons for optimal health, clarity, and growth.
            </p>
            <div className="flex justify-center space-x-8">
              <div className="text-center">
                <Droplets className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Flow & Adapt</p>
              </div>
              <div className="text-center">
                <Flame className="h-8 w-8 text-red-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Transform & Energize</p>
              </div>
              <div className="text-center">
                <Mountain className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Ground & Stabilize</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
