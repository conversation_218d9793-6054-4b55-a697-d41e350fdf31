import Link from 'next/link'
import { Sun, Heart, BookOpen, Users, Mail, MapPin, Phone } from 'lucide-react'

const footerLinks = {
  'Learning Paths': [
    { name: 'Cupping Therapy', href: '/cupping' },
    { name: 'Heart & Mind Development', href: '/heart-mind' },
    { name: 'Healing & Meditation', href: '/healing' },
    { name: 'Nature Elements', href: '/nature-elements' },
    { name: 'Heavens & Earth', href: '/heavens-earth' },
  ],
  'Spiritual Resources': [
    { name: '99 Names of God', href: '/names-of-god' },
    { name: 'Quranic Verses', href: '/quran' },
    { name: 'Nature Law', href: '/nature-law' },
    { name: 'Therapy', href: '/therapy' },
    { name: 'Quiz & Learning', href: '/quiz' },
  ],
  'Community': [
    { name: 'Discussion Forum', href: '/forum' },
    { name: 'Live Streaming', href: '/live' },
    { name: 'Community Learning', href: '/community' },
    { name: 'Patient Experience', href: '/patient-experience' },
  ],
  'Legal & Support': [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Site Map', href: '/sitemap' },
    { name: 'Cookie Policy', href: '/cookies' },
    { name: 'GDPR Compliance', href: '/gdpr' },
    { name: 'Contact Us', href: '/contact' },
  ],
}

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <Sun className="h-8 w-8 text-yellow-500" />
              <span className="text-xl font-bold">Light Upon Light</span>
            </div>
            <p className="text-gray-300 mb-4">
              Illuminating hearts and minds through divine wisdom, natural healing, and spiritual growth.
            </p>
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <Mail className="h-4 w-4" />
              <span><EMAIL></span>
            </div>
          </div>

          {/* Links Sections */}
          {Object.entries(footerLinks).map(([category, links]) => (
            <div key={category}>
              <h3 className="text-lg font-semibold mb-4">{category}</h3>
              <ul className="space-y-2">
                {links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors duration-200"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Quranic Verse */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="text-center mb-8">
            <div className="arabic-text text-xl mb-2 text-yellow-400">
              وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا
            </div>
            <p className="text-gray-300 text-sm">
              "And whoever fears Allah - He will make for him a way out." - Quran 65:2
            </p>
          </div>
        </div>

        {/* Educational Purposes Legal Disclaimer */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="bg-amber-900/20 border border-amber-800 rounded-lg p-6">
            <div className="flex items-center justify-center mb-4">
              <Heart className="h-5 w-5 text-amber-400 mr-2" />
              <span className="text-lg font-semibold text-amber-300">Important Educational Disclaimer</span>
            </div>
            <div className="text-center space-y-3">
              <p className="text-amber-200 text-sm leading-relaxed">
                <strong>Note:</strong> Death is not curable. Kindly remember to care for your inner safety and awaken to the return of inner life —
                life filled with consciousness and understanding. Journey towards straight guidance and home of peace and light.
              </p>
              <p className="text-amber-200 text-sm leading-relaxed">
                <strong>Educational Purposes Only:</strong> All content on this website is provided for educational and informational purposes only.
                This information should not replace professional medical advice, diagnosis, or treatment. Always consult with qualified healthcare
                providers before starting any new therapy or making health-related decisions.
              </p>
              <p className="text-amber-200 text-xs">
                Explore more on <Link href="/heart-mind" className="underline hover:text-amber-100">heart development</Link>,
                <Link href="/cupping" className="underline hover:text-amber-100"> cupping</Link>,
                and <Link href="/privacy" className="underline hover:text-amber-100"> legal disclaimers</Link>.
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-gray-400 text-sm mb-4 md:mb-0">
            © {new Date().getFullYear()} Light Upon Light. All rights reserved.
          </div>
          <div className="flex space-x-6 text-sm text-gray-400">
            <span>🌟 GDPR Compliant</span>
            <span>🔒 Privacy Protected</span>
            <span>♿ Accessible</span>
            <span>⚡ Lighthouse Optimized</span>
          </div>
        </div>

        {/* Multi-language Notice */}
        <div className="text-center mt-6 text-xs text-gray-500">
          Available in: English • العربية • Français • Español • اردو • Svenska
        </div>
      </div>
    </footer>
  )
}
