/**
 * Accessibility Audit Utilities
 * WCAG 2.1 AA compliance checking and enhancement tools
 */

// WCAG 2.1 Guidelines
export const WCAG_GUIDELINES = {
  AA: {
    colorContrast: {
      normal: 4.5,
      large: 3.0
    },
    textSize: {
      minimum: 16,
      large: 18
    }
  },
  AAA: {
    colorContrast: {
      normal: 7.0,
      large: 4.5
    }
  }
} as const;

// Accessibility Issues
export interface AccessibilityIssue {
  type: 'error' | 'warning' | 'info';
  category: 'color-contrast' | 'keyboard-navigation' | 'screen-reader' | 'focus-management' | 'semantic-html' | 'images';
  element: string;
  description: string;
  recommendation: string;
  wcagReference: string;
}

// Color Contrast Utilities
export class ColorContrastChecker {
  // Convert hex to RGB
  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  // Calculate relative luminance
  private getLuminance(r: number, g: number, b: number): number {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  // Calculate contrast ratio
  getContrastRatio(color1: string, color2: string): number {
    const rgb1 = this.hexToRgb(color1);
    const rgb2 = this.hexToRgb(color2);
    
    if (!rgb1 || !rgb2) return 0;

    const lum1 = this.getLuminance(rgb1.r, rgb1.g, rgb1.b);
    const lum2 = this.getLuminance(rgb2.r, rgb2.g, rgb2.b);
    
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    
    return (brightest + 0.05) / (darkest + 0.05);
  }

  // Check if contrast meets WCAG standards
  meetsWCAG(foreground: string, background: string, level: 'AA' | 'AAA' = 'AA', isLargeText = false): {
    passes: boolean;
    ratio: number;
    required: number;
  } {
    const ratio = this.getContrastRatio(foreground, background);
    const required = isLargeText 
      ? WCAG_GUIDELINES[level].colorContrast.large 
      : WCAG_GUIDELINES[level].colorContrast.normal;
    
    return {
      passes: ratio >= required,
      ratio,
      required
    };
  }
}

// Accessibility Auditor
export class AccessibilityAuditor {
  private issues: AccessibilityIssue[] = [];
  private colorChecker = new ColorContrastChecker();

  // Audit entire page
  auditPage(): AccessibilityIssue[] {
    this.issues = [];
    
    this.auditImages();
    this.auditHeadings();
    this.auditForms();
    this.auditLinks();
    this.auditColorContrast();
    this.auditKeyboardNavigation();
    this.auditARIA();
    
    return this.issues;
  }

  // Audit images for alt text
  private auditImages(): void {
    const images = document.querySelectorAll('img');
    
    images.forEach((img, index) => {
      if (!img.alt) {
        this.issues.push({
          type: 'error',
          category: 'images',
          element: `img[${index}]`,
          description: 'Image missing alt text',
          recommendation: 'Add descriptive alt text for screen readers',
          wcagReference: 'WCAG 1.1.1 Non-text Content'
        });
      } else if (img.alt.length < 3) {
        this.issues.push({
          type: 'warning',
          category: 'images',
          element: `img[${index}]`,
          description: 'Alt text too short',
          recommendation: 'Provide more descriptive alt text',
          wcagReference: 'WCAG 1.1.1 Non-text Content'
        });
      }
    });
  }

  // Audit heading structure
  private auditHeadings(): void {
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    
    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));
      
      if (index === 0 && level !== 1) {
        this.issues.push({
          type: 'error',
          category: 'semantic-html',
          element: heading.tagName.toLowerCase(),
          description: 'Page should start with h1',
          recommendation: 'Use h1 for the main page heading',
          wcagReference: 'WCAG 1.3.1 Info and Relationships'
        });
      }
      
      if (level > previousLevel + 1) {
        this.issues.push({
          type: 'warning',
          category: 'semantic-html',
          element: heading.tagName.toLowerCase(),
          description: 'Heading level skipped',
          recommendation: 'Use sequential heading levels (h1, h2, h3...)',
          wcagReference: 'WCAG 1.3.1 Info and Relationships'
        });
      }
      
      previousLevel = level;
    });
  }

  // Audit form elements
  private auditForms(): void {
    const inputs = document.querySelectorAll('input, textarea, select');
    
    inputs.forEach((input, index) => {
      const hasLabel = input.id && document.querySelector(`label[for="${input.id}"]`);
      const hasAriaLabel = input.getAttribute('aria-label');
      const hasAriaLabelledBy = input.getAttribute('aria-labelledby');
      
      if (!hasLabel && !hasAriaLabel && !hasAriaLabelledBy) {
        this.issues.push({
          type: 'error',
          category: 'screen-reader',
          element: `${input.tagName.toLowerCase()}[${index}]`,
          description: 'Form element missing label',
          recommendation: 'Add label, aria-label, or aria-labelledby attribute',
          wcagReference: 'WCAG 1.3.1 Info and Relationships'
        });
      }
    });
  }

  // Audit links
  private auditLinks(): void {
    const links = document.querySelectorAll('a');
    
    links.forEach((link, index) => {
      const text = link.textContent?.trim();
      const ariaLabel = link.getAttribute('aria-label');
      
      if (!text && !ariaLabel) {
        this.issues.push({
          type: 'error',
          category: 'screen-reader',
          element: `a[${index}]`,
          description: 'Link missing accessible text',
          recommendation: 'Add descriptive text or aria-label',
          wcagReference: 'WCAG 2.4.4 Link Purpose'
        });
      }
      
      if (text && (text.toLowerCase() === 'click here' || text.toLowerCase() === 'read more')) {
        this.issues.push({
          type: 'warning',
          category: 'screen-reader',
          element: `a[${index}]`,
          description: 'Link text not descriptive',
          recommendation: 'Use descriptive link text that explains the destination',
          wcagReference: 'WCAG 2.4.4 Link Purpose'
        });
      }
    });
  }

  // Audit color contrast
  private auditColorContrast(): void {
    const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, a, button, label');
    
    textElements.forEach((element, index) => {
      const styles = window.getComputedStyle(element);
      const color = styles.color;
      const backgroundColor = styles.backgroundColor;
      
      // Skip if transparent or not visible
      if (backgroundColor === 'rgba(0, 0, 0, 0)' || backgroundColor === 'transparent') {
        return;
      }
      
      // Convert RGB to hex for contrast checking
      const colorHex = this.rgbToHex(color);
      const bgHex = this.rgbToHex(backgroundColor);
      
      if (colorHex && bgHex) {
        const fontSize = parseFloat(styles.fontSize);
        const isLargeText = fontSize >= 18 || (fontSize >= 14 && styles.fontWeight === 'bold');
        
        const contrast = this.colorChecker.meetsWCAG(colorHex, bgHex, 'AA', isLargeText);
        
        if (!contrast.passes) {
          this.issues.push({
            type: 'error',
            category: 'color-contrast',
            element: `${element.tagName.toLowerCase()}[${index}]`,
            description: `Insufficient color contrast (${contrast.ratio.toFixed(2)}:1, required: ${contrast.required}:1)`,
            recommendation: 'Increase color contrast between text and background',
            wcagReference: 'WCAG 1.4.3 Contrast (Minimum)'
          });
        }
      }
    });
  }

  // Audit keyboard navigation
  private auditKeyboardNavigation(): void {
    const focusableElements = document.querySelectorAll(
      'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
    );
    
    focusableElements.forEach((element, index) => {
      const tabIndex = element.getAttribute('tabindex');
      
      if (tabIndex && parseInt(tabIndex) > 0) {
        this.issues.push({
          type: 'warning',
          category: 'keyboard-navigation',
          element: `${element.tagName.toLowerCase()}[${index}]`,
          description: 'Positive tabindex found',
          recommendation: 'Avoid positive tabindex values, use 0 or -1',
          wcagReference: 'WCAG 2.4.3 Focus Order'
        });
      }
      
      // Check for focus indicators
      const styles = window.getComputedStyle(element, ':focus');
      if (styles.outline === 'none' && !styles.boxShadow && !styles.border) {
        this.issues.push({
          type: 'warning',
          category: 'focus-management',
          element: `${element.tagName.toLowerCase()}[${index}]`,
          description: 'No visible focus indicator',
          recommendation: 'Add visible focus styles for keyboard users',
          wcagReference: 'WCAG 2.4.7 Focus Visible'
        });
      }
    });
  }

  // Audit ARIA attributes
  private auditARIA(): void {
    const elementsWithAria = document.querySelectorAll('[aria-label], [aria-labelledby], [aria-describedby], [role]');
    
    elementsWithAria.forEach((element, index) => {
      const role = element.getAttribute('role');
      const ariaLabel = element.getAttribute('aria-label');
      const ariaLabelledBy = element.getAttribute('aria-labelledby');
      const ariaDescribedBy = element.getAttribute('aria-describedby');
      
      // Check if referenced elements exist
      if (ariaLabelledBy) {
        const referencedElement = document.getElementById(ariaLabelledBy);
        if (!referencedElement) {
          this.issues.push({
            type: 'error',
            category: 'screen-reader',
            element: `${element.tagName.toLowerCase()}[${index}]`,
            description: 'aria-labelledby references non-existent element',
            recommendation: 'Ensure referenced element exists',
            wcagReference: 'WCAG 1.3.1 Info and Relationships'
          });
        }
      }
      
      if (ariaDescribedBy) {
        const referencedElement = document.getElementById(ariaDescribedBy);
        if (!referencedElement) {
          this.issues.push({
            type: 'error',
            category: 'screen-reader',
            element: `${element.tagName.toLowerCase()}[${index}]`,
            description: 'aria-describedby references non-existent element',
            recommendation: 'Ensure referenced element exists',
            wcagReference: 'WCAG 1.3.1 Info and Relationships'
          });
        }
      }
    });
  }

  // Helper: Convert RGB to Hex
  private rgbToHex(rgb: string): string | null {
    const match = rgb.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (!match) return null;
    
    const [, r, g, b] = match;
    return `#${parseInt(r).toString(16).padStart(2, '0')}${parseInt(g).toString(16).padStart(2, '0')}${parseInt(b).toString(16).padStart(2, '0')}`;
  }

  // Generate accessibility report
  generateReport(): {
    summary: {
      total: number;
      errors: number;
      warnings: number;
      info: number;
    };
    issues: AccessibilityIssue[];
    score: number;
  } {
    const errors = this.issues.filter(issue => issue.type === 'error').length;
    const warnings = this.issues.filter(issue => issue.type === 'warning').length;
    const info = this.issues.filter(issue => issue.type === 'info').length;
    
    // Calculate accessibility score (0-100)
    const totalElements = document.querySelectorAll('*').length;
    const score = Math.max(0, 100 - (errors * 10 + warnings * 5 + info * 1));
    
    return {
      summary: {
        total: this.issues.length,
        errors,
        warnings,
        info
      },
      issues: this.issues,
      score: Math.round(score)
    };
  }
}

// Accessibility Enhancement Utilities
export const accessibilityEnhancements = {
  // Add skip links
  addSkipLinks: () => {
    if (document.querySelector('.skip-link')) return;
    
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'skip-link sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white p-2 z-50';
    
    document.body.insertBefore(skipLink, document.body.firstChild);
  },

  // Enhance focus management
  enhanceFocusManagement: () => {
    // Add focus trap for modals
    const modals = document.querySelectorAll('[role="dialog"]');
    modals.forEach(modal => {
      const focusableElements = modal.querySelectorAll(
        'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
      );
      
      if (focusableElements.length > 0) {
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
        
        modal.addEventListener('keydown', (e: Event) => {
          const keyboardEvent = e as KeyboardEvent;
          if (keyboardEvent.key === 'Tab') {
            if (keyboardEvent.shiftKey && document.activeElement === firstElement) {
              keyboardEvent.preventDefault();
              lastElement.focus();
            } else if (!keyboardEvent.shiftKey && document.activeElement === lastElement) {
              keyboardEvent.preventDefault();
              firstElement.focus();
            }
          }
        });
      }
    });
  },

  // Add ARIA live regions
  addLiveRegions: () => {
    if (document.querySelector('#aria-live-polite')) return;
    
    const politeRegion = document.createElement('div');
    politeRegion.id = 'aria-live-polite';
    politeRegion.setAttribute('aria-live', 'polite');
    politeRegion.className = 'sr-only';
    
    const assertiveRegion = document.createElement('div');
    assertiveRegion.id = 'aria-live-assertive';
    assertiveRegion.setAttribute('aria-live', 'assertive');
    assertiveRegion.className = 'sr-only';
    
    document.body.appendChild(politeRegion);
    document.body.appendChild(assertiveRegion);
  }
};

// Initialize accessibility auditor
export const accessibilityAuditor = new AccessibilityAuditor();
export const colorContrastChecker = new ColorContrastChecker();
