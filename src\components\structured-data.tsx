// Structured Data Component for SEO
// Provides JSON-LD structured data for better search engine understanding

import { config } from '@/lib/config'

interface StructuredDataProps {
  type?: 'website' | 'organization' | 'educational' | 'article' | 'forum'
  title?: string
  description?: string
  url?: string
  image?: string
  datePublished?: string
  dateModified?: string
  author?: string
}

export function StructuredData({
  type = 'website',
  title,
  description,
  url,
  image,
  datePublished,
  dateModified,
  author
}: StructuredDataProps) {
  const baseUrl = config.site.url
  
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
      "@type": type === 'website' ? 'WebSite' : 
               type === 'organization' ? 'Organization' :
               type === 'educational' ? 'EducationalOrganization' :
               type === 'article' ? 'Article' :
               type === 'forum' ? 'DiscussionForumPosting' : 'WebSite',
      "name": title || "Light Upon Light",
      "description": description || "Educational platform for spiritual wisdom, natural healing, and community learning",
      "url": url || baseUrl,
      "image": image || `${baseUrl}/og-image.svg`,
    }

    if (type === 'website') {
      return {
        ...baseData,
        "potentialAction": {
          "@type": "SearchAction",
          "target": {
            "@type": "EntryPoint",
            "urlTemplate": `${baseUrl}/search?q={search_term_string}`
          },
          "query-input": "required name=search_term_string"
        },
        "sameAs": [
          // Add social media links when available
        ]
      }
    }

    if (type === 'organization' || type === 'educational') {
      return {
        ...baseData,
        "foundingDate": "2024",
        "founder": {
          "@type": "Person",
          "name": "Mohammad Abbas"
        },
        "contactPoint": {
          "@type": "ContactPoint",
          "email": "<EMAIL>",
          "contactType": "customer service"
        },
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "Global"
        },
        "areaServed": "Worldwide",
        "knowsAbout": [
          "Islamic Education",
          "Spiritual Growth",
          "Natural Healing",
          "Quranic Studies",
          "Community Learning",
          "Meditation",
          "Wisdom Traditions"
        ]
      }
    }

    if (type === 'article') {
      return {
        ...baseData,
        "@type": "Article",
        "headline": title,
        "datePublished": datePublished,
        "dateModified": dateModified || datePublished,
        "author": {
          "@type": "Person",
          "name": author || "Light Upon Light"
        },
        "publisher": {
          "@type": "Organization",
          "name": "Light Upon Light",
          "logo": {
            "@type": "ImageObject",
            "url": `${baseUrl}/logo.svg`
          }
        },
        "mainEntityOfPage": {
          "@type": "WebPage",
          "@id": url || baseUrl
        }
      }
    }

    if (type === 'forum') {
      return {
        ...baseData,
        "@type": "DiscussionForumPosting",
        "headline": title,
        "dateCreated": datePublished,
        "author": {
          "@type": "Person",
          "name": author || "Community Member"
        },
        "isPartOf": {
          "@type": "WebSite",
          "name": "Light Upon Light Forum",
          "url": `${baseUrl}/forum`
        }
      }
    }

    return baseData
  }

  const structuredData = getStructuredData()

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2)
      }}
    />
  )
}

// Breadcrumb structured data
export function BreadcrumbStructuredData({ items }: { items: Array<{ name: string; url: string }> }) {
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(breadcrumbData, null, 2)
      }}
    />
  )
}

// FAQ structured data
export function FAQStructuredData({ faqs }: { faqs: Array<{ question: string; answer: string }> }) {
  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(faqData, null, 2)
      }}
    />
  )
}
