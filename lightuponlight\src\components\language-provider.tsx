'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export type Language = 'en' | 'ar' | 'fr' | 'es' | 'ur' | 'sv'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.wisdom': 'Wisdom',
    'nav.cupping': 'Cupping Therapy',
    'nav.plants': 'Plant Medicine',
    'nav.heart': 'Heart Development',
    'nav.quran': 'Quran Verses',
    'nav.names': '99 Names',
    'nav.heavens': 'Heavens & Earth',
    'nav.community': 'Community',
    'nav.forum': 'Forum',
    'nav.live': 'Live Stream',
    'nav.account': 'Account',
    'nav.signin': 'Sign In',
    'nav.signout': 'Sign Out',
    
    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.view': 'View',
    'common.download': 'Download',
    'common.upload': 'Upload',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.sort': 'Sort',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.close': 'Close',
    'common.open': 'Open',
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.showless': 'Show Less',
    'common.viewall': 'View All',
    
    // Account
    'account.title': 'Account Settings',
    'account.profile': 'Profile',
    'account.privacy': 'Privacy & Data',
    'account.language': 'Language',
    'account.settings': 'Account Settings',
    'account.export': 'Export Data',
    'account.delete': 'Delete Account',
    'account.fullname': 'Full Name',
    'account.email': 'Email Address',
    'account.bio': 'Bio',
    
    // Forum
    'forum.title': 'Community Forum',
    'forum.newpost': 'New Post',
    'forum.reply': 'Reply',
    'forum.like': 'Like',
    'forum.dislike': 'Dislike',
    'forum.category': 'Category',
    'forum.title_field': 'Title',
    'forum.content': 'Content',
    'forum.post': 'Post',
    'forum.viewall': 'View All Posts',
    'forum.totalposts': 'Total Posts',
    'forum.totalreplies': 'Total Replies',
    
    // Live Stream
    'live.title': 'Live Stream',
    'live.viewers': 'Viewers',
    'live.chat': 'Chat',
    'live.send': 'Send',
    'live.startstream': 'Start Stream',
    'live.stopstream': 'Stop Stream',
    'live.camera': 'Camera',
    'live.microphone': 'Microphone',
    
    // Community
    'community.title': 'Community Learning Hub',
    'community.overview': 'Overview',
    'community.learningpaths': 'Learning Paths',
    'community.events': 'Events',
    'community.achievements': 'Achievements',
    'community.members': 'Active Members',
    'community.posts': 'Total Posts',
    'community.views': 'Total Views',
    
    // Cupping
    'cupping.title': 'Cupping Therapy',
    'cupping.subtitle': 'Natural Healing & Wellness Wisdom',
    'cupping.benefits': 'Benefits',
    'cupping.painrelief': 'Pain Relief',
    'cupping.circulation': 'Better Blood Flow',
    'cupping.detox': 'Natural Cleaning',
    'cupping.stress': 'Feeling Calm & Peaceful',
    'cupping.immune': 'Body Support',
    'cupping.sleep': 'Sleep Enhancement',
    'cupping.facts.title': 'Amazing Facts About Natural Healing & Inner Development',
    'cupping.facts.divine': 'Divine Design of Healing',
    'cupping.facts.confirmation': 'Heart Confirmation & Knowing',
    'cupping.facts.intellectual': 'Intellectual Living Through Healing',
    'cupping.facts.alignment': 'Perfect Balance & Alignment',
    'cupping.facts.growth': 'Path to Inner Growth',
    'cupping.facts.enlightenment': 'Gateway to Enlightenment',

    // Heart Development
    'heart.title': 'Heart-Mind Development',
    'heart.subtitle': 'Logic, Reflection & Intellectual Development',
    'heart.contentment': 'Contentment',
    'heart.consciousness': 'Enhanced Consciousness',
    'heart.soundheart': 'Sound Heart Development',
    'heart.resilience': 'Stress Resilience',
    'heart.purpose': 'Life Purpose',
    'heart.wisdom': 'Wisdom Integration',
    'heart.facts.title': 'Amazing Facts About Human Development Potential',
    'heart.facts.learning': 'Unlimited Learning Capacity',
    'heart.facts.alignment': 'Heart-Mind Alignment',
    'heart.facts.intelligence': 'Natural Intelligence Growth',
    'heart.facts.balance': 'Perfect Balance Living',
    'heart.facts.renewal': 'Continuous Renewal',
    'heart.facts.potential': 'Infinite Potential',

    // Patience
    'patience.title': 'Patience & Wisdom',
    'patience.subtitle': 'Wisdom & Intelligence Development Through Patience',
    'patience.facts.title': 'Amazing Facts About Patience & Spiritual Development',
    'patience.facts.timing': 'Divine Timing & Wisdom',
    'patience.facts.confirmation': 'Heart Confirmation Through Patience',
    'patience.facts.intellectual': 'Intellectual Living Through Patience',
    'patience.facts.alignment': 'Perfect Alignment & Balance',
    'patience.facts.growth': 'Path to Inner Growth',
    'patience.facts.enlightenment': 'Gateway to Enlightenment',

    // Logic
    'logic.title': 'Logic & Intelligence',
    'logic.subtitle': 'Natural Intelligence & Pure Understanding',
    'logic.facts.title': 'Amazing Facts About Divine Logic & Enlightenment',
    'logic.facts.divine': 'Divine Logic in Creation',
    'logic.facts.confirmation': 'Heart Confirmation of Truth',
    'logic.facts.intellectual': 'Intellectual Living Through Divine Wisdom',
    'logic.facts.alignment': 'Perfect Alignment with Divine Will',
    'logic.facts.growth': 'Path to Inner Growth & Intelligence',
    'logic.facts.enlightenment': 'Gateway to Enlightenment',

    // Cookie Consent
    'cookies.title': 'Cookie Preferences',
    'cookies.description': 'We use cookies to enhance your experience, provide personalized content, and analyze our traffic.',
    'cookies.necessary': 'Necessary Cookies',
    'cookies.analytics': 'Analytics Cookies',
    'cookies.functional': 'Functional Cookies',
    'cookies.marketing': 'Marketing Cookies',
    'cookies.acceptall': 'Accept All',
    'cookies.customize': 'Customize',
    'cookies.acceptselected': 'Accept Selected',
    'cookies.rejectall': 'Reject All',
    'cookies.privacy': 'Privacy Policy',
    'cookies.policy': 'Cookie Policy'
  },
  ar: {
    // Navigation
    'nav.home': 'الرئيسية',
    'nav.wisdom': 'الحكمة',
    'nav.cupping': 'العلاج بالحجامة',
    'nav.plants': 'طب الأعشاب',
    'nav.heart': 'تطوير القلب',
    'nav.quran': 'آيات القرآن',
    'nav.names': 'الأسماء الحسنى',
    'nav.heavens': 'السماوات والأرض',
    'nav.community': 'المجتمع',
    'nav.forum': 'المنتدى',
    'nav.live': 'البث المباشر',
    'nav.account': 'الحساب',
    'nav.signin': 'تسجيل الدخول',
    'nav.signout': 'تسجيل الخروج',
    
    // Common
    'common.loading': 'جاري التحميل...',
    'common.error': 'خطأ',
    'common.success': 'نجح',
    'common.save': 'حفظ',
    'common.cancel': 'إلغاء',
    'common.delete': 'حذف',
    'common.edit': 'تعديل',
    'common.view': 'عرض',
    'common.download': 'تحميل',
    'common.upload': 'رفع',
    'common.search': 'بحث',
    'common.filter': 'تصفية',
    'common.sort': 'ترتيب',
    'common.next': 'التالي',
    'common.previous': 'السابق',
    'common.close': 'إغلاق',
    'common.open': 'فتح',
    'common.yes': 'نعم',
    'common.no': 'لا',
    
    // Account
    'account.title': 'إعدادات الحساب',
    'account.profile': 'الملف الشخصي',
    'account.privacy': 'الخصوصية والبيانات',
    'account.language': 'اللغة',
    'account.settings': 'إعدادات الحساب',
    'account.export': 'تصدير البيانات',
    'account.delete': 'حذف الحساب',
    'account.fullname': 'الاسم الكامل',
    'account.email': 'عنوان البريد الإلكتروني',
    'account.bio': 'السيرة الذاتية',

    // Cupping
    'cupping.title': 'العلاج بالحجامة',
    'cupping.subtitle': 'الحكمة الطبيعية والعافية',
    'cupping.benefits': 'الفوائد',
    'cupping.painrelief': 'تخفيف الألم',
    'cupping.circulation': 'تحسين تدفق الدم',
    'cupping.detox': 'التنظيف الطبيعي',
    'cupping.stress': 'الشعور بالهدوء والسلام',
    'cupping.immune': 'دعم الجسم',
    'cupping.sleep': 'تحسين النوم',
    'cupping.facts.title': 'حقائق مذهلة عن الشفاء الطبيعي والنمو الداخلي',
    'cupping.facts.divine': 'التصميم الإلهي للشفاء',
    'cupping.facts.confirmation': 'تأكيد القلب والمعرفة',
    'cupping.facts.intellectual': 'العيش الفكري من خلال الشفاء',
    'cupping.facts.alignment': 'التوازن والانسجام المثالي',
    'cupping.facts.growth': 'طريق النمو الداخلي',
    'cupping.facts.enlightenment': 'بوابة التنوير',

    // Heart Development
    'heart.title': 'تطوير القلب والعقل',
    'heart.subtitle': 'المنطق والتأمل والتطوير الفكري',
    'heart.contentment': 'القناعة',
    'heart.consciousness': 'الوعي المتقدم',
    'heart.soundheart': 'تطوير القلب السليم',
    'heart.resilience': 'مقاومة الضغط',
    'heart.purpose': 'هدف الحياة',
    'heart.wisdom': 'تكامل الحكمة',
    'heart.facts.title': 'حقائق مذهلة عن إمكانات التطوير البشري',
    'heart.facts.learning': 'قدرة التعلم اللامحدودة',
    'heart.facts.alignment': 'انسجام القلب والعقل',
    'heart.facts.intelligence': 'نمو الذكاء الطبيعي',
    'heart.facts.balance': 'العيش المتوازن المثالي',
    'heart.facts.renewal': 'التجديد المستمر',
    'heart.facts.potential': 'الإمكانات اللانهائية',

    // Patience
    'patience.title': 'الصبر والحكمة',
    'patience.subtitle': 'تطوير الحكمة والذكاء من خلال الصبر',
    'patience.facts.title': 'حقائق مذهلة عن الصبر والتطوير الروحي',
    'patience.facts.timing': 'التوقيت الإلهي والحكمة',
    'patience.facts.confirmation': 'تأكيد القلب من خلال الصبر',
    'patience.facts.intellectual': 'العيش الفكري من خلال الصبر',
    'patience.facts.alignment': 'التوازن والانسجام المثالي',
    'patience.facts.growth': 'طريق النمو الداخلي',
    'patience.facts.enlightenment': 'بوابة التنوير',

    // Logic
    'logic.title': 'المنطق والذكاء',
    'logic.subtitle': 'الذكاء الطبيعي والفهم النقي',
    'logic.facts.title': 'حقائق مذهلة عن المنطق الإلهي والتنوير',
    'logic.facts.divine': 'المنطق الإلهي في الخلق',
    'logic.facts.confirmation': 'تأكيد القلب للحقيقة',
    'logic.facts.intellectual': 'العيش الفكري من خلال الحكمة الإلهية',
    'logic.facts.alignment': 'التوافق المثالي مع الإرادة الإلهية',
    'logic.facts.growth': 'طريق النمو الداخلي والذكاء',
    'logic.facts.enlightenment': 'بوابة التنوير',

    // Cookie Consent
    'cookies.title': 'تفضيلات ملفات تعريف الارتباط',
    'cookies.description': 'نستخدم ملفات تعريف الارتباط لتحسين تجربتك وتقديم محتوى مخصص وتحليل حركة المرور.',
    'cookies.necessary': 'ملفات تعريف الارتباط الضرورية',
    'cookies.analytics': 'ملفات تعريف الارتباط التحليلية',
    'cookies.functional': 'ملفات تعريف الارتباط الوظيفية',
    'cookies.marketing': 'ملفات تعريف الارتباط التسويقية',
    'cookies.acceptall': 'قبول الكل',
    'cookies.customize': 'تخصيص',
    'cookies.acceptselected': 'قبول المحدد',
    'cookies.rejectall': 'رفض الكل',
    'cookies.privacy': 'سياسة الخصوصية',
    'cookies.policy': 'سياسة ملفات تعريف الارتباط'
  },
  fr: {
    // Navigation
    'nav.home': 'Accueil',
    'nav.wisdom': 'Sagesse',
    'nav.cupping': 'Thérapie par Ventouses',
    'nav.plants': 'Médecine des Plantes',
    'nav.heart': 'Développement du Cœur',
    'nav.quran': 'Versets du Coran',
    'nav.names': '99 Noms',
    'nav.heavens': 'Cieux et Terre',
    'nav.community': 'Communauté',
    'nav.forum': 'Forum',
    'nav.live': 'Diffusion en Direct',
    'nav.account': 'Compte',
    'nav.signin': 'Se Connecter',
    'nav.signout': 'Se Déconnecter'
  },
  es: {
    // Navigation
    'nav.home': 'Inicio',
    'nav.wisdom': 'Sabiduría',
    'nav.cupping': 'Terapia de Ventosas',
    'nav.plants': 'Medicina de Plantas',
    'nav.heart': 'Desarrollo del Corazón',
    'nav.quran': 'Versos del Corán',
    'nav.names': '99 Nombres',
    'nav.heavens': 'Cielos y Tierra',
    'nav.community': 'Comunidad',
    'nav.forum': 'Foro',
    'nav.live': 'Transmisión en Vivo',
    'nav.account': 'Cuenta',
    'nav.signin': 'Iniciar Sesión',
    'nav.signout': 'Cerrar Sesión'
  },
  ur: {
    // Navigation
    'nav.home': 'گھر',
    'nav.wisdom': 'حکمت',
    'nav.cupping': 'حجامہ تھراپی',
    'nav.plants': 'نباتی ادویات',
    'nav.heart': 'دل کی ترقی',
    'nav.quran': 'قرآنی آیات',
    'nav.names': '99 نام',
    'nav.heavens': 'آسمان اور زمین',
    'nav.community': 'کمیونٹی',
    'nav.forum': 'فورم',
    'nav.live': 'لائیو سٹریم',
    'nav.account': 'اکاؤنٹ',
    'nav.signin': 'سائن ان',
    'nav.signout': 'سائن آؤٹ'
  },
  sv: {
    // Navigation
    'nav.home': 'Hem',
    'nav.wisdom': 'Visdom',
    'nav.cupping': 'Koppningsterapi',
    'nav.plants': 'Växtmedicin',
    'nav.heart': 'Hjärtutveckling',
    'nav.quran': 'Koranverser',
    'nav.names': '99 Namn',
    'nav.heavens': 'Himmel och Jord',
    'nav.community': 'Gemenskap',
    'nav.forum': 'Forum',
    'nav.live': 'Livestream',
    'nav.account': 'Konto',
    'nav.signin': 'Logga In',
    'nav.signout': 'Logga Ut',

    // Common
    'common.loading': 'Laddar...',
    'common.error': 'Fel',
    'common.success': 'Framgång',
    'common.save': 'Spara',
    'common.cancel': 'Avbryt',
    'common.delete': 'Ta bort',
    'common.edit': 'Redigera',
    'common.view': 'Visa',
    'common.download': 'Ladda ner',
    'common.upload': 'Ladda upp',
    'common.search': 'Sök',
    'common.filter': 'Filtrera',
    'common.sort': 'Sortera',
    'common.next': 'Nästa',
    'common.previous': 'Föregående',
    'common.close': 'Stäng',
    'common.open': 'Öppna',
    'common.yes': 'Ja',
    'common.no': 'Nej',

    // Cupping
    'cupping.title': 'Koppningsterapi',
    'cupping.subtitle': 'Naturlig Läkning & Välbefinnande Visdom',
    'cupping.benefits': 'Fördelar',
    'cupping.painrelief': 'Smärtlindring',
    'cupping.circulation': 'Bättre Blodflöde',
    'cupping.detox': 'Naturlig Rening',
    'cupping.stress': 'Känna Lugn & Fred',
    'cupping.immune': 'Kroppsstöd',
    'cupping.sleep': 'Sömnförbättring',
    'cupping.facts.title': 'Fantastiska Fakta om Naturlig Läkning & Inre Utveckling',
    'cupping.facts.divine': 'Gudomlig Design av Läkning',
    'cupping.facts.confirmation': 'Hjärtats Bekräftelse & Vetande',
    'cupping.facts.intellectual': 'Intellektuellt Levande genom Läkning',
    'cupping.facts.alignment': 'Perfekt Balans & Anpassning',
    'cupping.facts.growth': 'Väg till Inre Tillväxt',
    'cupping.facts.enlightenment': 'Port till Upplysning',

    // Heart Development
    'heart.title': 'Hjärt-Sinne Utveckling',
    'heart.subtitle': 'Logik, Reflektion & Intellektuell Utveckling',
    'heart.contentment': 'Tillfredsställelse',
    'heart.consciousness': 'Förhöjt Medvetande',
    'heart.soundheart': 'Sunt Hjärta Utveckling',
    'heart.resilience': 'Stressresistens',
    'heart.purpose': 'Livssyfte',
    'heart.wisdom': 'Visdomsintegration',
    'heart.facts.title': 'Fantastiska Fakta om Mänsklig Utvecklingspotential',
    'heart.facts.learning': 'Obegränsad Inlärningskapacitet',
    'heart.facts.alignment': 'Hjärt-Sinne Anpassning',
    'heart.facts.intelligence': 'Naturlig Intelligensutveckling',
    'heart.facts.balance': 'Perfekt Balanserat Levande',
    'heart.facts.renewal': 'Kontinuerlig Förnyelse',
    'heart.facts.potential': 'Oändlig Potential',

    // Patience
    'patience.title': 'Tålamod & Visdom',
    'patience.subtitle': 'Visdom & Intelligensutveckling genom Tålamod',
    'patience.facts.title': 'Fantastiska Fakta om Tålamod & Andlig Utveckling',
    'patience.facts.timing': 'Gudomlig Timing & Visdom',
    'patience.facts.confirmation': 'Hjärtats Bekräftelse genom Tålamod',
    'patience.facts.intellectual': 'Intellektuellt Levande genom Tålamod',
    'patience.facts.alignment': 'Perfekt Anpassning & Balans',
    'patience.facts.growth': 'Väg till Inre Tillväxt',
    'patience.facts.enlightenment': 'Port till Upplysning',

    // Logic
    'logic.title': 'Logik & Intelligens',
    'logic.subtitle': 'Naturlig Intelligens & Ren Förståelse',
    'logic.facts.title': 'Fantastiska Fakta om Gudomlig Logik & Upplysning',
    'logic.facts.divine': 'Gudomlig Logik i Skapelsen',
    'logic.facts.confirmation': 'Hjärtats Bekräftelse av Sanning',
    'logic.facts.intellectual': 'Intellektuellt Levande genom Gudomlig Visdom',
    'logic.facts.alignment': 'Perfekt Anpassning med Gudomlig Vilja',
    'logic.facts.growth': 'Väg till Inre Tillväxt & Intelligens',
    'logic.facts.enlightenment': 'Port till Upplysning',

    // Cookie Consent
    'cookies.title': 'Cookie-inställningar',
    'cookies.description': 'Vi använder cookies för att förbättra din upplevelse, tillhandahålla personligt innehåll och analysera vår trafik.',
    'cookies.necessary': 'Nödvändiga Cookies',
    'cookies.analytics': 'Analytiska Cookies',
    'cookies.functional': 'Funktionella Cookies',
    'cookies.marketing': 'Marknadsföring Cookies',
    'cookies.acceptall': 'Acceptera Alla',
    'cookies.customize': 'Anpassa',
    'cookies.acceptselected': 'Acceptera Valda',
    'cookies.rejectall': 'Avvisa Alla',
    'cookies.privacy': 'Integritetspolicy',
    'cookies.policy': 'Cookie-policy'
  }
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState<Language>('en')
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    // Load saved language from localStorage
    const savedLanguage = localStorage.getItem('language') as Language
    if (savedLanguage && Object.keys(translations).includes(savedLanguage)) {
      setLanguage(savedLanguage)
    }
  }, [])

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang)
    if (mounted) {
      localStorage.setItem('language', lang)
    }
  }

  const t = (key: string): string => {
    if (!mounted) return key // Prevent hydration issues
    
    const translation = translations[language]?.[key as keyof typeof translations[typeof language]]
    return translation || translations.en[key as keyof typeof translations.en] || key
  }

  if (!mounted) {
    // Return a minimal context during SSR to prevent hydration issues
    const defaultContext: LanguageContextType = {
      language: 'en',
      setLanguage: () => {},
      t: (key: string) => key
    }
    return (
      <LanguageContext.Provider value={defaultContext}>
        {children}
      </LanguageContext.Provider>
    )
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}
