'use client'

import { SecurityService } from './security'

// Email service for sending contact form messages
export class EmailService {
  private static readonly ADMIN_EMAIL = '<EMAIL>'
  private static readonly DPO_EMAIL = '<EMAIL>'

  // Send contact form message
  static async sendContactMessage(data: {
    name: string
    email: string
    subject: string
    message: string
    inquiryType?: string
    source?: 'contact-page' | 'account-page'
  }) {
    try {
      // Validate and sanitize input data
      const validation = SecurityService.validateContactForm(data)
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
      }

      // Rate limiting check
      const rateLimitKey = `contact_${data.email}`
      if (!SecurityService.checkRateLimit(rateLimitKey, 5, 300000)) { // 5 messages per 5 minutes
        throw new Error('Too many messages sent. Please wait before sending another message.')
      }

      // Sanitize data
      const sanitizedData = {
        name: SecurityService.sanitizeInput(data.name),
        email: SecurityService.sanitizeInput(data.email),
        subject: SecurityService.sanitizeInput(data.subject),
        message: SecurityService.sanitizeInput(data.message),
        inquiryType: data.inquiryType ? SecurityService.sanitizeInput(data.inquiryType) : undefined,
        source: data.source
      }

      const emailBody = this.formatContactEmail(sanitizedData)
      const mailtoLink = `mailto:${this.ADMIN_EMAIL}?subject=${encodeURIComponent(sanitizedData.subject)}&body=${encodeURIComponent(emailBody)}`

      // Log for development (without sensitive data)
      if (process.env.NODE_ENV === 'development') {
        console.log('Contact message sent from:', sanitizedData.source)
      }

      // Try to open mailto link
      if (typeof window !== 'undefined') {
        window.open(mailtoLink, '_blank')
      }

      // Show notice about manual sending
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          alert(`📧 Notice: If the email client didn't open automatically, please send your message manually to: ${this.ADMIN_EMAIL}`)
        }, 2000)
      }

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      return { success: true, message: 'Message sent successfully' }
    } catch (error) {
      console.error('Error sending contact message:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to send message')
    }
  }

  // Send GDPR data request
  static async sendGDPRRequest(data: {
    name: string
    email: string
    requestType: 'access' | 'deletion' | 'portability' | 'rectification' | 'restriction' | 'objection'
    details?: string
  }) {
    try {
      // Validate input data
      if (!data.name || !data.email || !data.requestType) {
        throw new Error('All required fields must be provided')
      }

      if (!SecurityService.isValidEmail(data.email)) {
        throw new Error('Invalid email format')
      }

      // Rate limiting for GDPR requests (stricter)
      const rateLimitKey = `gdpr_${data.email}`
      if (!SecurityService.checkRateLimit(rateLimitKey, 2, 3600000)) { // 2 requests per hour
        throw new Error('Too many GDPR requests. Please wait before submitting another request.')
      }

      // Sanitize data
      const sanitizedData = {
        name: SecurityService.sanitizeInput(data.name),
        email: SecurityService.sanitizeInput(data.email),
        requestType: data.requestType, // This is from a controlled enum
        details: data.details ? SecurityService.sanitizeInput(data.details) : undefined
      }

      const subject = `GDPR Data Request - ${sanitizedData.requestType.charAt(0).toUpperCase() + sanitizedData.requestType.slice(1)}`
      const emailBody = this.formatGDPREmail(sanitizedData)

      const mailtoLink = `mailto:${this.DPO_EMAIL}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(emailBody)}`

      // Log for compliance (without sensitive data)
      if (process.env.NODE_ENV === 'development') {
        console.log('GDPR request submitted:', { requestType: sanitizedData.requestType, timestamp: new Date().toISOString() })
      }

      // Try to open mailto link
      if (typeof window !== 'undefined') {
        window.open(mailtoLink, '_blank')
      }

      // Show notice about manual sending
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          alert(`📧 Notice: If the email client didn't open automatically, please send your GDPR request manually to: ${this.DPO_EMAIL}`)
        }, 2000)
      }

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      return { success: true, message: 'GDPR request sent successfully' }
    } catch (error) {
      console.error('Error sending GDPR request:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to send GDPR request')
    }
  }

  // Format contact email
  private static formatContactEmail(data: {
    name: string
    email: string
    subject: string
    message: string
    inquiryType?: string
    source?: string
  }): string {
    return `
Light Upon Light - Contact Form Submission

From: ${data.name}
Email: ${data.email}
Source: ${data.source || 'contact-page'}
${data.inquiryType ? `Inquiry Type: ${data.inquiryType}` : ''}
Subject: ${data.subject}

Message:
${data.message}

---
Sent from Light Upon Light Contact Form
Timestamp: ${new Date().toISOString()}
    `.trim()
  }

  // Format GDPR email
  private static formatGDPREmail(data: {
    name: string
    email: string
    requestType: string
    details?: string
  }): string {
    return `
Light Upon Light - GDPR Data Protection Request

Data Subject Information:
Name: ${data.name}
Email: ${data.email}
Request Type: ${data.requestType.charAt(0).toUpperCase() + data.requestType.slice(1)}

Request Details:
${data.details || 'No additional details provided.'}

Legal Basis:
This request is made under the General Data Protection Regulation (GDPR) Article ${this.getGDPRArticle(data.requestType)}.

Please process this request within the statutory 30-day period as required by GDPR.

---
Data Protection Officer: Mohammad Abbas
Officer Email: <EMAIL>
Timestamp: ${new Date().toISOString()}
    `.trim()
  }

  // Get GDPR article for request type
  private static getGDPRArticle(requestType: string): string {
    const articles = {
      access: '15 (Right of Access)',
      deletion: '17 (Right to Erasure)',
      portability: '20 (Right to Data Portability)',
      rectification: '16 (Right to Rectification)',
      restriction: '18 (Right to Restriction of Processing)',
      objection: '21 (Right to Object)'
    }
    return articles[requestType as keyof typeof articles] || '15'
  }

  // Validate email format
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Get admin contact info
  static getContactInfo() {
    return {
      adminEmail: this.ADMIN_EMAIL,
      dpoEmail: this.DPO_EMAIL,
      responseTime: '24-48 hours',
      gdprResponseTime: '30 days maximum'
    }
  }
}

// Types for email service
export interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
  inquiryType?: string
  source?: 'contact-page' | 'account-page'
}

export interface GDPRRequestData {
  name: string
  email: string
  requestType: 'access' | 'deletion' | 'portability' | 'rectification' | 'restriction' | 'objection'
  details?: string
}

export interface EmailResponse {
  success: boolean
  message: string
}
