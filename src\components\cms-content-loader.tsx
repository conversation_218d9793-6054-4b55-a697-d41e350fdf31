"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import { CMSService, CMSContent } from "@/lib/cms-service";
import { useLanguage } from "./language-provider";
import { Card, CardContent } from "./ui/card";
import { Button } from "./ui/button";
import {
  Loader2,
  RefreshCw,
  AlertCircle,
  Eye,
  Calendar,
  User,
  Tag,
  Folder,
  Clock,
  TrendingUp,
  Star,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

// ===== INTERFACES =====

interface ContentLoaderProps {
  contentType?: string;
  category?: string;
  tag?: string;
  featured?: boolean;
  limit?: number;
  showPagination?: boolean;
  showFilters?: boolean;
  showSearch?: boolean;
  layout?: 'grid' | 'list' | 'card';
  className?: string;
  onContentClick?: (content: CMSContent) => void;
}

interface LoadingState {
  loading: boolean;
  error: string | null;
  retrying: boolean;
}

interface FilterState {
  search: string;
  category: string;
  tag: string;
  status: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// ===== CONTENT LOADER COMPONENT =====

export function CMSContentLoader({
  contentType,
  category,
  tag,
  featured,
  limit = 12,
  showPagination = true,
  showFilters = false,
  showSearch = false,
  layout = 'grid',
  className = '',
  onContentClick,
}: ContentLoaderProps) {
  const { language } = useLanguage();

  // State management
  const [content, setContent] = useState<CMSContent[]>([]);
  const [totalContent, setTotalContent] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingState, setLoadingState] = useState<LoadingState>({
    loading: true,
    error: null,
    retrying: false,
  });

  // Filters
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    category: category || '',
    tag: tag || '',
    status: 'published',
    sortBy: 'published_at',
    sortOrder: 'desc',
  });

  // Categories and tags for filters
  const [categories, setCategories] = useState<any[]>([]);
  const [tags, setTags] = useState<any[]>([]);

  // Debounced search
  const [searchDebounce, setSearchDebounce] = useState<NodeJS.Timeout | null>(null);

  // Load content with caching and optimization
  const loadContent = useCallback(async (page: number = 1, retrying: boolean = false) => {
    setLoadingState(prev => ({ ...prev, loading: true, retrying, error: null }));

    try {
      const options = {
        content_type: contentType,
        language,
        featured,
        limit,
        offset: (page - 1) * limit,
        search: filters.search || undefined,
        category: filters.category || undefined,
        tag: filters.tag || undefined,
        sort_by: filters.sortBy,
        sort_order: filters.sortOrder,
      };

      const result = await CMSService.getContentList(options);
      
      setContent(result.content);
      setTotalContent(result.total);
      setCurrentPage(page);
      setLoadingState({ loading: false, error: null, retrying: false });
    } catch (error) {
      console.error('Error loading content:', error);
      setLoadingState({
        loading: false,
        error: 'Failed to load content. Please try again.',
        retrying: false,
      });
    }
  }, [contentType, language, featured, limit, filters]);

  // Load filter options
  const loadFilterOptions = useCallback(async () => {
    if (!showFilters) return;

    try {
      const [categoriesData, tagsData] = await Promise.all([
        CMSService.getCategories(language),
        CMSService.getPopularTags(language, 20),
      ]);

      setCategories(categoriesData);
      setTags(tagsData);
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  }, [language, showFilters]);

  // Initial load
  useEffect(() => {
    loadContent(1);
    loadFilterOptions();
  }, [loadContent, loadFilterOptions]);

  // Handle search with debouncing
  const handleSearchChange = useCallback((searchTerm: string) => {
    setFilters(prev => ({ ...prev, search: searchTerm }));

    if (searchDebounce) {
      clearTimeout(searchDebounce);
    }

    const timeout = setTimeout(() => {
      loadContent(1);
    }, 500);

    setSearchDebounce(timeout);
  }, [loadContent, searchDebounce]);

  // Handle filter changes
  const handleFilterChange = useCallback((key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    loadContent(1);
  }, [loadContent]);

  // Handle pagination
  const handlePageChange = useCallback((page: number) => {
    loadContent(page);
    // Scroll to top of content
    document.getElementById('cms-content-container')?.scrollIntoView({ behavior: 'smooth' });
  }, [loadContent]);

  // Retry loading
  const handleRetry = useCallback(() => {
    loadContent(currentPage, true);
  }, [loadContent, currentPage]);

  // Memoized pagination info
  const paginationInfo = useMemo(() => {
    const totalPages = Math.ceil(totalContent / limit);
    const hasMore = currentPage < totalPages;
    const hasPrevious = currentPage > 1;
    
    return {
      totalPages,
      hasMore,
      hasPrevious,
      startItem: (currentPage - 1) * limit + 1,
      endItem: Math.min(currentPage * limit, totalContent),
    };
  }, [totalContent, limit, currentPage]);

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === 'sv' ? 'sv-SE' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Render content item
  const renderContentItem = (item: CMSContent) => {
    const handleClick = () => {
      if (onContentClick) {
        onContentClick(item);
      } else {
        window.location.href = `/${item.slug}`;
      }
    };

    if (layout === 'list') {
      return (
        <div
          key={item.id}
          className="flex items-center justify-between p-4 bg-gray-800 border border-gray-700 rounded-lg hover:bg-gray-750 transition-colors cursor-pointer"
          onClick={handleClick}
        >
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="font-semibold text-white hover:text-blue-400 transition-colors">
                {item.title}
              </h3>
              {item.is_featured && <Star className="h-4 w-4 text-yellow-500" />}
            </div>
            
            {item.excerpt && (
              <p className="text-gray-300 text-sm mb-2 line-clamp-2">{item.excerpt}</p>
            )}
            
            <div className="flex items-center space-x-4 text-xs text-gray-400">
              <div className="flex items-center space-x-1">
                <User className="h-3 w-3" />
                <span>{item.author?.full_name || 'Unknown'}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Calendar className="h-3 w-3" />
                <span>{formatDate(item.published_at || item.created_at)}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Eye className="h-3 w-3" />
                <span>{item.view_count}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {item.categories?.slice(0, 2).map((cat, idx) => (
              <span
                key={idx}
                className="px-2 py-1 bg-blue-600 text-xs rounded-full"
                style={{ backgroundColor: cat.color || '#3B82F6' }}
              >
                {cat.name}
              </span>
            ))}
          </div>
        </div>
      );
    }

    return (
      <Card
        key={item.id}
        className="bg-gray-800 border-gray-700 hover:bg-gray-750 transition-colors cursor-pointer group"
        onClick={handleClick}
      >
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-3">
            <h3 className="font-semibold text-white group-hover:text-blue-400 transition-colors line-clamp-2">
              {item.title}
            </h3>
            {item.is_featured && <Star className="h-4 w-4 text-yellow-500 flex-shrink-0 ml-2" />}
          </div>
          
          {item.excerpt && (
            <p className="text-gray-300 text-sm mb-4 line-clamp-3">{item.excerpt}</p>
          )}
          
          <div className="flex items-center justify-between text-xs text-gray-400">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-1">
                <User className="h-3 w-3" />
                <span>{item.author?.full_name || 'Unknown'}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Calendar className="h-3 w-3" />
                <span>{formatDate(item.published_at || item.created_at)}</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-1">
              <Eye className="h-3 w-3" />
              <span>{item.view_count}</span>
            </div>
          </div>
          
          {(item.categories || item.tags) && (
            <div className="flex flex-wrap gap-1 mt-3">
              {item.categories?.slice(0, 2).map((cat, idx) => (
                <span
                  key={idx}
                  className="px-2 py-1 bg-blue-600 text-xs rounded-full"
                  style={{ backgroundColor: cat.color || '#3B82F6' }}
                >
                  {cat.name}
                </span>
              ))}
              {item.tags?.slice(0, 2).map((tag, idx) => (
                <span key={idx} className="px-2 py-1 bg-gray-600 text-xs rounded-full">
                  #{tag.name}
                </span>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div id="cms-content-container" className={`space-y-6 ${className}`}>
      {/* Search and Filters */}
      {(showSearch || showFilters) && (
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex flex-wrap items-center gap-4">
              {/* Search */}
              {showSearch && (
                <div className="flex-1 min-w-64">
                  <input
                    type="text"
                    placeholder="Search content..."
                    value={filters.search}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}
              
              {/* Filters */}
              {showFilters && (
                <>
                  <select
                    value={filters.category}
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                    className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All Categories</option>
                    {categories.map((cat) => (
                      <option key={cat.id} value={cat.id}>
                        {cat.name}
                      </option>
                    ))}
                  </select>

                  <select
                    value={filters.tag}
                    onChange={(e) => handleFilterChange('tag', e.target.value)}
                    className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All Tags</option>
                    {tags.map((tag) => (
                      <option key={tag.id} value={tag.id}>
                        {tag.name}
                      </option>
                    ))}
                  </select>

                  <select
                    value={filters.sortBy}
                    onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="published_at">Date Published</option>
                    <option value="created_at">Date Created</option>
                    <option value="updated_at">Last Updated</option>
                    <option value="view_count">Most Viewed</option>
                    <option value="title">Title</option>
                  </select>

                  <select
                    value={filters.sortOrder}
                    onChange={(e) => handleFilterChange('sortOrder', e.target.value as 'asc' | 'desc')}
                    className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="desc">Newest First</option>
                    <option value="asc">Oldest First</option>
                  </select>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loadingState.loading && !loadingState.retrying && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
            <p className="text-gray-400">Loading content...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {loadingState.error && (
        <Card className="bg-red-900/20 border-red-500/30">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-400 mb-4">{loadingState.error}</p>
            <Button
              onClick={handleRetry}
              disabled={loadingState.retrying}
              variant="outline"
              className="border-red-500 text-red-400 hover:bg-red-500/10"
            >
              {loadingState.retrying ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Retrying...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Content Grid/List */}
      {!loadingState.loading && !loadingState.error && (
        <>
          {content.length > 0 ? (
            <div
              className={
                layout === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                  : layout === 'list'
                    ? 'space-y-4'
                    : 'grid grid-cols-1 lg:grid-cols-2 gap-6'
              }
            >
              {content.map(renderContentItem)}
            </div>
          ) : (
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-12 text-center">
                <TrendingUp className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-300 mb-2">No Content Found</h3>
                <p className="text-gray-400">
                  {filters.search || filters.category || filters.tag
                    ? 'Try adjusting your search or filters'
                    : 'No content available at the moment'}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Pagination */}
          {showPagination && content.length > 0 && paginationInfo.totalPages > 1 && (
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-400">
                    Showing {paginationInfo.startItem}-{paginationInfo.endItem} of {totalContent} results
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={!paginationInfo.hasPrevious}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, paginationInfo.totalPages) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <Button
                            key={page}
                            variant={currentPage === page ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => handlePageChange(page)}
                          >
                            {page}
                          </Button>
                        );
                      })}
                      
                      {paginationInfo.totalPages > 5 && (
                        <>
                          <span className="text-gray-400">...</span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(paginationInfo.totalPages)}
                          >
                            {paginationInfo.totalPages}
                          </Button>
                        </>
                      )}
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={!paginationInfo.hasMore}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}

// ===== SPECIALIZED LOADERS =====

export function FeaturedContentLoader(props: Omit<ContentLoaderProps, 'featured'>) {
  return <CMSContentLoader {...props} featured={true} />;
}

export function RecentContentLoader(props: ContentLoaderProps) {
  return (
    <CMSContentLoader
      {...props}
      showFilters={false}
      showPagination={false}
      limit={props.limit || 6}
    />
  );
}

export function CategoryContentLoader({ category, ...props }: ContentLoaderProps) {
  return <CMSContentLoader {...props} category={category} />;
}

export function TagContentLoader({ tag, ...props }: ContentLoaderProps) {
  return <CMSContentLoader {...props} tag={tag} />;
}

// ===== CONTENT DISPLAY COMPONENT =====

interface ContentDisplayProps {
  slug: string;
  language?: string;
  showRelated?: boolean;
  showComments?: boolean;
  className?: string;
}

export function CMSContentDisplay({
  slug,
  language = 'en',
  showRelated = true,
  showComments = false,
  className = '',
}: ContentDisplayProps) {
  const [content, setContent] = useState<CMSContent | null>(null);
  const [relatedContent, setRelatedContent] = useState<CMSContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadContent = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const contentData = await CMSService.getContentBySlug(slug, language, true);

      if (!contentData) {
        setError('Content not found');
        return;
      }

      setContent(contentData);

      // Load related content if enabled
      if (showRelated) {
        const related = await CMSService.getRelatedContent(contentData.id, 4, language);
        setRelatedContent(related);
      }
    } catch (err) {
      console.error('Error loading content:', err);
      setError('Failed to load content');
    } finally {
      setLoading(false);
    }
  }, [slug, language, showRelated]);

  useEffect(() => {
    loadContent();
  }, [loadContent]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === 'sv' ? 'sv-SE' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-700 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-700 rounded w-1/2 mb-6"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-700 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !content) {
    return (
      <Card className="bg-red-900/20 border-red-500/30">
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-red-400 mb-2">Content Not Found</h2>
          <p className="text-red-300">{error || 'The requested content could not be found.'}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Main Content */}
      <article className="prose prose-invert max-w-none">
        {/* Header */}
        <header className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">{content.title}</h1>

          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-400 mb-6">
            <div className="flex items-center space-x-1">
              <User className="h-4 w-4" />
              <span>{content.author?.full_name || 'Unknown Author'}</span>
            </div>

            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(content.published_at || content.created_at)}</span>
            </div>

            <div className="flex items-center space-x-1">
              <Eye className="h-4 w-4" />
              <span>{content.view_count} views</span>
            </div>

            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>Updated {formatDate(content.updated_at)}</span>
            </div>
          </div>

          {/* Categories and Tags */}
          {(content.categories || content.tags) && (
            <div className="flex flex-wrap gap-2 mb-6">
              {content.categories?.map((cat, idx) => (
                <span
                  key={idx}
                  className="px-3 py-1 bg-blue-600 text-white text-sm rounded-full"
                  style={{ backgroundColor: cat.color || '#3B82F6' }}
                >
                  <Folder className="h-3 w-3 inline mr-1" />
                  {cat.name}
                </span>
              ))}
              {content.tags?.map((tag, idx) => (
                <span key={idx} className="px-3 py-1 bg-gray-600 text-white text-sm rounded-full">
                  <Tag className="h-3 w-3 inline mr-1" />
                  {tag.name}
                </span>
              ))}
            </div>
          )}

          {/* Excerpt */}
          {content.excerpt && (
            <div className="text-lg text-gray-300 italic border-l-4 border-blue-500 pl-4 mb-6">
              {content.excerpt}
            </div>
          )}
        </header>

        {/* Content Body */}
        <div
          className="prose prose-invert max-w-none"
          dangerouslySetInnerHTML={{
            __html: content.content.body?.replace(/\n/g, '<br>') || ''
          }}
        />
      </article>

      {/* Related Content */}
      {showRelated && relatedContent.length > 0 && (
        <section>
          <h2 className="text-2xl font-bold text-white mb-6">Related Content</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {relatedContent.map((item) => (
              <Card
                key={item.id}
                className="bg-gray-800 border-gray-700 hover:bg-gray-750 transition-colors cursor-pointer"
                onClick={() => window.location.href = `/${item.slug}`}
              >
                <CardContent className="p-4">
                  <h3 className="font-semibold text-white mb-2 line-clamp-2">
                    {item.title}
                  </h3>
                  {item.excerpt && (
                    <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                      {item.excerpt}
                    </p>
                  )}
                  <div className="flex items-center justify-between text-xs text-gray-400">
                    <span>{formatDate(item.published_at || item.created_at)}</span>
                    <div className="flex items-center space-x-1">
                      <Eye className="h-3 w-3" />
                      <span>{item.view_count}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      )}
    </div>
  );
}
