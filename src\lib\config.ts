// Application Configuration
// Centralized configuration for environment variables and constants

export const config = {
  // Site Configuration
  site: {
    name: 'Light Upon Light',
    description: 'Educational platform for spiritual wisdom, natural healing, Quranic knowledge, and community learning',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'https://lightuponlight.com',
    domain: process.env.NEXT_PUBLIC_SITE_URL?.replace(/https?:\/\//, '') || 'lightuponlight.com',
  },

  // Supabase Configuration
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
  },

  // Email Configuration
  email: {
    adminEmail: '<EMAIL>',
    contactEmail: '<EMAIL>',
    dpoEmail: '<EMAIL>',
    adminEmails: process.env.ADMIN_EMAILS?.split(',') || [
      '<EMAIL>',
      '<EMAIL>'
    ],
  },

  // Analytics Configuration
  analytics: {
    googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
    sentryDsn: process.env.SENTRY_DSN,
  },

  // Feature Flags
  features: {
    enableAnalytics: !!process.env.NEXT_PUBLIC_GA_ID,
    enableErrorMonitoring: !!process.env.SENTRY_DSN,
    enableLiveStream: true,
    enableForum: true,
    enableCommunity: true,
  },

  // Rate Limiting Configuration
  rateLimits: {
    chatMessages: {
      maxRequests: 10,
      windowMs: 60 * 1000, // 1 minute
    },
    contactForms: {
      maxRequests: 5,
      windowMs: 5 * 60 * 1000, // 5 minutes
    },
    gdprRequests: {
      maxRequests: 2,
      windowMs: 60 * 60 * 1000, // 1 hour
    },
  },

  // Security Configuration
  security: {
    maxMessageLength: 1000,
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'],
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  },

  // UI Configuration
  ui: {
    defaultLanguage: 'en',
    supportedLanguages: ['en', 'ar', 'fr', 'es', 'ur', 'sv'],
    defaultTheme: 'light',
    itemsPerPage: 20,
    maxSearchResults: 100,
  },

  // Database Configuration
  database: {
    maxConnections: 20,
    connectionTimeout: 30000, // 30 seconds
    queryTimeout: 60000, // 60 seconds
  },

  // Cache Configuration
  cache: {
    defaultTtl: 5 * 60, // 5 minutes
    longTtl: 60 * 60, // 1 hour
    shortTtl: 60, // 1 minute
  },

  // Development Configuration
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test',
} as const

// Type-safe environment variable validation
export function validateConfig() {
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  ]

  const missingVars = requiredEnvVars.filter(
    (varName) => !process.env[varName]
  )

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}`
    )
  }

  // Validate URLs
  try {
    new URL(config.supabase.url)
    new URL(config.site.url)
  } catch (error) {
    throw new Error('Invalid URL in configuration')
  }

  return true
}

// Helper functions
export const isAdmin = (email: string): boolean => {
  return config.email.adminEmails.includes(email.toLowerCase())
}

export const getApiUrl = (path: string): string => {
  return `${config.site.url}/api${path.startsWith('/') ? path : `/${path}`}`
}

export const getAssetUrl = (path: string): string => {
  return `${config.site.url}${path.startsWith('/') ? path : `/${path}`}`
}

// Export individual configurations for convenience
export const {
  site,
  supabase,
  email,
  analytics,
  features,
  rateLimits,
  security,
  ui,
  database,
  cache,
  isDevelopment,
  isProduction,
  isTest,
} = config

// Validate configuration on import (only in production)
if (isProduction) {
  validateConfig()
}
