"use client";

import { useState, useEffect } from "react";
import { createSupabaseClient } from "@/lib/supabase";
import { User } from "@supabase/supabase-js";
import Link from "next/link";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Video,
  Users,
  MessageCircle,
  Send,
  Settings,
  Share2,
  Eye,
  Clock,
  Shield,
  Trash2,
  Monitor,
  Play,
  Pause,
  Circle,
  Square,
  Star,
} from "lucide-react";
import { ScrollToTopWithProgress } from "@/components/scroll-to-top";

interface ChatMessage {
  id: string;
  user: string;
  message: string;
  timestamp: Date;
  isAdmin?: boolean;
  isModerator?: boolean;
  userId?: string;
}

export default function LiveStreamPage() {
  const [user, setUser] = useState<User | null>(null);
  const [mounted, setMounted] = useState(false);
  const [isLive, setIsLive] = useState(false);
  const [youtubeUrl, setYoutubeUrl] = useState("");
  const [youtubeVideoId, setYoutubeVideoId] = useState("");
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [mutedUsers, setMutedUsers] = useState<string[]>([]);

  const supabase = createSupabaseClient();

  // User permissions - only <EMAIL> can stream
  const isAdmin = user?.email === "<EMAIL>";
  const canStream = isAdmin;
  const canModerate = isAdmin;

  useEffect(() => {
    setMounted(true);
    
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    getUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null);
      }
    );

    return () => subscription.unsubscribe();
  }, [supabase.auth]);

  // Extract YouTube video ID from URL
  const extractYouTubeVideoId = (url: string): string => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : '';
  };

  // Update video ID when URL changes
  useEffect(() => {
    if (youtubeUrl) {
      const videoId = extractYouTubeVideoId(youtubeUrl);
      setYoutubeVideoId(videoId);
    } else {
      setYoutubeVideoId("");
    }
  }, [youtubeUrl]);

  // Start stream
  const startStream = () => {
    if (!canStream || !youtubeVideoId) return;
    setIsLive(true);
    
    // Add system message
    const systemMessage: ChatMessage = {
      id: Date.now().toString(),
      user: "System",
      message: "🔴 Stream is now LIVE! Welcome everyone!",
      timestamp: new Date(),
      isAdmin: false,
      isModerator: false
    };
    setChatMessages(prev => [...prev, systemMessage]);
  };

  // Stop stream
  const stopStream = () => {
    if (!canStream) return;
    setIsLive(false);
    
    // Add system message
    const systemMessage: ChatMessage = {
      id: Date.now().toString(),
      user: "System", 
      message: "⏹️ Stream has ended. Thank you for watching!",
      timestamp: new Date(),
      isAdmin: false,
      isModerator: false
    };
    setChatMessages(prev => [...prev, systemMessage]);
  };

  // Send chat message
  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !user) return;

    const message: ChatMessage = {
      id: Date.now().toString(),
      user: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous',
      message: newMessage,
      timestamp: new Date(),
      isAdmin: isAdmin,
      isModerator: false,
      userId: user.id
    };

    setChatMessages((prev) => [...prev, message]);
    setNewMessage("");
  };

  // Delete message (admin only)
  const deleteMessage = (messageId: string) => {
    if (!canModerate) return;
    setChatMessages(prev => prev.filter(msg => msg.id !== messageId));
  };

  // Mute user (admin only)
  const muteUser = (userId: string) => {
    if (!canModerate) return;
    setMutedUsers(prev => [...prev, userId]);
  };

  // If not mounted, don't render anything to prevent hydration issues
  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Video className="h-8 w-8 text-red-500" />
                {isLive && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                )}
              </div>
              <div>
                <h1 className="text-2xl font-bold">Live Stream</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-300">
                  <div className="flex items-center space-x-1">
                    <div
                      className={`w-2 h-2 rounded-full ${isLive ? "bg-red-500 animate-pulse" : "bg-gray-500"}`}
                    ></div>
                    <span>{isLive ? "LIVE" : "OFFLINE"}</span>
                  </div>
                  {user && (
                    <span className="text-blue-400">
                      {isAdmin ? 'ADMIN' : 'VIEWER'}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Admin Controls */}
        {canStream && (
          <Card className="mb-6 bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <h3 className="text-lg font-semibold mb-4">📡 Stream Controls</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    YouTube Live Stream URL:
                  </label>
                  <input
                    type="url"
                    value={youtubeUrl}
                    onChange={(e) => setYoutubeUrl(e.target.value)}
                    placeholder="https://www.youtube.com/watch?v=VIDEO_ID or https://youtu.be/VIDEO_ID"
                    className="w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
                  />
                  {youtubeVideoId && (
                    <p className="text-sm text-green-400 mt-1">
                      ✓ Valid YouTube URL detected (Video ID: {youtubeVideoId})
                    </p>
                  )}
                  {youtubeUrl && !youtubeVideoId && (
                    <p className="text-sm text-red-400 mt-1">
                      ⚠ Invalid YouTube URL. Please enter a valid YouTube video URL.
                    </p>
                  )}
                </div>

                <div className="flex space-x-2">
                  {!isLive ? (
                    <Button
                      onClick={startStream}
                      disabled={!youtubeVideoId}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      <Circle className="h-4 w-4 mr-2" />
                      Go Live
                    </Button>
                  ) : (
                    <Button onClick={stopStream} variant="destructive">
                      <Square className="h-4 w-4 mr-2" />
                      End Stream
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    onClick={() => setYoutubeUrl("")}
                  >
                    Clear URL
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Video Area */}
          <div className="lg:col-span-3">
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-0">
                <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
                  {isLive && youtubeVideoId ? (
                    <iframe
                      src={`https://www.youtube.com/embed/${youtubeVideoId}?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1&fs=1&cc_load_policy=0&iv_load_policy=3&autohide=0&color=white&disablekb=0&enablejsapi=1&origin=${typeof window !== 'undefined' ? window.location.origin : ''}`}
                      title="YouTube Live Stream"
                      className="w-full h-full"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                      allowFullScreen
                      style={{ border: 'none' }}
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
                          <Video className="h-8 w-8 text-gray-400" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-300 mb-2">⏸️ OFFLINE</h3>
                        <p className="text-gray-400 mb-4">Stream is currently offline</p>
                        <div className="text-sm text-gray-500">
                          <p>Check back later for live sessions on</p>
                          <p className="font-medium">Natural Healing & Wisdom</p>
                        </div>
                        {!user && (
                          <div className="mt-6">
                            <p className="text-gray-400 text-sm mb-3">Sign in to get notified when we go live</p>
                            <div className="flex justify-center space-x-2">
                              <Link href="/auth/signin">
                                <Button variant="outline" size="sm">
                                  Sign In
                                </Button>
                              </Link>
                              <Link href="/auth/signup">
                                <Button size="sm">
                                  Sign Up
                                </Button>
                              </Link>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Live Indicator */}
                  {isLive && (
                    <div className="absolute top-4 left-4">
                      <div className="bg-red-600 px-3 py-1 rounded-full flex items-center space-x-2">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        <span className="text-sm font-bold">LIVE</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Stream Info */}
            <Card className="mt-4 bg-gray-800 border-gray-700">
              <CardContent className="p-4">
                <h2 className="text-xl font-bold mb-4">Natural Healing & Wisdom Session</h2>
                <div className="text-gray-300 space-y-2">
                  <p className="text-base">
                    🌟 <strong>Welcome to Light Upon Light Educational Sessions!</strong>
                  </p>
                  <p className="text-sm">
                    <strong>For Young Learners:</strong> Discover amazing natural remedies that have helped people feel better for thousands of years! Learn simple ways to stay healthy using plants, honey, and gentle healing methods.
                  </p>
                  <p className="text-sm">
                    <strong>For Adults:</strong> Explore the scientific wisdom behind traditional healing practices including cupping therapy, herbal medicine, and spiritual wellness. Understand how ancient knowledge meets modern health science.
                  </p>
                  <p className="text-sm">
                    <strong>Today's Topics:</strong> Natural healing methods, plant medicine benefits, mindful living practices, and the logical wisdom found in traditional remedies that work for all ages.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Chat Sidebar */}
          <div className="lg:col-span-1">
            <Card className="bg-gray-800 border-gray-700 h-[600px] flex flex-col">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center">
                    <MessageCircle className="h-5 w-5 mr-2" />
                    Live Chat
                    {isLive && (
                      <span className="ml-2 px-2 py-1 bg-green-600 text-xs rounded-full">
                        {chatMessages.length}
                      </span>
                    )}
                  </CardTitle>
                </div>
              </CardHeader>

              <CardContent className="flex-1 overflow-y-auto p-4 space-y-3">
                {chatMessages
                  .filter((msg) => !mutedUsers.includes(msg.userId || ''))
                  .map((msg) => (
                    <div key={msg.id} className="group">
                      <div className="flex items-start space-x-2">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span
                              className={`text-sm font-medium ${
                                msg.isAdmin
                                  ? "text-red-400"
                                  : msg.isModerator
                                    ? "text-blue-400"
                                    : "text-gray-300"
                              }`}
                            >
                              {msg.user}
                              {msg.isAdmin && (
                                <Shield className="h-3 w-3 text-red-400 inline ml-1" />
                              )}
                              {msg.isModerator && (
                                <Star className="h-3 w-3 text-blue-400 inline ml-1" />
                              )}
                            </span>
                            <span className="text-xs text-gray-500">
                              {mounted
                                ? msg.timestamp.toLocaleTimeString()
                                : "--:--:--"}
                            </span>
                          </div>
                          <p className="text-sm text-gray-200 mt-1 break-words">
                            {msg.message}
                          </p>
                        </div>
                        {canModerate &&
                          !msg.isAdmin &&
                          msg.user !== "System" && (
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => deleteMessage(msg.id)}
                                className="h-6 w-6 p-0"
                                title="Delete message"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => msg.userId && muteUser(msg.userId)}
                                className="h-6 w-6 p-0"
                                title="Mute user"
                              >
                                <Shield className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                      </div>
                    </div>
                  ))}

                {chatMessages.length === 0 && (
                  <div className="text-center text-gray-400 py-8">
                    <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-sm">No messages yet</p>
                    <p className="text-xs mt-1">
                      {isLive ? "Be the first to say hello!" : "Chat will be active when stream goes live"}
                    </p>
                  </div>
                )}
              </CardContent>

              {/* Chat Input */}
              <div className="p-4 border-t border-gray-700">
                {user ? (
                  <form onSubmit={sendMessage} className="space-y-2">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm text-gray-400">
                        Chatting as:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          isAdmin
                            ? "text-red-400"
                            : "text-gray-300"
                        }`}
                      >
                        {user.user_metadata?.full_name ||
                          user.email?.split("@")[0] ||
                          "Anonymous"}
                        {isAdmin && (
                          <Shield className="h-3 w-3 text-red-400 inline ml-1" />
                        )}
                      </span>
                      <span
                        className={`px-2 py-1 rounded text-xs ${
                          isAdmin
                            ? "bg-red-600 text-white"
                            : "bg-gray-600 text-white"
                        }`}
                      >
                        {isAdmin ? 'ADMIN' : 'VIEWER'}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="Type a message..."
                        className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        maxLength={200}
                      />

                      <Button
                        type="submit"
                        size="sm"
                        disabled={!newMessage.trim()}
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>{newMessage.length}/200 characters</span>
                      {canModerate && (
                        <span className="text-blue-400">
                          Moderation enabled
                        </span>
                      )}
                    </div>
                  </form>
                ) : (
                  <div className="text-center text-gray-400 text-sm">
                    <p className="mb-2">
                      👁️ You can watch the stream without signing in
                    </p>
                    <p className="mb-3">
                      💬 Sign in to participate in chat and interact
                    </p>
                    <Link href="/auth/signin">
                      <Button size="sm" variant="outline">
                        Sign In to Chat
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>

        {/* Enhanced scroll to top with progress for live page */}
        <ScrollToTopWithProgress threshold={200} />
      </div>
    </div>
  );
}
