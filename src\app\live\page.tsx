"use client";

import React, { useState, useEffect, useRef } from "react";
import { useAuth } from "@/components/providers";
import { useLanguage } from "@/components/language-provider";
import { getUserPermissions, getModerationActions, GUEST_BROADCASTER_EMAIL } from "@/lib/stream-permissions";
import { FunctionalButton } from "@/components/functional-buttons";
import { AdminBroadcastStatus } from "@/components/admin-broadcast-status";
import { RoleSwitcher, useActiveRole } from "@/components/role-switcher";
import { ScrollToTopWithProgress } from "@/components/scroll-to-top";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Video,
  Mic,
  MicOff,
  Camera,
  CameraOff,
  Users,
  MessageCircle,
  Send,
  Settings,
  Share2,
  Eye,
  Volume2,
  VolumeX,
  Square,
  Circle,
  Maximize,
  Minimize,
  Shield,
  Star,
  Trash2,
  <PERSON>,
  Wifi,
  WifiOff,
  Radio,
  Monitor,
} from "lucide-react";

interface LocalChatMessage {
  id: string;
  user: string;
  message: string;
  timestamp: Date;
  isAdmin?: boolean;
  isModerator?: boolean;
  isBlocked?: boolean;
  userId?: string;
}

interface UserRole {
  userId: string;
  username: string;
  email: string;
  role: "viewer" | "moderator" | "admin";
  joinedAt: Date;
  isOnline: boolean;
}

interface StreamSettings {
  quality: "low" | "medium" | "high" | "ultra";
  bitrate: number;
  fps: number;
  resolution: string;
  audioQuality: "low" | "medium" | "high";
}

interface SavedStream {
  id: string;
  title: string;
  date: string;
  duration: string;
  size: string;
  thumbnail: string;
  viewers: number;
  likes: number;
}

export default function LiveStreamPage() {
  const { user } = useAuth();
  const { t } = useLanguage();
  const { activeRole, setActiveRole, hasMultipleRoles } = useActiveRole();
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);

  // Prevent hydration issues
  const [mounted, setMounted] = useState(false);

  // Stream state
  const [isLive, setIsLive] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [viewerCount, setViewerCount] = useState(0);
  const [streamDuration, setStreamDuration] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<
    "connected" | "connecting" | "disconnected"
  >("disconnected");
  const [streamTitle, setStreamTitle] = useState(
    "Natural Healing & Wisdom Session"
  );
  const [activeStreamId, setActiveStreamId] = useState<string | null>(null);

  // Stream source integration
  const [streamSource, setStreamSource] = useState<"native" | "youtube">("native");
  const [youtubeUrl, setYoutubeUrl] = useState("");
  const [youtubeVideoId, setYoutubeVideoId] = useState("");

  // Public stream state (visible to everyone)
  const [publicYoutubeVideoId, setPublicYoutubeVideoId] = useState("");
  const [publicStreamSource, setPublicStreamSource] = useState<"native" | "youtube">("native");

  // Get user permissions using the secure system
  const securePermissions = getUserPermissions(user?.email);
  const moderationActions = getModerationActions(securePermissions);

  // Permission shortcuts for easy access
  const canBroadcast = securePermissions.canBroadcast;
  const canModerateChat = securePermissions.canModerateChat;
  const canGrantAccess = securePermissions.canGrantAccess;
  const canManageSite = securePermissions.canManageSite;
  const userRole = securePermissions.role;
  const displayName = securePermissions.displayName;

  // Media controls
  const [isCameraOn, setIsCameraOn] = useState(false);
  const [isMicOn, setIsMicOn] = useState(false);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  const [cameraError, setCameraError] = useState("");
  const [micError, setMicError] = useState("");

  // Chat and User Management - Start with empty chat for real user interactions
  const [chatMessages, setChatMessages] = useState<LocalChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [chatVisible, setChatVisible] = useState(true);
  const [blockedUsers, setBlockedUsers] = useState<string[]>([]);
  const [timedOutUsers, setTimedOutUsers] = useState<{[key: string]: number}>({});
  const [mutedUsers, setMutedUsers] = useState<string[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<UserRole[]>([]);
  const [showUserManagement, setShowUserManagement] = useState(false);
  const [showModerationPanel, setShowModerationPanel] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [bannedWords, setBannedWords] = useState<string[]>(['spam', 'scam', 'fake', 'hate']);
  const [slowModeEnabled, setSlowModeEnabled] = useState(false);
  const [slowModeDelay, setSlowModeDelay] = useState(5);
  const [lastMessageTime, setLastMessageTime] = useState<{[key: string]: number}>({});
  const [currentStreamId] = useState<string>('live-stream-' + Date.now());

  // Stream settings
  const [streamSettings, setStreamSettings] = useState<StreamSettings>({
    quality: "high",
    bitrate: 2500,
    fps: 30,
    resolution: "1280x720",
    audioQuality: "high",
  });
  const [showSettings, setShowSettings] = useState(false);

  // Admin controls - use security service for verification
  const [isAdmin, setIsAdmin] = useState(false);
  const [canStream, setCanStream] = useState(false);
  const [userPermissions, setUserPermissions] = useState({
    isAdmin: false,
    isModerator: false,
    canModerate: false,
    canStream: false
  });
  const [savedStreams, setSavedStreams] = useState<SavedStream[]>([]);

  // Security audit logging
  const logSecurityEvent = (event: string, userEmail: string | null, details?: any) => {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      event,
      userEmail: userEmail || 'anonymous',
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'unknown',
      details
    };

    console.log('🔐 SECURITY AUDIT:', logEntry);

    // In production, you would send this to your security monitoring service
    // Example: send to Supabase audit log table, external monitoring service, etc.
  };

  // Set mounted state to prevent hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  // Verify user permissions when user changes
  useEffect(() => {
    const verifyPermissions = async () => {
      if (user?.email) {
        try {
          setUserPermissions({
            isAdmin: userRole === 'full_admin',
            isModerator: userRole === 'guest_broadcaster' || userRole === 'full_admin',
            canModerate: canModerateChat,
            canStream: canBroadcast
          });
          setIsAdmin(userRole === 'full_admin');
          setCanStream(canBroadcast);
        } catch (error) {
          console.error('Error verifying user permissions:', error);
          setUserPermissions({
            isAdmin: false,
            isModerator: false,
            canModerate: false,
            canStream: false
          });
          setIsAdmin(false);
        }
      } else {
        setUserPermissions({
          isAdmin: false,
          isModerator: false,
          canModerate: false,
          canStream: false
        });
        setIsAdmin(false);
      }
    };

    if (mounted) {
      verifyPermissions();
    }
  }, [user, mounted, activeRole, userRole, canModerateChat, canBroadcast]);

  // Auto-update viewer count and duration
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isLive && mounted) {
      interval = setInterval(() => {
        setStreamDuration((prev) => prev + 1);
        // Simulate realistic viewer count changes (only when live)
        setViewerCount((prev) => {
          const change = Math.floor(Math.random() * 6) - 3; // Smaller changes
          const newCount = Math.max(0, prev + change);
          return Math.min(500, newCount); // More realistic max
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isLive, mounted]);

  // YouTube URL processing
  const extractYouTubeVideoId = (url: string): string => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : '';
  };

  const handleYouTubeUrlChange = (url: string) => {
    setYoutubeUrl(url);
    const videoId = extractYouTubeVideoId(url);
    setYoutubeVideoId(videoId);
  };

  // Publish YouTube stream to public (only broadcasters can do this)
  const publishYouTubeStream = () => {
    if (!canBroadcast || !youtubeVideoId) return;
    setPublicYoutubeVideoId(youtubeVideoId);
    setPublicStreamSource("youtube");
    console.log("Publishing YouTube stream:", youtubeVideoId);
    alert("📺 YouTube stream published to public view!");
  };

  // Auto-publish when admin changes stream source
  useEffect(() => {
    if (canBroadcast && streamSource === "youtube" && youtubeVideoId) {
      setPublicYoutubeVideoId(youtubeVideoId);
      setPublicStreamSource("youtube");
    } else if (canBroadcast && streamSource === "native") {
      setPublicStreamSource("native");
      setPublicYoutubeVideoId("");
    }
  }, [canBroadcast, streamSource, youtubeVideoId]);

  // Request camera and microphone access - RESTRICTED TO AUTHORIZED USERS ONLY
  const requestMediaAccess = async (): Promise<boolean> => {
    // SECURITY CHECK: Only authorized users can access camera/microphone
    if (!user?.email) {
      setCameraError("❌ Authentication required for camera/microphone access");
      logSecurityEvent('UNAUTHORIZED_MEDIA_ACCESS_ATTEMPT', null, { reason: 'no_user' });
      return false;
    }

    // SECURITY CHECK: Only <NAME_EMAIL> can access camera/microphone
    if (!canBroadcast) {
      setCameraError("❌ You are not authorized to access camera/microphone for broadcasting");
      logSecurityEvent('UNAUTHORIZED_MEDIA_ACCESS_ATTEMPT', user.email, { reason: 'no_broadcast_permission' });
      return false;
    }

    // ADDITIONAL SECURITY: Double-check user permissions
    const userPermissions = getUserPermissions(user.email);
    if (!userPermissions.canBroadcast) {
      setCameraError("❌ Broadcasting permissions required for camera/microphone access");
      logSecurityEvent('UNAUTHORIZED_MEDIA_ACCESS_ATTEMPT', user.email, { reason: 'insufficient_permissions' });
      return false;
    }

    logSecurityEvent('AUTHORIZED_MEDIA_ACCESS', user.email, { role: userPermissions.role });

    try {
      setCameraError("");
      setConnectionStatus("connecting");

      const constraints = {
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 },
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
      }

      streamRef.current = stream;
      setIsCameraOn(true);
      setIsMicOn(true);
      setConnectionStatus("connected");

      console.log("✅ Camera/microphone access granted for authorized user:", user.email);
      return true;
    } catch (error: any) {
      console.error("Error accessing media devices:", error);
      setConnectionStatus("disconnected");

      if (error.name === "NotAllowedError") {
        setCameraError("Camera/microphone access denied. Please allow permissions and try again.");
      } else if (error.name === "NotFoundError") {
        setCameraError("No camera or microphone found. Please connect devices and try again.");
      } else {
        setCameraError("Unable to access camera/microphone. Please check your devices.");
      }

      return false;
    }
  };

  // Start live stream - RESTRICTED TO AUTHORIZED USERS ONLY
  const startStream = async () => {
    // SECURITY CHECK: User must be authenticated
    if (!user?.email) {
      alert("❌ Authentication required to start streaming");
      logSecurityEvent('UNAUTHORIZED_STREAM_START_ATTEMPT', null, { reason: 'no_user' });
      return;
    }

    // SECURITY CHECK: User must have broadcast permissions
    if (!canBroadcast) {
      alert("❌ You are not authorized to start live streams");
      logSecurityEvent('UNAUTHORIZED_STREAM_START_ATTEMPT', user.email, { reason: 'no_broadcast_permission' });
      return;
    }

    // ADDITIONAL SECURITY: Double-check user permissions
    const userPermissions = getUserPermissions(user.email);
    if (!userPermissions.canBroadcast) {
      alert("❌ Broadcasting permissions required to start streaming");
      logSecurityEvent('UNAUTHORIZED_STREAM_START_ATTEMPT', user.email, { reason: 'insufficient_permissions' });
      return;
    }

    logSecurityEvent('AUTHORIZED_STREAM_START', user.email, { role: userPermissions.role, streamSource });

    // Only request camera access for native streaming
    if (streamSource === "native") {
      const hasAccess = await requestMediaAccess();
      if (!hasAccess) {
        alert("❌ Camera/microphone access required for native streaming");
        return;
      }
    }

    // For YouTube streaming, no camera access needed
    if (streamSource === "youtube" && !publicYoutubeVideoId) {
      alert("❌ Please enter a valid YouTube URL first");
      return;
    }

    try {
      setIsLive(true);
      setStreamDuration(0);
      setConnectionStatus("connected");

      // Add welcome message to chat
      const welcomeMessage: LocalChatMessage = {
        id: Date.now().toString(),
        user: "System",
        message: "🔴 Live stream started! Welcome everyone to our healing session!",
        timestamp: new Date(),
        isAdmin: true,
        userId: "system"
      };
      setChatMessages([welcomeMessage]);

      alert("🔴 Live stream started! You are now broadcasting to viewers.");
    } catch (error) {
      console.error('Error starting stream:', error);
      alert("🔴 Stream started locally");
    }
  };

  // Stop live stream
  const stopStream = async () => {
    if (!canBroadcast || !user) return;

    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
    }

    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
    }

    setIsLive(false);
    setActiveStreamId(null);
    setIsRecording(false);
    setIsCameraOn(false);
    setIsMicOn(false);
    setConnectionStatus("disconnected");

    // Add end message to chat
    const endMessage: LocalChatMessage = {
      id: Date.now().toString(),
      user: "System",
      message: "⏹️ Stream ended. Thank you all for joining! May you be blessed with healing and wisdom.",
      timestamp: new Date(),
      isAdmin: true,
      userId: "system"
    };
    setChatMessages(prev => [...prev, endMessage]);

    alert("⏹️ Stream ended. Thank you for watching!");
  };

  // Toggle camera - RESTRICTED TO AUTHORIZED USERS ONLY
  const toggleCamera = () => {
    // SECURITY CHECK: Only authorized users can control camera
    if (!user?.email || !canBroadcast) {
      logSecurityEvent('UNAUTHORIZED_CAMERA_TOGGLE_ATTEMPT', user?.email || null);
      alert("❌ You are not authorized to control the camera");
      return;
    }

    if (streamRef.current) {
      const videoTrack = streamRef.current.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !isCameraOn;
        setIsCameraOn(!isCameraOn);
        logSecurityEvent('AUTHORIZED_CAMERA_TOGGLE', user.email, { newState: !isCameraOn });
      }
    }
  };

  // Toggle microphone - RESTRICTED TO AUTHORIZED USERS ONLY
  const toggleMicrophone = () => {
    // SECURITY CHECK: Only authorized users can control microphone
    if (!user?.email || !canBroadcast) {
      logSecurityEvent('UNAUTHORIZED_MICROPHONE_TOGGLE_ATTEMPT', user?.email || null);
      alert("❌ You are not authorized to control the microphone");
      return;
    }

    if (streamRef.current) {
      const audioTrack = streamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !isMicOn;
        setIsMicOn(!isMicOn);
        logSecurityEvent('AUTHORIZED_MICROPHONE_TOGGLE', user.email, { newState: !isMicOn });
      }
    }
  };

  // Toggle recording
  const toggleRecording = async () => {
    if (!canBroadcast || !isLive) return;

    if (!isRecording) {
      try {
        if (streamRef.current) {
          const mediaRecorder = new MediaRecorder(streamRef.current);
          recordedChunksRef.current = [];

          mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              recordedChunksRef.current.push(event.data);
            }
          };

          mediaRecorder.onstop = () => {
            const blob = new Blob(recordedChunksRef.current, {
              type: "video/webm",
            });
            const url = URL.createObjectURL(blob);

            // Create download link
            const a = document.createElement("a");
            a.href = url;
            a.download = `stream-${new Date().toISOString().slice(0, 19)}.webm`;
            a.click();

            alert("🎥 Recording saved successfully!");
          };

          mediaRecorder.start();
          mediaRecorderRef.current = mediaRecorder;
          setIsRecording(true);
          alert("🔴 Recording started");
        }
      } catch (error) {
        console.error('Error starting recording:', error);
        alert("Failed to start recording");
      }
    } else {
      try {
        if (mediaRecorderRef.current) {
          mediaRecorderRef.current.stop();
          mediaRecorderRef.current = null;
          setIsRecording(false);
          alert("⏹️ Recording stopped and saved");
        }
      } catch (error) {
        console.error('Error stopping recording:', error);
        alert("Failed to stop recording");
      }
    }
  };

  // Send chat message
  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !user) return;

    const message: LocalChatMessage = {
      id: Date.now().toString(),
      user: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous',
      message: newMessage,
      timestamp: new Date(),
      isAdmin: userRole === 'full_admin',
      isModerator: userRole === 'guest_broadcaster',
      userId: user.id
    };

    setChatMessages((prev) => [...prev, message]);
    setNewMessage("");
  };

  // Get user role
  const getUserRole = (userEmail?: string) => {
    if (!userEmail) return "viewer";
    if (userEmail === "<EMAIL>" || userEmail === "<EMAIL>") {
      return "admin";
    }
    const userRole = onlineUsers.find((u) => u.email === userEmail);
    return userRole?.role || "viewer";
  };

  // Check if user can moderate
  const canModerate = () => {
    return canModerateChat;
  };

  // Delete message (admin/moderator only)
  const deleteMessage = (messageId: string) => {
    if (!canModerate()) return;
    setChatMessages(prev => prev.filter(msg => msg.id !== messageId));
  };

  // Timeout user (admin/moderator only)
  const timeoutUser = (userId: string, duration: number = 300) => {
    if (!canModerate()) return;
    const timeoutEnd = Date.now() + (duration * 1000);
    setTimedOutUsers(prev => ({ ...prev, [userId]: timeoutEnd }));

    const timeoutMessage: LocalChatMessage = {
      id: Date.now().toString(),
      user: "System",
      message: `User has been timed out for ${duration} seconds`,
      timestamp: new Date(),
      isAdmin: true,
      userId: "system"
    };
    setChatMessages(prev => [...prev, timeoutMessage]);
  };

  // Mute user (admin/moderator only)
  const muteUser = (userId: string) => {
    if (!canModerate()) return;
    setMutedUsers(prev => [...prev, userId]);

    const muteMessage: LocalChatMessage = {
      id: Date.now().toString(),
      user: "System",
      message: `User has been muted`,
      timestamp: new Date(),
      isAdmin: true,
      userId: "system"
    };
    setChatMessages(prev => [...prev, muteMessage]);
  };

  // Check if user is timed out
  const isUserTimedOut = (userId: string) => {
    const timeoutEnd = timedOutUsers[userId];
    if (timeoutEnd && Date.now() < timeoutEnd) {
      return true;
    } else if (timeoutEnd && Date.now() >= timeoutEnd) {
      // Remove expired timeout
      setTimedOutUsers(prev => {
        const newTimeouts = { ...prev };
        delete newTimeouts[userId];
        return newTimeouts;
      });
    }
    return false;
  };

  // If not mounted, don't render anything to prevent hydration issues
  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Video className="h-8 w-8 text-red-500" />
                {isLive && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                )}
              </div>
              <div>
                <div className="flex items-center space-x-3 mb-2">
                  <h1 className="text-2xl font-bold">Live Stream</h1>
                  {user && (
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      userRole === 'full_admin' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                      userRole === 'guest_broadcaster' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                    }`}>
                      {displayName}
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-4 text-sm text-gray-300">
                  <div className="flex items-center space-x-1">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        connectionStatus === "connected"
                          ? "bg-green-500"
                          : connectionStatus === "connecting"
                            ? "bg-yellow-500 animate-pulse"
                            : "bg-red-500"
                      }`}
                    ></div>
                    <span className="capitalize">{connectionStatus}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div
                      className={`w-2 h-2 rounded-full ${isLive ? "bg-red-500 animate-pulse" : "bg-gray-500"}`}
                    ></div>
                    <span>{isLive ? "LIVE" : "OFFLINE"}</span>
                  </div>
                  {isLive && (
                    <>
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4" />
                        <span>{viewerCount.toLocaleString()} viewers</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{Math.floor(streamDuration / 60)}:{(streamDuration % 60).toString().padStart(2, '0')}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <FunctionalButton type="share" size="sm" variant="outline">
                  <Share2 className="h-4 w-4 mr-1" />
                  Share Stream
                </FunctionalButton>
                {isAdmin && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSettings(!showSettings)}
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    Settings
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Role <NAME_EMAIL> */}
        {hasMultipleRoles && (
          <RoleSwitcher onRoleChange={(role) => setActiveRole(role)} />
        )}

        {/* Admin Broadcast Status */}
        <AdminBroadcastStatus />

        {/* Security Warning for Unauthorized Users */}
        {user && !canBroadcast && (
          <Card className="mb-6 bg-red-900/20 border-red-500/30">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Shield className="h-6 w-6 text-red-400" />
                <div>
                  <h3 className="text-lg font-semibold text-red-400">🔒 Camera/Microphone Access Restricted</h3>
                  <p className="text-sm text-gray-300 mt-1">
                    Camera and microphone access is restricted to authorized broadcasters only.
                    Only <span className="font-mono text-yellow-400"><EMAIL></span> and
                    designated guest broadcasters can access streaming controls.
                  </p>
                  <p className="text-xs text-gray-400 mt-2">
                    You can view the live stream and participate in chat, but broadcasting permissions are required for camera/microphone access.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Guest Broadcaster Status - <NAME_EMAIL> */}
        {canGrantAccess && (
          <Card className="mb-6 bg-purple-900/20 border-purple-500/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-purple-400">👑 Live Stream Access Control</h3>
                  <p className="text-sm text-gray-300">Manage guest broadcaster permissions</p>
                </div>
                <div className="text-xs text-purple-400 bg-purple-900/30 px-2 py-1 rounded">
                  Full Admin
                </div>
              </div>

              <div className="space-y-4">
                <div className="p-4 bg-gray-800/50 rounded-lg border border-gray-600">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-white">Current Guest Broadcaster:</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      GUEST_BROADCASTER_EMAIL ? 'bg-green-900/30 text-green-400' : 'bg-gray-700 text-gray-400'
                    }`}>
                      {GUEST_BROADCASTER_EMAIL ? 'Active' : 'None'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-300 font-mono bg-gray-900/50 p-2 rounded">
                    {GUEST_BROADCASTER_EMAIL || 'No guest broadcaster configured'}
                  </p>
                </div>

                {GUEST_BROADCASTER_EMAIL && (
                  <div className="p-3 bg-green-900/20 border border-green-500/30 rounded-md">
                    <h4 className="text-sm font-medium text-green-400 mb-2">✅ Guest Live Stream Permissions:</h4>
                    <ul className="text-xs text-gray-300 space-y-1">
                      <li>• 📡 Start/stop live streams & broadcast publicly</li>
                      <li>• 🎥 Camera and microphone controls</li>
                      <li>• 📺 YouTube iframe integration (compliant)</li>
                      <li>• 💬 Full chat moderation (delete, timeout, mute, kick)</li>
                      <li>• 📌 Pin messages and manage slow mode</li>
                      <li>• 🎬 Stream title editing and settings</li>
                      <li>• 🚫 Hide/report users (YouTube-style options)</li>
                      <li>• ⚠️ NO site admin or user management access</li>
                    </ul>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Stream Source Selector - Only for broadcasters */}
        {canBroadcast && (
          <Card className="mb-6 bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">📡 Stream Source</h3>
                <div className="flex space-x-2">
                  <Button
                    variant={streamSource === "native" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setStreamSource("native")}
                  >
                    <Camera className="h-4 w-4 mr-2" />
                    Native Camera
                  </Button>
                  <Button
                    variant={streamSource === "youtube" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setStreamSource("youtube")}
                  >
                    <Monitor className="h-4 w-4 mr-2" />
                    YouTube
                  </Button>
                </div>
              </div>

              {streamSource === "youtube" && (
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      YouTube Live Stream URL:
                    </label>
                    <input
                      type="url"
                      value={youtubeUrl}
                      onChange={(e) => handleYouTubeUrlChange(e.target.value)}
                      placeholder="https://www.youtube.com/watch?v=VIDEO_ID or https://youtu.be/VIDEO_ID"
                      className="w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
                    />
                    {youtubeVideoId && (
                      <p className="text-sm text-green-400 mt-1">
                        ✓ Valid YouTube URL detected (Video ID: {youtubeVideoId})
                      </p>
                    )}
                    {youtubeUrl && !youtubeVideoId && (
                      <p className="text-sm text-red-400 mt-1">
                        ⚠ Invalid YouTube URL. Please enter a valid YouTube video URL.
                      </p>
                    )}
                  </div>

                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleYouTubeUrlChange("")}
                      className="text-xs"
                    >
                      Clear URL
                    </Button>
                    {youtubeVideoId && (
                      <Button
                        onClick={publishYouTubeStream}
                        className="text-xs bg-red-600 hover:bg-red-700"
                      >
                        📺 Publish to Public
                      </Button>
                    )}
                  </div>
                </div>
              )}


            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Video Area */}
          <div className="lg:col-span-3">
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-0">
                <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
                  {/* PUBLIC STREAM VIEW - Everyone can see this */}
                  {(publicStreamSource === "youtube" && publicYoutubeVideoId) ? (
                    /* YouTube Stream - Public View */
                    <iframe
                      src={`https://www.youtube.com/embed/${publicYoutubeVideoId}?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1&fs=1&cc_load_policy=0&iv_load_policy=3&autohide=0&color=white&disablekb=0&enablejsapi=1&origin=${typeof window !== 'undefined' ? window.location.origin : ''}`}
                      title="YouTube Live Stream"
                      className="w-full h-full"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                      allowFullScreen
                      style={{ border: 'none' }}
                    />
                  ) : isLive ? (
                    /* Native Stream - Public View */
                    <div className="w-full h-full bg-gray-900 flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                          <Video className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-xl font-semibold text-white mb-2">🔴 LIVE</h3>
                        <p className="text-gray-300 mb-4">Natural Healing & Wisdom Session</p>
                        <div className="flex items-center justify-center space-x-4 text-sm text-gray-400">
                          <div className="flex items-center space-x-1">
                            <Users className="h-4 w-4" />
                            <span>{viewerCount.toLocaleString()} viewers</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                            <span>LIVE</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (streamSource === "native" && canBroadcast) ? (
                    <>
                      <video
                        ref={videoRef}
                        autoPlay
                        muted
                        className="w-full h-full object-cover"
                        style={{ display: isCameraOn ? "block" : "none" }}
                      />

                      {!isCameraOn && (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
                          <div className="text-center">
                            <CameraOff className="h-16 w-16 text-gray-500 mx-auto mb-4" />
                            <p className="text-gray-400 mb-2">Camera is off</p>
                            {cameraError && (
                              <p className="text-red-400 text-sm max-w-md">
                                {cameraError}
                              </p>
                            )}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    /* Offline State - Public View */
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
                          <Video className="h-8 w-8 text-gray-400" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-300 mb-2">⏸️ OFFLINE</h3>
                        <p className="text-gray-400 mb-4">Stream is currently offline</p>
                        <div className="text-sm text-gray-500">
                          <p>Check back later for live sessions on</p>
                          <p className="font-medium">Natural Healing & Wisdom</p>
                        </div>
                        {!user && (
                          <div className="mt-6">
                            <p className="text-gray-400 text-sm mb-3">Sign in to get notified when we go live</p>
                            <div className="flex justify-center space-x-2">
                              <Link href="/auth/signin">
                                <Button variant="outline" size="sm">
                                  Sign In
                                </Button>
                              </Link>
                              <Link href="/auth/signup">
                                <Button size="sm">
                                  Sign Up
                                </Button>
                              </Link>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Stream Controls Overlay - Only for Native Streaming */}
                  {streamSource === "native" && (
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center justify-between bg-black bg-opacity-75 rounded-lg p-3">
                        <div className="flex items-center space-x-2">
                        {canBroadcast ? (
                          !isLive ? (
                            <Button
                              onClick={startStream}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              <Circle className="h-4 w-4 mr-2" />
                              Go Live
                            </Button>
                          ) : (
                            <Button onClick={stopStream} variant="destructive">
                              <Square className="h-4 w-4 mr-2" />
                              End Stream
                            </Button>
                          )
                        ) : (
                          <div className="text-gray-400 text-sm">
                            {isLive ? "Stream is live" : user ? "No broadcast permission" : "Sign in to view broadcast controls"}
                          </div>
                        )}

                        {canBroadcast && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={toggleCamera}
                              className={`${isCameraOn ? "bg-green-600 hover:bg-green-700" : "bg-red-600 hover:bg-red-700"} border-0`}
                              title={
                                isCameraOn ? "Turn camera off" : "Turn camera on"
                              }
                            >
                              {isCameraOn ? (
                                <Camera className="h-4 w-4" />
                              ) : (
                                <CameraOff className="h-4 w-4" />
                              )}
                            </Button>

                            <Button
                              variant="outline"
                              size="sm"
                              onClick={toggleMicrophone}
                              className={`${isMicOn ? "bg-green-600 hover:bg-green-700" : "bg-red-600 hover:bg-red-700"} border-0`}
                              title={
                                isMicOn ? "Mute microphone" : "Unmute microphone"
                              }
                            >
                              {isMicOn ? (
                                <Mic className="h-4 w-4" />
                              ) : (
                                <MicOff className="h-4 w-4" />
                              )}
                            </Button>
                          </>
                        )}

                        {canBroadcast && isLive && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={toggleRecording}
                            className={`${isRecording ? "bg-red-600 hover:bg-red-700" : "bg-gray-600 hover:bg-gray-700"} border-0`}
                            title={
                              isRecording ? "Stop recording" : "Start recording"
                            }
                          >
                            <Circle
                              className={`h-4 w-4 ${isRecording ? "animate-pulse" : ""}`}
                            />
                          </Button>
                        )}
                        </div>

                        <div className="flex items-center space-x-2">
                          <div className="flex items-center space-x-2">
                            <Volume2 className="h-4 w-4 text-gray-300" />
                            <input
                              type="range"
                              min="0"
                              max="100"
                              value={volume}
                              onChange={(e) => setVolume(Number(e.target.value))}
                              className="w-20 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                            />
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsMuted(!isMuted)}
                            className="border-0"
                            title={isMuted ? "Unmute" : "Mute"}
                          >
                            {isMuted ? (
                              <VolumeX className="h-4 w-4" />
                            ) : (
                              <Volume2 className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsFullscreen(!isFullscreen)}
                            className="border-0"
                            title="Toggle fullscreen"
                          >
                            {isFullscreen ? (
                              <Minimize className="h-4 w-4" />
                            ) : (
                              <Maximize className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Live Indicator */}
                  {isLive && (
                    <div className="absolute top-4 left-4">
                      <div className="bg-red-600 px-3 py-1 rounded-full flex items-center space-x-2">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        <span className="text-sm font-bold">LIVE</span>
                      </div>
                    </div>
                  )}

                  {/* Recording Indicator */}
                  {isRecording && (
                    <div className="absolute top-4 left-20">
                      <div className="bg-red-800 px-3 py-1 rounded-full flex items-center space-x-2">
                        <Circle className="w-2 h-2 bg-white rounded-full animate-pulse" />
                        <span className="text-sm font-bold">REC</span>
                      </div>
                    </div>
                  )}

                  {/* Viewer Count */}
                  {isLive && (
                    <div className="absolute top-4 right-4">
                      <div className="bg-black bg-opacity-50 px-3 py-1 rounded-full flex items-center space-x-2">
                        <Eye className="h-4 w-4" />
                        <span className="text-sm">
                          {viewerCount.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Connection Status */}
                  <div className="absolute top-16 right-4">
                    <div
                      className={`px-3 py-1 rounded-full flex items-center space-x-2 text-xs ${
                        connectionStatus === "connected"
                          ? "bg-green-600"
                          : connectionStatus === "connecting"
                            ? "bg-yellow-600"
                            : "bg-red-600"
                      }`}
                    >
                      {connectionStatus === "connected" ? (
                        <Wifi className="h-3 w-3" />
                      ) : (
                        <WifiOff className="h-3 w-3" />
                      )}
                      <span className="capitalize">{connectionStatus}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Stream Info */}
            <Card className="mt-4 bg-gray-800 border-gray-700">
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    {canBroadcast ? (
                      <input
                        type="text"
                        value={streamTitle}
                        onChange={(e) => setStreamTitle(e.target.value)}
                        className="text-xl font-bold bg-transparent border-b border-gray-600 focus:border-blue-500 outline-none w-full"
                        placeholder="Enter stream title..."
                      />
                    ) : (
                      <h2 className="text-xl font-bold">{streamTitle}</h2>
                    )}
                    <div className="text-gray-300 mt-2 space-y-2">
                      <p className="text-base">
                        🌟{" "}
                        <strong>
                          Welcome to Light Upon Light Educational Sessions!
                        </strong>
                      </p>
                      <p className="text-sm">
                        <strong>For Young Learners:</strong> Discover amazing
                        natural remedies that have helped people feel better for
                        thousands of years! Learn simple ways to stay healthy
                        using plants, honey, and gentle healing methods.
                      </p>
                      <p className="text-sm">
                        <strong>For Adults:</strong> Explore the scientific
                        wisdom behind traditional healing practices including
                        cupping therapy, herbal medicine, and spiritual
                        wellness. Understand how ancient knowledge meets modern
                        health science.
                      </p>
                      <p className="text-sm">
                        <strong>Today's Topics:</strong> Natural healing
                        methods, plant medicine benefits, mindful living
                        practices, and the logical wisdom found in traditional
                        remedies that work for all ages.
                      </p>
                    </div>
                  </div>
                  <div className="ml-4 text-right">
                    <div className="text-2xl font-bold text-green-400">
                      {viewerCount.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-400">viewers</div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <FunctionalButton
                      type="like"
                      itemId="live-stream"
                      count={89}
                      size="sm"
                    />

                    <FunctionalButton
                      type="bookmark"
                      itemId="live-stream"
                      size="sm"
                    />

                    <FunctionalButton type="share" size="sm" />

                    {isAdmin && (
                      <FunctionalButton
                        type="download"
                        size="sm"
                        onClick={() =>
                          alert("Stream will be saved automatically")
                        }
                      >
                        Save Stream
                      </FunctionalButton>
                    )}
                  </div>

                  {isLive && (
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Radio className="h-4 w-4" />
                        <span>{streamSettings.quality} quality</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Monitor className="h-4 w-4" />
                        <span>{streamSettings.resolution}</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Chat Sidebar */}
          <div className="lg:col-span-1">
            <Card className="bg-gray-800 border-gray-700 h-[600px] flex flex-col">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center">
                    <MessageCircle className="h-5 w-5 mr-2" />
                    Live Chat
                    {isLive && (
                      <span className="ml-2 px-2 py-1 bg-green-600 text-xs rounded-full">
                        {chatMessages.length}
                      </span>
                    )}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setChatVisible(!chatVisible)}
                  >
                    {chatVisible ? (
                      <Minimize className="h-4 w-4" />
                    ) : (
                      <Maximize className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardHeader>

              {chatVisible && (
                <>
                  <CardContent className="flex-1 overflow-y-auto p-4 space-y-3">
                    {chatMessages
                      .filter((msg) => !msg.isBlocked)
                      .map((msg) => (
                        <div key={msg.id} className="group">
                          <div className="flex items-start space-x-2">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <span
                                  className={`text-sm font-medium ${
                                    msg.isAdmin
                                      ? "text-red-400"
                                      : msg.isModerator
                                        ? "text-blue-400"
                                        : "text-gray-300"
                                  }`}
                                >
                                  {msg.user}
                                  {msg.isAdmin && (
                                    <Shield className="h-3 w-3 text-red-400 inline ml-1" />
                                  )}
                                  {msg.isModerator && (
                                    <Star className="h-3 w-3 text-blue-400 inline ml-1" />
                                  )}
                                </span>
                                <span className="text-xs text-gray-500">
                                  {mounted
                                    ? msg.timestamp.toLocaleTimeString()
                                    : "--:--:--"}
                                </span>
                              </div>
                              <p className="text-sm text-gray-200 mt-1 break-words">
                                {msg.message}
                              </p>
                            </div>
                            {canModerate() &&
                              !msg.isAdmin &&
                              msg.user !== "System" && (
                                <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => deleteMessage(msg.id)}
                                    className="h-6 w-6 p-0"
                                    title="Delete message"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => msg.userId && timeoutUser(msg.userId)}
                                    className="h-6 w-6 p-0"
                                    title="Timeout user (5 min)"
                                  >
                                    <Clock className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => msg.userId && muteUser(msg.userId)}
                                    className="h-6 w-6 p-0"
                                    title="Mute user"
                                  >
                                    <Shield className="h-3 w-3" />
                                  </Button>
                                </div>
                              )}
                          </div>
                        </div>
                      ))}

                    {chatMessages.length === 0 && (
                      <div className="text-center text-gray-400 py-8">
                        <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p className="text-sm">No messages yet</p>
                        <p className="text-xs mt-1">
                          {isLive ? "Be the first to say hello!" : "Chat will be active when stream goes live"}
                        </p>
                      </div>
                    )}
                  </CardContent>

                  {/* Chat Input */}
                  <div className="p-4 border-t border-gray-700">
                    {user ? (
                      <form onSubmit={sendMessage} className="space-y-2">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-sm text-gray-400">
                            Chatting as:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              getUserRole(user.email) === "admin"
                                ? "text-red-400"
                                : getUserRole(user.email) === "moderator"
                                  ? "text-blue-400"
                                  : "text-gray-300"
                            }`}
                          >
                            {user.user_metadata?.full_name ||
                              user.email?.split("@")[0] ||
                              "Anonymous"}
                            {getUserRole(user.email) === "admin" && (
                              <Shield className="h-3 w-3 text-red-400 inline ml-1" />
                            )}
                            {getUserRole(user.email) === "moderator" && (
                              <Star className="h-3 w-3 text-blue-400 inline ml-1" />
                            )}
                          </span>
                          <span
                            className={`px-2 py-1 rounded text-xs ${
                              getUserRole(user.email) === "admin"
                                ? "bg-red-600 text-white"
                                : getUserRole(user.email) === "moderator"
                                  ? "bg-blue-600 text-white"
                                  : "bg-gray-600 text-white"
                            }`}
                          >
                            {getUserRole(user.email).toUpperCase()}
                          </span>
                        </div>
                        <div className="flex space-x-2">
                          <input
                            type="text"
                            value={newMessage}
                            onChange={(e) => setNewMessage(e.target.value)}
                            placeholder={`Type a message as ${getUserRole(user.email)}...`}
                            className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            maxLength={200}
                            disabled={user.id ? isUserTimedOut(user.id) : false}
                          />

                          <Button
                            type="submit"
                            size="sm"
                            disabled={!newMessage.trim() || (user.id ? isUserTimedOut(user.id) : false)}
                          >
                            <Send className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>{newMessage.length}/200 characters</span>
                          {canModerate() && (
                            <span className="text-blue-400">
                              Moderation enabled
                            </span>
                          )}
                        </div>
                      </form>
                    ) : (
                      <div className="text-center text-gray-400 text-sm">
                        <p className="mb-2">
                          👁️ You can watch the stream without signing in
                        </p>
                        <p className="mb-3">
                          💬 Sign in to participate in chat and interact
                        </p>
                        <Link href="/auth/signin">
                          <Button size="sm" variant="outline">
                            Sign In to Chat
                          </Button>
                        </Link>
                      </div>
                    )}
                  </div>
                </>
              )}
            </Card>
          </div>
        </div>

        {/* Enhanced scroll to top with progress for live page */}
        <ScrollToTopWithProgress threshold={200} />
      </div>
    </div>
  );
}
