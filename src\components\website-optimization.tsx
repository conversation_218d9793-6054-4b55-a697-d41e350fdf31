"use client";

import React, { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  performanceMonitor,
  preloadCriticalResources,
  type CoreWebVitals,
  PERFORMANCE_THRESHOLDS
} from '@/lib/performance-optimization';
import {
  accessibilityAuditor,
  accessibilityEnhancements,
  type AccessibilityIssue
} from '@/lib/accessibility-audit';
import {
  analytics,
  ConsentManager,
  type ConsentPreferences
} from '@/lib/privacy-analytics';
import {
  Activity,
  Shield,
  Zap,
  Eye,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Settings,
  BarChart3,
  Users,
  Clock,
  Gauge
} from 'lucide-react';

interface OptimizationReport {
  performance: {
    metrics: CoreWebVitals;
    score: number;
    recommendations: string[];
  };
  accessibility: {
    score: number;
    issues: AccessibilityIssue[];
    summary: {
      total: number;
      errors: number;
      warnings: number;
      info: number;
    };
  };
  privacy: {
    consent: ConsentPreferences | null;
    analyticsEnabled: boolean;
    gdprCompliant: boolean;
  };
}

export function WebsiteOptimization() {
  const [report, setReport] = useState<OptimizationReport | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Initialize optimizations on mount
  useEffect(() => {
    initializeOptimizations();
  }, []);

  const initializeOptimizations = async () => {
    // Preload critical resources
    preloadCriticalResources();
    
    // Add accessibility enhancements
    accessibilityEnhancements.addSkipLinks();
    accessibilityEnhancements.enhanceFocusManagement();
    accessibilityEnhancements.addLiveRegions();
    
    // Track initial page view
    analytics.trackPageView(window.location.pathname, document.title);
  };

  const runOptimizationAudit = async () => {
    setIsLoading(true);
    
    try {
      // Performance audit
      const performanceMetrics = await performanceMonitor.measureCoreWebVitals();
      const performanceReport = performanceMonitor.getPerformanceReport();
      
      // Accessibility audit
      const accessibilityIssues = accessibilityAuditor.auditPage();
      const accessibilityReport = accessibilityAuditor.generateReport();
      
      // Privacy audit
      const consent = ConsentManager.getConsent();
      const analyticsStatus = analytics.getAnalyticsSummary();
      
      const optimizationReport: OptimizationReport = {
        performance: {
          metrics: performanceMetrics,
          score: calculatePerformanceScore(performanceMetrics),
          recommendations: performanceReport.recommendations
        },
        accessibility: {
          score: accessibilityReport.score,
          issues: accessibilityReport.issues,
          summary: accessibilityReport.summary
        },
        privacy: {
          consent,
          analyticsEnabled: analyticsStatus.isEnabled,
          gdprCompliant: !!consent
        }
      };
      
      setReport(optimizationReport);
      setLastUpdated(new Date());
      
      // Track audit completion
      analytics.trackInteraction('optimization-audit', 'completed');
      
    } catch (error) {
      console.error('Optimization audit failed:', error);
      analytics.trackError(error as Error, 'optimization-audit');
    } finally {
      setIsLoading(false);
    }
  };

  const calculatePerformanceScore = (metrics: CoreWebVitals): number => {
    let score = 100;
    
    // LCP scoring
    if (metrics.lcp > PERFORMANCE_THRESHOLDS.lcp.needsImprovement) {
      score -= 25;
    } else if (metrics.lcp > PERFORMANCE_THRESHOLDS.lcp.good) {
      score -= 10;
    }
    
    // FID scoring
    if (metrics.fid > PERFORMANCE_THRESHOLDS.fid.needsImprovement) {
      score -= 25;
    } else if (metrics.fid > PERFORMANCE_THRESHOLDS.fid.good) {
      score -= 10;
    }
    
    // CLS scoring
    if (metrics.cls > PERFORMANCE_THRESHOLDS.cls.needsImprovement) {
      score -= 25;
    } else if (metrics.cls > PERFORMANCE_THRESHOLDS.cls.good) {
      score -= 10;
    }
    
    // FCP scoring
    if (metrics.fcp > PERFORMANCE_THRESHOLDS.fcp.needsImprovement) {
      score -= 15;
    } else if (metrics.fcp > PERFORMANCE_THRESHOLDS.fcp.good) {
      score -= 5;
    }
    
    // TTFB scoring
    if (metrics.ttfb > PERFORMANCE_THRESHOLDS.ttfb.needsImprovement) {
      score -= 10;
    } else if (metrics.ttfb > PERFORMANCE_THRESHOLDS.ttfb.good) {
      score -= 5;
    }
    
    return Math.max(0, Math.round(score));
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number): "default" | "secondary" | "destructive" | "outline" => {
    if (score >= 90) return 'default';
    if (score >= 70) return 'secondary';
    return 'destructive';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Website Optimization
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Performance, Accessibility & Privacy Audit
          </p>
        </div>
        <Button 
          onClick={runOptimizationAudit} 
          disabled={isLoading}
          className="flex items-center space-x-2"
        >
          <Activity className="h-4 w-4" />
          <span>{isLoading ? 'Running Audit...' : 'Run Audit'}</span>
        </Button>
      </div>

      {lastUpdated && (
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Last updated: {lastUpdated.toLocaleString()}
        </div>
      )}

      {report && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Performance Score */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Performance</CardTitle>
              <Zap className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <span className={getScoreColor(report.performance.score)}>
                  {report.performance.score}
                </span>
                <span className="text-sm text-gray-500 ml-1">/100</span>
              </div>
              <Badge variant={getScoreBadgeVariant(report.performance.score)} className="mt-2">
                {report.performance.score >= 90 ? 'Excellent' : 
                 report.performance.score >= 70 ? 'Good' : 'Needs Improvement'}
              </Badge>
            </CardContent>
          </Card>

          {/* Accessibility Score */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Accessibility</CardTitle>
              <Eye className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <span className={getScoreColor(report.accessibility.score)}>
                  {report.accessibility.score}
                </span>
                <span className="text-sm text-gray-500 ml-1">/100</span>
              </div>
              <div className="flex items-center space-x-2 mt-2">
                <Badge variant="outline" className="text-xs">
                  {report.accessibility.summary.errors} errors
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {report.accessibility.summary.warnings} warnings
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Privacy Compliance */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Privacy</CardTitle>
              <Shield className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  {report.privacy.gdprCompliant ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  <span className="text-sm">GDPR Compliant</span>
                </div>
                <div className="flex items-center space-x-2">
                  {report.privacy.consent ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  )}
                  <span className="text-sm">Consent Collected</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {report && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Gauge className="h-5 w-5" />
                <span>Core Web Vitals</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Largest Contentful Paint (LCP)</span>
                  <Badge variant={report.performance.metrics.lcp <= PERFORMANCE_THRESHOLDS.lcp.good ? 'default' : 'destructive'}>
                    {Math.round(report.performance.metrics.lcp)}ms
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">First Input Delay (FID)</span>
                  <Badge variant={report.performance.metrics.fid <= PERFORMANCE_THRESHOLDS.fid.good ? 'default' : 'destructive'}>
                    {Math.round(report.performance.metrics.fid)}ms
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Cumulative Layout Shift (CLS)</span>
                  <Badge variant={report.performance.metrics.cls <= PERFORMANCE_THRESHOLDS.cls.good ? 'default' : 'destructive'}>
                    {report.performance.metrics.cls.toFixed(3)}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Accessibility Issues */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>Accessibility Issues</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {report.accessibility.issues.slice(0, 5).map((issue, index) => (
                  <div key={index} className="flex items-start space-x-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    {issue.type === 'error' ? (
                      <XCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                    ) : issue.type === 'warning' ? (
                      <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                    ) : (
                      <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                    )}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {issue.description}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {issue.recommendation}
                      </p>
                    </div>
                  </div>
                ))}
                {report.accessibility.issues.length === 0 && (
                  <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                    <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-600" />
                    <p>No accessibility issues found!</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {report && report.performance.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Optimization Recommendations</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {report.performance.recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {recommendation}
                  </span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default WebsiteOptimization;
