import { Shield, CheckCircle, Eye, Download, Trash2, Edit, Lock, Globe } from 'lucide-react'

const gdprRights = [
  {
    right: 'Right to Information',
    description: 'You have the right to know what personal data we collect and how we use it',
    implementation: 'Transparent privacy policy and clear consent mechanisms',
    icon: Eye,
    color: 'text-blue-600'
  },
  {
    right: 'Right of Access',
    description: 'You can request a copy of all personal data we hold about you',
    implementation: 'Data export functionality and detailed data reports',
    icon: Download,
    color: 'text-green-600'
  },
  {
    right: 'Right to Rectification',
    description: 'You can request correction of inaccurate or incomplete personal data',
    implementation: 'Profile editing tools and data correction requests',
    icon: Edit,
    color: 'text-yellow-600'
  },
  {
    right: 'Right to Erasure',
    description: 'You can request deletion of your personal data under certain circumstances',
    implementation: 'Account deletion and data removal processes',
    icon: Trash2,
    color: 'text-red-600'
  },
  {
    right: 'Right to Data Portability',
    description: 'You can request your data in a structured, machine-readable format',
    implementation: 'Data export in JSON/CSV formats',
    icon: Download,
    color: 'text-purple-600'
  },
  {
    right: 'Right to Object',
    description: 'You can object to processing of your personal data for certain purposes',
    implementation: 'Opt-out mechanisms and consent withdrawal',
    icon: Shield,
    color: 'text-orange-600'
  }
]

const dataProcessing = [
  {
    category: 'Account Information',
    data: ['Email address', 'Username', 'Profile preferences', 'Authentication data'],
    purpose: 'Account management and authentication',
    legalBasis: 'Contract performance',
    retention: 'Until account deletion'
  },
  {
    category: 'Learning Progress',
    data: ['Course completion', 'Quiz scores', 'Learning paths', 'Achievements'],
    purpose: 'Educational service provision and progress tracking',
    legalBasis: 'Contract performance',
    retention: '3 years after last activity'
  },
  {
    category: 'Community Activity',
    data: ['Forum posts', 'Comments', 'Live chat messages', 'Community interactions'],
    purpose: 'Community platform functionality',
    legalBasis: 'Legitimate interest',
    retention: 'Until content deletion or account removal'
  },
  {
    category: 'Technical Data',
    data: ['IP address', 'Browser information', 'Device data', 'Usage analytics'],
    purpose: 'Security, performance optimization, and service improvement',
    legalBasis: 'Legitimate interest',
    retention: '12 months'
  }
]

const complianceMeasures = [
  {
    measure: 'Data Protection by Design',
    description: 'Privacy considerations integrated into all system development',
    implementation: ['Privacy impact assessments', 'Secure coding practices', 'Minimal data collection']
  },
  {
    measure: 'Data Security',
    description: 'Technical and organizational measures to protect personal data',
    implementation: ['Encryption at rest and in transit', 'Access controls', 'Regular security audits']
  },
  {
    measure: 'Consent Management',
    description: 'Clear and granular consent mechanisms for data processing',
    implementation: ['Cookie consent manager', 'Opt-in/opt-out controls', 'Consent records']
  },
  {
    measure: 'Data Subject Rights',
    description: 'Processes to handle and respond to data subject requests',
    implementation: ['Automated data export', 'Request handling system', '30-day response time']
  }
]

export default function GDPRPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Shield className="h-16 w-16 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              GDPR Compliance
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Light Upon Light is committed to protecting your privacy and ensuring full compliance 
              with the General Data Protection Regulation (GDPR) and other applicable privacy laws.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Compliance Statement */}
        <div className="mb-12">
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <CheckCircle className="h-6 w-6 text-green-600 mr-3" />
              <h2 className="text-xl font-bold text-green-800 dark:text-green-300">
                GDPR Compliant Platform
              </h2>
            </div>
            <p className="text-green-700 dark:text-green-300">
              Our platform has been designed and implemented with GDPR compliance at its core. 
              We respect your privacy rights and provide transparent, secure, and user-controlled 
              data processing practices.
            </p>
          </div>
        </div>

        {/* Your Rights */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">
            Your Data Protection Rights
          </h2>
          <div className="space-y-6">
            {gdprRights.map((right, index) => {
              const Icon = right.icon
              return (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                  <div className="flex items-start space-x-4">
                    <Icon className={`h-6 w-6 ${right.color} mt-1`} />
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {right.right}
                      </h3>
                      <p className="text-gray-700 dark:text-gray-300 mb-3">
                        {right.description}
                      </p>
                      <div className="bg-gray-50 dark:bg-gray-700 rounded p-3">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                          How we implement this:
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {right.implementation}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Data Processing */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">
            How We Process Your Data
          </h2>
          <div className="space-y-4">
            {dataProcessing.map((processing, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {processing.category}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Data Types:</h4>
                    <ul className="space-y-1">
                      {processing.data.map((item, itemIndex) => (
                        <li key={itemIndex} className="text-sm text-gray-600 dark:text-gray-300">
                          • {item}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Purpose:</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{processing.purpose}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Legal Basis:</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{processing.legalBasis}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Retention:</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{processing.retention}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Compliance Measures */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">
            Our Compliance Measures
          </h2>
          <div className="space-y-6">
            {complianceMeasures.map((measure, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {measure.measure}
                </h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  {measure.description}
                </p>
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded p-4">
                  <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">
                    Implementation:
                  </h4>
                  <ul className="space-y-1">
                    {measure.implementation.map((item, itemIndex) => (
                      <li key={itemIndex} className="text-sm text-blue-700 dark:text-blue-300">
                        • {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Exercise Your Rights */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Exercise Your Rights
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <p className="text-gray-700 dark:text-gray-300 mb-6">
              To exercise any of your data protection rights, please contact us using the methods below. 
              We will respond to your request within 30 days as required by GDPR.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button className="bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                <Download className="h-5 w-5 mx-auto mb-2" />
                Request Data Export
              </button>
              <button className="bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-colors">
                <Edit className="h-5 w-5 mx-auto mb-2" />
                Update My Data
              </button>
              <button className="bg-red-600 text-white px-4 py-3 rounded-lg hover:bg-red-700 transition-colors">
                <Trash2 className="h-5 w-5 mx-auto mb-2" />
                Delete My Account
              </button>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Data Protection Contact
          </h2>
          <p className="text-gray-700 dark:text-gray-300 mb-4">
            For any questions about our GDPR compliance or to exercise your data protection rights:
          </p>
          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
              <Shield className="h-5 w-5" />
              <span>Data Protection Officer: Mohammad Abbas</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
              <Globe className="h-5 w-5" />
              <span>Email: <EMAIL></span>
            </div>
            <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
              <Lock className="h-5 w-5" />
              <span>Subject: GDPR Data Request</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
