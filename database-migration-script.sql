-- DATABASE MIGRATION SCRIPT
-- Light Upon Light Platform - Safe Production Migration

-- =====================================================
-- MIGRATION SAFETY CHECKS
-- =====================================================

-- Check if we're running on the correct database
DO $$
BEGIN
  IF current_database() != 'postgres' THEN
    RAISE EXCEPTION 'This migration must be run on the postgres database';
  END IF;
END $$;

-- Create migration log table
CREATE TABLE IF NOT EXISTS migration_log (
  id SERIAL PRIMARY KEY,
  migration_name TEXT NOT NULL,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  status TEXT CHECK (status IN ('running', 'completed', 'failed', 'rolled_back')),
  error_message TEXT
);

-- Log migration start
INSERT INTO migration_log (migration_name, status) 
VALUES ('database_security_performance_fixes', 'running');

-- =====================================================
-- STEP 1: BACKUP EXISTING POLICIES (FOR ROLLBACK)
-- =====================================================

-- Create backup table for existing policies
CREATE TABLE IF NOT EXISTS policy_backup AS
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE schemaname = 'public';

-- =====================================================
-- STEP 2: APPLY SECURITY FIXES SAFELY
-- =====================================================

-- Add missing columns if they don't exist
DO $$
BEGIN
  -- Add reply_count to forum_posts if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'forum_posts' AND column_name = 'reply_count'
  ) THEN
    ALTER TABLE forum_posts ADD COLUMN reply_count INTEGER DEFAULT 0;
  END IF;

  -- Add is_archived to live_streams if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'live_streams' AND column_name = 'is_archived'
  ) THEN
    ALTER TABLE live_streams ADD COLUMN is_archived BOOLEAN DEFAULT false;
  END IF;

  -- Add event_date and event_time to events if they don't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'events' AND column_name = 'event_date'
  ) THEN
    ALTER TABLE events ADD COLUMN event_date DATE;
    ALTER TABLE events ADD COLUMN event_time TIME;
    
    -- Migrate existing scheduled_at data
    UPDATE events 
    SET event_date = scheduled_at::date,
        event_time = scheduled_at::time
    WHERE scheduled_at IS NOT NULL;
  END IF;
END $$;

-- =====================================================
-- STEP 3: APPLY RLS POLICIES SAFELY
-- =====================================================

-- Function to safely create policies
CREATE OR REPLACE FUNCTION safe_create_policy(
  policy_name TEXT,
  table_name TEXT,
  policy_command TEXT,
  policy_definition TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Drop policy if it exists
  EXECUTE format('DROP POLICY IF EXISTS %I ON %I', policy_name, table_name);
  
  -- Create new policy
  EXECUTE format('CREATE POLICY %I ON %I %s %s', 
    policy_name, table_name, policy_command, policy_definition);
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to create policy % on table %: %', policy_name, table_name, SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Apply critical RLS policies
SELECT safe_create_policy(
  'users_select_active',
  'users',
  'FOR SELECT',
  'USING (is_active = true)'
);

SELECT safe_create_policy(
  'users_update_own',
  'users',
  'FOR UPDATE',
  'USING (auth.uid() = id)'
);

SELECT safe_create_policy(
  'chat_messages_select_active',
  'chat_messages',
  'FOR SELECT',
  'USING (is_deleted = false)'
);

SELECT safe_create_policy(
  'chat_messages_insert_auth',
  'chat_messages',
  'FOR INSERT',
  'WITH CHECK (auth.role() = ''authenticated'' AND auth.uid() = user_id)'
);

-- =====================================================
-- STEP 4: CREATE INDEXES SAFELY
-- =====================================================

-- Function to safely create indexes
CREATE OR REPLACE FUNCTION safe_create_index(
  index_name TEXT,
  table_name TEXT,
  index_definition TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Drop index if it exists
  EXECUTE format('DROP INDEX IF EXISTS %I', index_name);
  
  -- Create new index concurrently
  EXECUTE format('CREATE INDEX CONCURRENTLY %I ON %I %s', 
    index_name, table_name, index_definition);
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to create index %: %', index_name, SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Apply critical indexes
SELECT safe_create_index(
  'idx_forum_posts_active_category',
  'forum_posts',
  '(category_id, created_at DESC) WHERE is_deleted = false'
);

SELECT safe_create_index(
  'idx_chat_messages_stream_time',
  'chat_messages',
  '(stream_id, created_at ASC) WHERE is_deleted = false'
);

SELECT safe_create_index(
  'idx_users_email_active',
  'users',
  '(email) WHERE is_active = true'
);

-- =====================================================
-- STEP 5: CREATE ESSENTIAL FUNCTIONS
-- =====================================================

-- Updated timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  IF row(NEW.*) IS DISTINCT FROM row(OLD.*) THEN
    NEW.updated_at = CURRENT_TIMESTAMP;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Admin verification function
CREATE OR REPLACE FUNCTION is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = user_id 
      AND role = 'admin' 
      AND is_active = true
      AND email IN ('<EMAIL>', '<EMAIL>')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Moderator verification function
CREATE OR REPLACE FUNCTION is_moderator(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = user_id 
      AND role IN ('admin', 'moderator') 
      AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Input sanitization function
CREATE OR REPLACE FUNCTION sanitize_input(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
  IF input_text IS NULL THEN
    RETURN NULL;
  END IF;
  
  RETURN regexp_replace(
    regexp_replace(
      regexp_replace(
        trim(input_text), 
        '<[^>]*>', '', 'g'
      ), 
      'javascript:', '', 'gi'
    ),
    'on\w+\s*=', '', 'gi'
  );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- =====================================================
-- STEP 6: APPLY TRIGGERS SAFELY
-- =====================================================

-- Function to safely create triggers
CREATE OR REPLACE FUNCTION safe_create_trigger(
  trigger_name TEXT,
  table_name TEXT,
  trigger_definition TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Drop trigger if it exists
  EXECUTE format('DROP TRIGGER IF EXISTS %I ON %I', trigger_name, table_name);
  
  -- Create new trigger
  EXECUTE format('CREATE TRIGGER %I %s ON %I FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()', 
    trigger_name, trigger_definition, table_name);
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to create trigger % on table %: %', trigger_name, table_name, SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers
SELECT safe_create_trigger('update_users_updated_at', 'users', 'BEFORE UPDATE');
SELECT safe_create_trigger('update_forum_posts_updated_at', 'forum_posts', 'BEFORE UPDATE');
SELECT safe_create_trigger('update_forum_replies_updated_at', 'forum_replies', 'BEFORE UPDATE');

-- =====================================================
-- STEP 7: DATA CLEANUP AND OPTIMIZATION
-- =====================================================

-- Clean up orphaned records safely
DO $$
BEGIN
  -- Remove orphaned forum replies
  DELETE FROM forum_replies 
  WHERE post_id NOT IN (SELECT id FROM forum_posts);
  
  -- Remove orphaned post likes
  DELETE FROM post_likes 
  WHERE (post_id IS NOT NULL AND post_id NOT IN (SELECT id FROM forum_posts))
     OR (reply_id IS NOT NULL AND reply_id NOT IN (SELECT id FROM forum_replies));
  
  -- Remove orphaned chat messages
  DELETE FROM chat_messages 
  WHERE stream_id NOT IN (SELECT id FROM live_streams);
  
  RAISE NOTICE 'Orphaned records cleaned up successfully';
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Error during cleanup: %', SQLERRM;
END $$;

-- Update statistics
DO $$
DECLARE
  table_record RECORD;
BEGIN
  FOR table_record IN 
    SELECT tablename FROM pg_tables WHERE schemaname = 'public'
  LOOP
    EXECUTE 'ANALYZE ' || quote_ident(table_record.tablename);
  END LOOP;
  
  RAISE NOTICE 'Table statistics updated';
END $$;

-- =====================================================
-- STEP 8: VERIFY MIGRATION SUCCESS
-- =====================================================

-- Verification function
CREATE OR REPLACE FUNCTION verify_migration()
RETURNS TABLE (
  check_name TEXT,
  status TEXT,
  details TEXT
) AS $$
BEGIN
  -- Check RLS is enabled
  RETURN QUERY
  SELECT 
    'RLS_enabled'::TEXT,
    CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END,
    'Row Level Security enabled on ' || COUNT(*)::TEXT || ' tables'
  FROM pg_class c
  JOIN pg_namespace n ON n.oid = c.relnamespace
  WHERE n.nspname = 'public' 
    AND c.relkind = 'r' 
    AND c.relrowsecurity = true;

  -- Check critical indexes exist
  RETURN QUERY
  SELECT 
    'critical_indexes'::TEXT,
    CASE WHEN COUNT(*) >= 3 THEN 'PASS' ELSE 'FAIL' END,
    COUNT(*)::TEXT || ' critical indexes found'
  FROM pg_indexes 
  WHERE schemaname = 'public' 
    AND indexname LIKE 'idx_%';

  -- Check functions exist
  RETURN QUERY
  SELECT 
    'essential_functions'::TEXT,
    CASE WHEN COUNT(*) >= 3 THEN 'PASS' ELSE 'FAIL' END,
    COUNT(*)::TEXT || ' essential functions found'
  FROM pg_proc p
  JOIN pg_namespace n ON n.oid = p.pronamespace
  WHERE n.nspname = 'public' 
    AND p.proname IN ('is_admin', 'is_moderator', 'sanitize_input');
END;
$$ LANGUAGE plpgsql;

-- Run verification
SELECT * FROM verify_migration();

-- =====================================================
-- STEP 9: COMPLETE MIGRATION
-- =====================================================

-- Update migration log
UPDATE migration_log 
SET completed_at = NOW(), status = 'completed'
WHERE migration_name = 'database_security_performance_fixes' 
  AND status = 'running';

-- Clean up temporary functions
DROP FUNCTION IF EXISTS safe_create_policy(TEXT, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS safe_create_index(TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS safe_create_trigger(TEXT, TEXT, TEXT);

-- Final success message
DO $$
BEGIN
  RAISE NOTICE 'Migration completed successfully at %', NOW();
  RAISE NOTICE 'Database security and performance fixes have been applied';
  RAISE NOTICE 'Please verify all application functionality';
END $$;
