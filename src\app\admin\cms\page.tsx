"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/components/providers";
import { useLanguage } from "@/components/language-provider";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  Upload,
  Save,
  X,
  FileText,
  Image,
  Video,
  Calendar,
  Tag,
  Folder,
  Settings,
  BarChart3,
  Users,
  Globe,
  Clock,
  CheckCircle,
  AlertCircle,
  Archive,
  Star,
  TrendingUp,
} from "lucide-react";
import { CMSContentEditor } from "@/components/cms-content-editor";

interface CMSContent {
  id: string;
  title: string;
  slug: string;
  content_type_id: string;
  content: any;
  excerpt?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  featured_image_id?: string;
  status: 'draft' | 'published' | 'archived' | 'scheduled';
  published_at?: string;
  scheduled_at?: string;
  language: string;
  author_id: string;
  author?: { full_name: string; email: string };
  created_at: string;
  updated_at: string;
  view_count: number;
  is_featured: boolean;
  categories?: string[];
  tags?: string[];
}

interface CMSStats {
  totalContent: number;
  publishedContent: number;
  draftContent: number;
  totalViews: number;
  contentByType: Record<string, number>;
  recentActivity: Array<{
    id: string;
    action: string;
    content_title: string;
    author: string;
    timestamp: string;
  }>;
}

export default function CMSAdminPage() {
  const { user } = useAuth();
  const { t } = useLanguage();

  // State management
  const [mounted, setMounted] = useState(false);
  const [activeTab, setActiveTab] = useState<'dashboard' | 'content' | 'media' | 'categories' | 'settings'>('dashboard');
  const [content, setContent] = useState<CMSContent[]>([]);
  const [stats, setStats] = useState<CMSStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [selectedContent, setSelectedContent] = useState<CMSContent | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 20;

  const checkAdminAccess = useCallback(async () => {
    if (!user) return;

    try {
      // Check if user has admin/moderator role
      const response = await fetch('/api/auth/check-role');
      const data = await response.json();

      if (data.success && ['admin', 'moderator'].includes(data.role)) {
        setIsAdmin(true);
      }
    } catch (error) {
      console.error('Error checking admin access:', error);
    }
  }, [user]);

  const loadDashboardData = useCallback(async () => {
    setLoading(true);
    try {
      // Load content list
      const contentParams = new URLSearchParams({
        limit: itemsPerPage.toString(),
        offset: ((currentPage - 1) * itemsPerPage).toString(),
        ...(filterStatus !== 'all' && { status: filterStatus }),
        ...(filterType !== 'all' && { content_type: filterType }),
        ...(searchTerm && { search: searchTerm }),
      });

      const [contentResponse, statsResponse] = await Promise.all([
        fetch(`/api/cms/content?${contentParams}`),
        fetch('/api/cms/stats')
      ]);

      if (contentResponse.ok) {
        const contentData = await contentResponse.json();
        setContent(contentData.data.content);
        setTotalPages(Math.ceil(contentData.data.total / itemsPerPage));
      }

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  }, [currentPage, filterStatus, filterType, searchTerm, itemsPerPage]);

  useEffect(() => {
    setMounted(true);
    checkAdminAccess();
  }, [checkAdminAccess]);

  useEffect(() => {
    if (mounted && isAdmin) {
      loadDashboardData();
    }
  }, [mounted, isAdmin, currentPage, filterStatus, filterType, searchTerm, loadDashboardData]);

  const handleCreateContent = () => {
    setSelectedContent(null);
    setShowEditor(true);
  };

  const handleEditContent = (content: CMSContent) => {
    setSelectedContent(content);
    setShowEditor(true);
  };

  const handleDeleteContent = async (contentId: string) => {
    if (!confirm('Are you sure you want to delete this content?')) return;

    try {
      const response = await fetch(`/api/cms/content?id=${contentId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        loadDashboardData();
      }
    } catch (error) {
      console.error('Error deleting content:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'draft':
        return <Edit className="h-4 w-4 text-yellow-500" />;
      case 'scheduled':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'archived':
        return <Archive className="h-4 w-4 text-gray-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'page':
        return <FileText className="h-4 w-4" />;
      case 'post':
        return <FileText className="h-4 w-4" />;
      case 'educational':
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  if (!mounted) return null;

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
            <p className="text-gray-300 mb-6">Please sign in to access the CMS admin panel.</p>
            <Button onClick={() => window.location.href = '/auth/signin'}>
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">Insufficient Permissions</h2>
            <p className="text-gray-300 mb-6">You need admin or moderator privileges to access the CMS.</p>
            <Button onClick={() => window.location.href = '/'}>
              Return Home
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">CMS Admin Dashboard</h1>
              <p className="text-gray-300">Manage your website content</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button onClick={handleCreateContent} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                Create Content
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-6">
          {[
            { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
            { id: 'content', label: 'Content', icon: FileText },
            { id: 'media', label: 'Media', icon: Image },
            { id: 'categories', label: 'Categories', icon: Folder },
            { id: 'settings', label: 'Settings', icon: Settings },
          ].map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? 'default' : 'outline'}
              onClick={() => setActiveTab(tab.id as any)}
              className="flex items-center space-x-2"
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </Button>
          ))}
        </div>

        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            {stats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-400">Total Content</p>
                        <p className="text-2xl font-bold">{stats.totalContent}</p>
                      </div>
                      <FileText className="h-8 w-8 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-400">Published</p>
                        <p className="text-2xl font-bold text-green-500">{stats.publishedContent}</p>
                      </div>
                      <CheckCircle className="h-8 w-8 text-green-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-400">Drafts</p>
                        <p className="text-2xl font-bold text-yellow-500">{stats.draftContent}</p>
                      </div>
                      <Edit className="h-8 w-8 text-yellow-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-400">Total Views</p>
                        <p className="text-2xl font-bold">{stats.totalViews.toLocaleString()}</p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-purple-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Recent Activity */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                {stats?.recentActivity && stats.recentActivity.length > 0 ? (
                  <div className="space-y-4">
                    {stats.recentActivity.map((activity) => (
                      <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                        <div>
                          <p className="font-medium">{activity.content_title}</p>
                          <p className="text-sm text-gray-400">
                            {activity.action} by {activity.author}
                          </p>
                        </div>
                        <span className="text-sm text-gray-500">
                          {new Date(activity.timestamp).toLocaleDateString()}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-400 text-center py-8">No recent activity</p>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Content Tab */}
        {activeTab === 'content' && (
          <div className="space-y-6">
            {/* Filters and Search */}
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-4">
                <div className="flex flex-wrap items-center gap-4">
                  <div className="flex-1 min-w-64">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search content..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Status</option>
                    <option value="published">Published</option>
                    <option value="draft">Draft</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="archived">Archived</option>
                  </select>

                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Types</option>
                    <option value="page">Pages</option>
                    <option value="post">Posts</option>
                    <option value="educational">Educational</option>
                    <option value="legal">Legal</option>
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* Content List */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle>Content Management</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                    <p className="text-gray-400 mt-2">Loading content...</p>
                  </div>
                ) : content.length > 0 ? (
                  <div className="space-y-4">
                    {content.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div className="flex items-center space-x-4 flex-1">
                          <div className="flex items-center space-x-2">
                            {getContentTypeIcon(item.content_type_id)}
                            {getStatusIcon(item.status)}
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h3 className="font-medium">{item.title}</h3>
                              {item.is_featured && (
                                <Star className="h-4 w-4 text-yellow-500" />
                              )}
                            </div>
                            <div className="flex items-center space-x-4 text-sm text-gray-400 mt-1">
                              <span>/{item.slug}</span>
                              <span>{item.author?.full_name || 'Unknown'}</span>
                              <span>{new Date(item.updated_at).toLocaleDateString()}</span>
                              <span>{item.view_count} views</span>
                            </div>
                            {(item.categories || item.tags) && (
                              <div className="flex items-center space-x-2 mt-2">
                                {item.categories?.map((cat, idx) => (
                                  <span
                                    key={idx}
                                    className="px-2 py-1 bg-blue-600 text-xs rounded-full"
                                  >
                                    {cat}
                                  </span>
                                ))}
                                {item.tags?.map((tag, idx) => (
                                  <span key={idx} className="px-2 py-1 bg-gray-600 text-xs rounded-full">
                                    #{tag}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(`/${item.slug}`, '_blank')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditContent(item)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteContent(item.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <div className="flex items-center justify-center space-x-2 mt-6">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                          disabled={currentPage === 1}
                        >
                          Previous
                        </Button>
                        <span className="text-sm text-gray-400">
                          Page {currentPage} of {totalPages}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                          disabled={currentPage === totalPages}
                        >
                          Next
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                    <p className="text-gray-400">No content found</p>
                    <Button onClick={handleCreateContent} className="mt-4">
                      Create Your First Content
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Other tabs would be implemented similarly */}
        {activeTab === 'media' && (
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-8 text-center">
              <Image className="h-12 w-12 text-gray-500 mx-auto mb-4" aria-label="Media management placeholder" />
              <p className="text-gray-400">Media management coming soon</p>
            </CardContent>
          </Card>
        )}

        {activeTab === 'categories' && (
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-8 text-center">
              <Folder className="h-12 w-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400">Category management coming soon</p>
            </CardContent>
          </Card>
        )}

        {activeTab === 'settings' && (
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-8 text-center">
              <Settings className="h-12 w-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400">CMS settings coming soon</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Content Editor Modal */}
      {showEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-700">
              <h2 className="text-xl font-bold">
                {selectedContent ? 'Edit Content' : 'Create New Content'}
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowEditor(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="p-4 overflow-y-auto max-h-[calc(90vh-80px)]">
              <CMSContentEditor
                content={selectedContent}
                onSave={(savedContent) => {
                  setShowEditor(false);
                  loadDashboardData();
                }}
                onCancel={() => setShowEditor(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
