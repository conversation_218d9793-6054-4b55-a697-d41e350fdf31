'use client'

import { useState, useEffect } from 'react'
import { Button } from './ui/button'
import { <PERSON>, <PERSON><PERSON>, Setting<PERSON> } from 'lucide-react'
import { useLanguage } from './language-provider'
import Cookies from 'js-cookie'

export function CookieConsent() {
  const { t } = useLanguage()
  const [showConsent, setShowConsent] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [preferences, setPreferences] = useState({
    necessary: true,
    analytics: false,
    marketing: false,
    functional: false,
  })

  useEffect(() => {
    const consent = Cookies.get('cookie-consent')
    if (!consent) {
      setShowConsent(true)
    }
  }, [])

  const acceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true,
    }
    setPreferences(allAccepted)
    Cookies.set('cookie-consent', JSON.stringify(allAccepted), { expires: 365 })
    setShowConsent(false)
  }

  const acceptSelected = () => {
    Cookies.set('cookie-consent', JSON.stringify(preferences), { expires: 365 })
    setShowConsent(false)
  }

  const rejectAll = () => {
    const onlyNecessary = {
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false,
    }
    setPreferences(onlyNecessary)
    Cookies.set('cookie-consent', JSON.stringify(onlyNecessary), { expires: 365 })
    setShowConsent(false)
  }

  if (!showConsent) return null

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-end justify-center p-4">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Cookie className="h-6 w-6 text-blue-600" />
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                {t('cookies.title')}
              </h2>
            </div>
            <button
              onClick={() => setShowConsent(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <p className="text-gray-600 dark:text-gray-300 mb-6">
            {t('cookies.description')} {' '}
            By clicking "{t('cookies.acceptall')}", you consent to our use of cookies in accordance with our Privacy Policy
            and GDPR compliance standards.
          </p>

          {showSettings && (
            <div className="space-y-4 mb-6 border-t border-gray-200 dark:border-gray-700 pt-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">Necessary Cookies</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Required for basic site functionality and security.
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.necessary}
                  disabled
                  className="rounded border-gray-300"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">Analytics Cookies</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Help us understand how visitors interact with our website.
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.analytics}
                  onChange={(e) => setPreferences(prev => ({ ...prev, analytics: e.target.checked }))}
                  className="rounded border-gray-300"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">Functional Cookies</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Enable enhanced functionality and personalization.
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.functional}
                  onChange={(e) => setPreferences(prev => ({ ...prev, functional: e.target.checked }))}
                  className="rounded border-gray-300"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">Marketing Cookies</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Used to deliver relevant advertisements and track ad performance.
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.marketing}
                  onChange={(e) => setPreferences(prev => ({ ...prev, marketing: e.target.checked }))}
                  className="rounded border-gray-300"
                />
              </div>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-3">
            <Button onClick={acceptAll} className="flex-1">
              Accept All
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setShowSettings(!showSettings)}
              className="flex-1"
            >
              <Settings className="h-4 w-4 mr-2" />
              {showSettings ? 'Hide Settings' : 'Customize'}
            </Button>
            {showSettings && (
              <Button variant="outline" onClick={acceptSelected} className="flex-1">
                Accept Selected
              </Button>
            )}
            <Button variant="ghost" onClick={rejectAll} className="flex-1">
              Reject All
            </Button>
          </div>

          <div className="mt-4 text-xs text-gray-500 dark:text-gray-400 text-center">
            Learn more in our{' '}
            <a href="/privacy" className="text-blue-600 hover:underline">
              Privacy Policy
            </a>{' '}
            and{' '}
            <a href="/cookies" className="text-blue-600 hover:underline">
              Cookie Policy
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
