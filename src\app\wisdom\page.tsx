import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>bul<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "lucide-react";

const wisdomTraditions = [
  {
    id: 1,
    tradition: "Ancient Greek Philosophy",
    principle: "Know Thyself",
    originalText: "γνῶθι σεαυτόν (gnothi seauton)",
    translation: "The unexamined life is not worth living.",
    explanation:
      "Self-knowledge is the foundation of all wisdom. Understanding your own thoughts, emotions, motivations, and limitations enables better decision-making and personal growth.",
    modernApplication:
      "Regular self-reflection, mindfulness practices, and honest self-assessment lead to improved emotional intelligence and life satisfaction.",
    benefits: [
      "Better decision-making",
      "Increased self-awareness",
      "Improved relationships",
      "Personal growth",
    ],

    icon: Brain,
    color: "text-blue-600",
  },
  {
    id: 2,
    tradition: "Eastern Philosophy",
    principle: "Balance and Harmony",
    originalText: "陰陽 (<PERSON>)",
    translation: "In balance, there is harmony; in harmony, there is peace.",
    explanation:
      "Life consists of complementary opposites that must be balanced. Understanding this principle helps navigate challenges and maintain equilibrium in all aspects of life.",
    modernApplication:
      "Work-life balance, emotional regulation, and understanding that difficulties often lead to growth and strength.",
    benefits: [
      "Reduced stress",
      "Better adaptability",
      "Emotional stability",
      "Holistic perspective",
    ],

    icon: Sun,
    color: "text-yellow-600",
  },
  {
    id: 3,
    tradition: "Stoic Philosophy",
    principle: "Focus on What You Control",
    originalText:
      'Epictetus: "Some things are within our power, while others are not."',
    translation:
      "Distinguish between what is within your control and what is not.",
    explanation:
      "Mental energy should be focused on things you can influence - your thoughts, actions, and responses - rather than external circumstances beyond your control.",
    modernApplication:
      "Stress management, productivity improvement, and developing resilience in the face of challenges.",
    benefits: [
      "Reduced anxiety",
      "Increased effectiveness",
      "Better focus",
      "Enhanced resilience",
    ],

    icon: Lightbulb,
    color: "text-purple-600",
  },
  {
    id: 4,
    tradition: "Universal Wisdom",
    principle: "Continuous Learning",
    originalText: 'Socrates: "I know that I know nothing."',
    translation: "The wise person never stops learning and growing.",
    explanation:
      "True wisdom comes from recognizing the vastness of what we don't know and maintaining curiosity and openness to new knowledge throughout life.",
    modernApplication:
      "Lifelong learning, intellectual humility, and staying curious about the world and other perspectives.",
    benefits: [
      "Intellectual growth",
      "Adaptability",
      "Innovation",
      "Wisdom accumulation",
    ],

    icon: Star,
    color: "text-indigo-600",
  },
  {
    id: 5,
    tradition: "Natural Philosophy",
    principle: "Observe and Learn from Nature",
    originalText: 'Aristotle: "Nature does nothing in vain."',
    translation:
      "Nature is the greatest teacher of efficiency, balance, and wisdom.",
    explanation:
      "Natural systems demonstrate optimal solutions developed over millions of years. Observing nature provides insights into efficiency, sustainability, and harmony.",
    modernApplication:
      "Biomimicry in technology, sustainable living practices, and understanding natural cycles and rhythms.",
    benefits: [
      "Sustainable solutions",
      "Efficiency insights",
      "Environmental awareness",
      "Natural harmony",
    ],

    icon: Heart,
    color: "text-green-600",
  },
  {
    id: 6,
    tradition: "Practical Wisdom",
    principle: "Action with Intention",
    originalText:
      'Aristotle: "We are what we repeatedly do. Excellence is not an act, but a habit."',
    translation:
      "Wisdom without action is incomplete; action without wisdom is dangerous.",
    explanation:
      "True wisdom manifests through thoughtful action. Knowledge must be applied with good intention and careful consideration of consequences.",
    modernApplication:
      "Mindful decision-making, ethical behavior, and aligning actions with values and long-term goals.",
    benefits: [
      "Meaningful progress",
      "Ethical living",
      "Goal achievement",
      "Character development",
    ],

    icon: Zap,
    color: "text-orange-600",
  },
];

const practicalApplications = [
  {
    area: "Decision Making",
    principles: [
      "Gather relevant information",
      "Consider multiple perspectives",
      "Evaluate long-term consequences",
      "Align with core values",
    ],

    description:
      "Apply wisdom principles to make better choices in personal and professional life.",
  },
  {
    area: "Relationships",
    principles: [
      "Practice empathy",
      "Communicate clearly",
      "Respect differences",
      "Seek understanding",
    ],

    description:
      "Use wisdom to build stronger, more meaningful connections with others.",
  },
  {
    area: "Personal Growth",
    principles: [
      "Regular self-reflection",
      "Embrace challenges",
      "Learn from mistakes",
      "Maintain curiosity",
    ],

    description:
      "Apply timeless principles for continuous personal development and self-improvement.",
  },
  {
    area: "Problem Solving",
    principles: [
      "Define the problem clearly",
      "Consider root causes",
      "Generate multiple solutions",
      "Test and adapt",
    ],

    description:
      "Use logical and intuitive wisdom to address challenges effectively.",
  },
];

export default function WisdomPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/20 dark:via-indigo-900/20 dark:to-purple-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <BookOpen className="h-16 w-16 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Ancient Wisdom & Universal Principles
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Explore timeless wisdom from various traditions and cultures.
              Discover universal principles that guide intelligent living,
              ethical decision-making, and personal growth.
            </p>
          </div>
        </div>
      </div>

      {/* Introduction Quote */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="verse-container text-center">
          <div className="text-2xl md:text-3xl mb-4 text-gray-800 dark:text-gray-200 font-serif italic">
            "Wisdom is not a product of schooling but of the lifelong attempt to
            acquire it."
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-2">
            True wisdom transcends cultural boundaries and speaks to universal
            human experiences and needs.
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Albert Einstein
          </p>
        </div>
      </div>

      {/* Educational Disclaimer */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2">
            Educational & Reflection Disclaimer
          </h3>
          <p className="text-blue-700 dark:text-blue-300 text-sm mb-3">
            All wisdom traditions and philosophical content presented here are
            for educational purposes and peaceful reflection. These materials
            are designed to promote inner peace, understanding, and personal
            growth through contemplation of universal principles and timeless
            wisdom.
          </p>
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            <strong>Copyright Notice:</strong> Ancient wisdom quotes and
            philosophical principles are from public domain sources. All
            interpretations, explanations, and educational content are original
            works created for peaceful learning and spiritual development. This
            content encourages beneficial knowledge acquisition and character
            development.
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* Awe-Inspiring Development Facts */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Amazing Facts About Wisdom & Intellectual Development
          </h2>
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-8 border border-blue-200 dark:border-blue-800">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🧠</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">
                  Universal Wisdom Across Cultures
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Throughout history, different cultures have discovered the
                  same fundamental wisdom principles. This universal convergence
                  shows that certain truths about life, growth, and happiness
                  are built into the fabric of human experience and divine
                  design.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💎</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">
                  Heart Confirmation Through Wisdom
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  When you encounter true wisdom, your heart immediately
                  recognizes its truth and value. This inner knowing guides you
                  toward beneficial knowledge and helps you distinguish between
                  genuine wisdom and mere information or opinion.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌟</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">
                  Intellectual Living Through Ancient Wisdom
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Ancient wisdom develops your intelligence by teaching you to
                  think deeply, question assumptions, and see patterns across
                  different areas of life. This intellectual approach helps you
                  make better decisions and understand life more clearly.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚖️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">
                  Perfect Balance in Wisdom Traditions
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  True wisdom always emphasizes balance - between thinking and
                  feeling, action and reflection, individual growth and service
                  to others. This balanced approach reflects the harmony found
                  throughout creation and leads to sustainable growth.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌱</span>
                  </div>
                </div>
                <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">
                  Path to Inner Growth & Character Development
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Each wisdom principle you learn and apply becomes a step on
                  your path to inner growth and character development. As you
                  integrate ancient wisdom, you develop patience, understanding,
                  compassion, and strength that benefit every area of your life.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">✨</span>
                  </div>
                </div>
                <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">
                  Gateway to Timeless Understanding
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Ancient wisdom opens doorways to timeless understanding that
                  transcends cultural and historical boundaries. Through
                  studying these principles, you connect with the deepest
                  insights about human nature, purpose, and the path to
                  fulfillment.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Wisdom Traditions */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Timeless Wisdom Principles
          </h2>
          <div className="space-y-8">
            {wisdomTraditions.map((wisdom) => {
              const Icon = wisdom.icon;
              return (
                <div
                  key={wisdom.id}
                  className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden"
                >
                  <div className="p-8">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        <Icon className={`h-8 w-8 ${wisdom.color}`} />

                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                            {wisdom.principle}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {wisdom.tradition}
                          </p>
                        </div>
                      </div>
                      <Star className="h-6 w-6 text-yellow-500" />
                    </div>

                    {/* Original Text */}
                    <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">
                        {wisdom.originalText}
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 italic">
                        "{wisdom.translation}"
                      </p>
                    </div>

                    {/* Explanation */}
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Understanding the Principle
                      </h4>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        {wisdom.explanation}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Modern Application */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Modern Application
                        </h4>
                        <p className="text-gray-700 dark:text-gray-300">
                          {wisdom.modernApplication}
                        </p>
                      </div>

                      {/* Benefits */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Key Benefits
                        </h4>
                        <div className="space-y-2">
                          {wisdom.benefits.map((benefit, index) => (
                            <div key={index} className="flex items-center">
                              <Heart className="h-4 w-4 text-red-500 mr-2" />

                              <span className="text-gray-700 dark:text-gray-300">
                                {benefit}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Practical Applications */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Practical Applications
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {practicalApplications.map((application, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6"
              >
                <div className="flex items-center mb-4">
                  <Moon className="h-6 w-6 text-indigo-600 mr-3" />

                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {application.area}
                  </h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {application.description}
                </p>
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-2">
                    Key Principles:
                  </h4>
                  <ul className="space-y-1">
                    {application.principles.map((principle, principleIndex) => (
                      <li
                        key={principleIndex}
                        className="flex items-center text-sm text-gray-700 dark:text-gray-300"
                      >
                        <Lightbulb className="h-3 w-3 text-yellow-500 mr-2" />

                        {principle}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Integration Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Integrating Wisdom into Daily Life
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <Brain className="h-12 w-12 text-blue-600 mx-auto mb-4" />

                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Study & Reflect
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Regular study of wisdom traditions and personal reflection on
                  their applications.
                </p>
              </div>
              <div className="text-center">
                <Heart className="h-12 w-12 text-red-600 mx-auto mb-4" />

                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Practice & Apply
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Consistent application of wisdom principles in daily decisions
                  and interactions.
                </p>
              </div>
              <div className="text-center">
                <Star className="h-12 w-12 text-purple-600 mx-auto mb-4" />

                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Share & Teach
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Sharing wisdom with others deepens understanding and creates
                  positive impact.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Begin Your Wisdom Journey
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Wisdom is not just knowledge, but the intelligent application of
              knowledge for the benefit of yourself and others. Start with one
              principle and gradually integrate more into your life.
            </p>
            <div className="flex justify-center space-x-8">
              <div className="text-center">
                <BookOpen className="h-8 w-8 text-blue-600 mx-auto mb-2" />

                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Study Traditions
                </p>
              </div>
              <div className="text-center">
                <Lightbulb className="h-8 w-8 text-yellow-600 mx-auto mb-2" />

                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Apply Principles
                </p>
              </div>
              <div className="text-center">
                <Heart className="h-8 w-8 text-red-600 mx-auto mb-2" />

                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Live Wisely
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
