import {
  Heart,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>s,
  Zap,
  Star,
  Users,
  Calendar,
  CheckCircle,
} from "lucide-react";

const therapyTypes = [
  {
    name: "Cupping Therapy",
    description:
      "Traditional therapeutic suction technique for pain relief and circulation improvement",
    benefits: [
      "Pain reduction",
      "Improved circulation",
      "Muscle relaxation",
      "Stress relief",
    ],

    duration: "30-60 minutes",
    frequency: "1-2 times per week",
    suitableFor: [
      "Chronic pain",
      "Muscle tension",
      "Sports injuries",
      "Stress-related conditions",
    ],

    icon: Heart,
    color: "text-red-600",
  },
  {
    name: "Mind-Body Integration Therapy",
    description:
      "Combining emotional intelligence with logical thinking for mental wellness",
    benefits: [
      "Emotional balance",
      "Stress management",
      "Better decision-making",
      "Mental clarity",
    ],

    duration: "45-90 minutes",
    frequency: "1 time per week",
    suitableFor: ["Anxiety", "Depression", "Stress", "Life transitions"],
    icon: Brain,
    color: "text-blue-600",
  },
  {
    name: "Plant Medicine Therapy",
    description:
      "Evidence-based use of medicinal plants for healing and wellness",
    benefits: [
      "Natural healing",
      "Reduced side effects",
      "Holistic wellness",
      "Immune support",
    ],

    duration: "Ongoing",
    frequency: "As prescribed",
    suitableFor: [
      "Digestive issues",
      "Immune disorders",
      "Chronic conditions",
      "Preventive care",
    ],

    icon: Leaf,
    color: "text-green-600",
  },
  {
    name: "Light & Energy Therapy",
    description:
      "Using natural light and energy principles for healing and vitality",
    benefits: [
      "Improved mood",
      "Better sleep",
      "Increased energy",
      "Seasonal balance",
    ],

    duration: "20-45 minutes",
    frequency: "3-5 times per week",
    suitableFor: [
      "Seasonal depression",
      "Sleep disorders",
      "Low energy",
      "Mood disorders",
    ],

    icon: Sun,
    color: "text-yellow-600",
  },
  {
    name: "Water & Cleansing Therapy",
    description: "Therapeutic use of water for detoxification and healing",
    benefits: [
      "Detoxification",
      "Improved circulation",
      "Stress relief",
      "Skin health",
    ],

    duration: "30-60 minutes",
    frequency: "2-3 times per week",
    suitableFor: [
      "Toxin buildup",
      "Circulation issues",
      "Skin conditions",
      "Stress",
    ],

    icon: Waves,
    color: "text-cyan-600",
  },
  {
    name: "Energy Balancing Therapy",
    description:
      "Restoring natural energy flow for optimal health and vitality",
    benefits: [
      "Energy balance",
      "Improved vitality",
      "Emotional stability",
      "Physical wellness",
    ],

    duration: "45-75 minutes",
    frequency: "1-2 times per week",
    suitableFor: [
      "Fatigue",
      "Energy imbalances",
      "Chronic illness",
      "Recovery",
    ],

    icon: Zap,
    color: "text-purple-600",
  },
];

const treatmentProcess = [
  {
    step: 1,
    title: "Initial Consultation",
    description:
      "Comprehensive assessment of your health goals, current condition, and treatment preferences",
    duration: "60-90 minutes",
    includes: [
      "Health history review",
      "Goal setting",
      "Treatment planning",
      "Education",
    ],
  },
  {
    step: 2,
    title: "Personalized Treatment Plan",
    description:
      "Custom therapy combination based on your specific needs and preferences",
    duration: "1-2 days",
    includes: [
      "Therapy selection",
      "Schedule planning",
      "Resource preparation",
      "Support setup",
    ],
  },
  {
    step: 3,
    title: "Active Treatment Phase",
    description:
      "Implementation of chosen therapies with regular monitoring and adjustments",
    duration: "4-12 weeks",
    includes: [
      "Regular sessions",
      "Progress monitoring",
      "Plan adjustments",
      "Support guidance",
    ],
  },
  {
    step: 4,
    title: "Integration & Maintenance",
    description:
      "Long-term wellness strategies and continued support for sustained benefits",
    duration: "Ongoing",
    includes: [
      "Maintenance sessions",
      "Lifestyle integration",
      "Continued education",
      "Community support",
    ],
  },
];

const therapeuticPrinciples = [
  {
    principle: "Holistic Approach",
    description:
      "Treating the whole person - body, mind, and spirit - not just symptoms",
    application:
      "Combining multiple therapeutic modalities for comprehensive healing",
  },
  {
    principle: "Natural Healing",
    description:
      "Supporting the body's innate ability to heal itself through natural methods",
    application:
      "Using plant medicines, natural therapies, and lifestyle modifications",
  },
  {
    principle: "Evidence-Based Practice",
    description:
      "Combining traditional wisdom with modern scientific understanding",
    application:
      "Researching and validating therapeutic approaches for safety and efficacy",
  },
  {
    principle: "Individualized Care",
    description:
      "Tailoring treatments to each person's unique needs and circumstances",
    application: "Customizing therapy combinations, schedules, and approaches",
  },
  {
    principle: "Preventive Focus",
    description:
      "Emphasizing prevention and wellness maintenance over crisis intervention",
    application:
      "Teaching lifestyle practices and early intervention strategies",
  },
  {
    principle: "Empowerment",
    description:
      "Educating and empowering individuals to take active roles in their healing",
    application:
      "Providing education, tools, and support for self-care and wellness",
  },
];

export default function TherapyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-green-50 to-purple-50 dark:from-blue-900/20 dark:via-green-900/20 dark:to-purple-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Heart className="h-16 w-16 text-red-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Therapeutic Healing Approaches
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Discover evidence-based therapeutic approaches that combine
              ancient wisdom with modern science. Our holistic therapies focus
              on natural healing, intelligence-based treatment, and sustainable
              wellness.
            </p>
          </div>
        </div>
      </div>

      {/* Medical Disclaimer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-800 dark:text-red-300 mb-2">
            Important Medical Disclaimer
          </h3>
          <p className="text-red-700 dark:text-red-300 text-sm mb-3">
            Therapeutic information is provided for educational purposes only
            and designed to promote inner peace and understanding of natural
            healing approaches. These approaches should complement, not replace,
            conventional medical care. Always consult qualified healthcare
            professionals before starting any therapeutic program, especially if
            you have medical conditions or take medications.
          </p>
          <p className="text-red-700 dark:text-red-300 text-sm">
            <strong>Copyright Notice:</strong> All therapeutic content,
            explanations, and educational materials are original works created
            for peaceful learning and wellness awareness. This content is
            designed to inspire understanding and promote beneficial knowledge
            acquisition for personal growth and health consciousness.
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* Awe-Inspiring Development Facts */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Amazing Facts About Therapeutic Healing & Human Development
          </h2>
          <div className="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-lg p-8 border border-blue-200 dark:border-blue-800">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌟</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">
                  Divine Healing Capacity
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Your body contains an amazing healing system designed with
                  perfect wisdom. Every cell knows exactly how to repair and
                  renew itself when supported with natural therapies. This
                  divine design shows the incredible intelligence built into
                  creation for health and recovery.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💎</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">
                  Heart Confirmation Through Healing
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  When you experience natural healing, your heart confirms the
                  truth of divine mercy and wisdom. This inner knowing brings
                  deep gratitude and understanding of how perfectly everything
                  is designed for your benefit, healing, and well-being.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🧠</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">
                  Intellectual Living Through Therapeutic Wisdom
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Understanding therapeutic approaches develops your
                  intelligence about health and healing. You learn to think
                  logically about wellness, make wise treatment choices, and
                  live in alignment with natural laws that support optimal
                  health and vitality.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚖️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">
                  Perfect Balance & Holistic Alignment
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Natural therapies bring your body, mind, and spirit into
                  perfect alignment with divine harmony. This holistic balance
                  reflects the divine order found throughout creation. When you
                  heal naturally, you experience the peace that comes from
                  living according to divine wisdom.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌱</span>
                  </div>
                </div>
                <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">
                  Path to Inner Growth & Healing
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Each healing experience becomes a step on your path to inner
                  growth and understanding. As your body heals, your heart opens
                  to deeper wisdom, your mind becomes clearer, and your spirit
                  grows in gratitude for the divine source of all healing and
                  wellness.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">✨</span>
                  </div>
                </div>
                <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">
                  Gateway to Therapeutic Enlightenment
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Natural therapies open doorways to enlightenment by showing
                  you the perfect wisdom in healing. Each moment of recovery
                  becomes a moment of recognition - seeing the divine hand in
                  every process of renewal, growth, and restoration in your
                  life.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Therapy Types */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Available Therapeutic Approaches
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {therapyTypes.map((therapy, index) => {
              const Icon = therapy.icon;
              return (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6"
                >
                  <div className="flex items-center mb-4">
                    <Icon className={`h-8 w-8 ${therapy.color} mr-3`} />

                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                      {therapy.name}
                    </h3>
                  </div>

                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    {therapy.description}
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                        Session Details:
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Duration: {therapy.duration}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Frequency: {therapy.frequency}
                      </p>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                        Key Benefits:
                      </h4>
                      <ul className="space-y-1">
                        {therapy.benefits
                          .slice(0, 3)
                          .map((benefit, benefitIndex) => (
                            <li
                              key={benefitIndex}
                              className="text-sm text-gray-600 dark:text-gray-300"
                            >
                              • {benefit}
                            </li>
                          ))}
                      </ul>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                      Suitable For:
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {therapy.suitableFor.map((condition, conditionIndex) => (
                        <span
                          key={conditionIndex}
                          className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs"
                        >
                          {condition}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Treatment Process */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Treatment Process
          </h2>
          <div className="space-y-6">
            {treatmentProcess.map((process, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6"
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold mr-6">
                    {process.step}
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {process.title}
                      </h3>
                      <span className="text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded">
                        {process.duration}
                      </span>
                    </div>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      {process.description}
                    </p>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                        Includes:
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {process.includes.map((item, itemIndex) => (
                          <div key={itemIndex} className="flex items-center">
                            <CheckCircle className="h-4 w-4 text-green-600 mr-2" />

                            <span className="text-sm text-gray-700 dark:text-gray-300">
                              {item}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Therapeutic Principles */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Our Therapeutic Principles
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {therapeuticPrinciples.map((principle, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {principle.principle}
                </h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4 text-sm">
                  {principle.description}
                </p>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded">
                  <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-1 text-sm">
                    Application:
                  </h4>
                  <p className="text-blue-700 dark:text-blue-300 text-xs">
                    {principle.application}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Integration Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Integrated Healing Approach
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <Brain className="h-12 w-12 text-blue-600 mx-auto mb-4" />

                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Mind
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Cognitive therapies, emotional intelligence, and mental
                  clarity practices.
                </p>
              </div>
              <div className="text-center">
                <Heart className="h-12 w-12 text-red-600 mx-auto mb-4" />

                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Body
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Physical therapies, natural healing, and wellness
                  optimization.
                </p>
              </div>
              <div className="text-center">
                <Star className="h-12 w-12 text-yellow-600 mx-auto mb-4" />

                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Spirit
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Wisdom practices, purpose alignment, and spiritual growth.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Begin Your Healing Journey
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Experience the power of integrated therapeutic approaches that
              honor both ancient wisdom and modern science. Start your journey
              toward optimal health and well-being.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                Schedule Consultation
              </button>
              <button className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                Learn More
              </button>
              <button className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                Join Community
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
