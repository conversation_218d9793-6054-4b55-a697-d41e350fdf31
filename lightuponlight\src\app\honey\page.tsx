import { Droplets, Shield, Zap, Heart, Brain, <PERSON>, Leaf, Star } from 'lucide-react'

const naturalFoods = [
  {
    name: 'Raw Honey',
    scientificBasis: 'Contains over 200 compounds including enzymes, antioxidants, and antimicrobial agents',
    benefits: ['Natural antimicrobial', 'Wound healing', 'Antioxidant properties', 'Energy source', 'Digestive support'],
    activeCompounds: ['Hydrogen peroxide', 'Flavonoids', 'Phenolic acids', 'Enzymes'],
    logic: 'Honey\'s low pH and hydrogen peroxide production create an environment hostile to harmful bacteria while promoting healing.',
    uses: ['Natural sweetener', 'Wound dressing', 'Cough suppressant', 'Energy boost'],
    icon: Droplets,
    color: 'text-amber-600'
  },
  {
    name: 'Onions',
    scientificBasis: 'Rich in sulfur compounds, quercetin, and organosulfur molecules with proven health benefits',
    benefits: ['Anti-inflammatory', 'Cardiovascular support', 'Immune boost', 'Antioxidant', 'Antimicrobial'],
    activeCompounds: ['Quercetin', 'Allicin', 'Sulfur compounds', 'Vitamin C'],
    logic: 'Sulfur compounds in onions support detoxification pathways and provide cardiovascular protection through multiple mechanisms.',
    uses: ['Culinary ingredient', 'Natural decongestant', 'Topical antiseptic', 'Digestive aid'],
    icon: Leaf,
    color: 'text-purple-600'
  },
  {
    name: 'Garlic',
    scientificBasis: 'Contains allicin and other organosulfur compounds with extensive research backing',
    benefits: ['Cardiovascular health', 'Immune support', 'Antimicrobial', 'Anti-inflammatory', 'Cholesterol management'],
    activeCompounds: ['Allicin', 'Ajoene', 'Diallyl sulfide', 'S-allyl cysteine'],
    logic: 'Allicin formation when garlic is crushed creates potent antimicrobial and cardiovascular protective effects.',
    uses: ['Cooking ingredient', 'Natural antibiotic', 'Cardiovascular supplement', 'Immune booster'],
    icon: Shield,
    color: 'text-green-600'
  },
  {
    name: 'Ginger',
    scientificBasis: 'Gingerol compounds provide anti-inflammatory and digestive benefits through multiple pathways',
    benefits: ['Digestive aid', 'Anti-nausea', 'Anti-inflammatory', 'Pain relief', 'Circulation boost'],
    activeCompounds: ['Gingerol', 'Shogaol', 'Zingerone', 'Paradol'],
    logic: 'Gingerol inhibits inflammatory pathways while stimulating digestive enzymes and gastric motility.',
    uses: ['Tea preparation', 'Cooking spice', 'Motion sickness remedy', 'Digestive supplement'],
    icon: Zap,
    color: 'text-orange-600'
  },
  {
    name: 'Black Seed (Nigella)',
    scientificBasis: 'Thymoquinone and other compounds provide wide-ranging therapeutic effects',
    benefits: ['Immune support', 'Anti-inflammatory', 'Antioxidant', 'Respiratory health', 'Metabolic support'],
    activeCompounds: ['Thymoquinone', 'Thymohydroquinone', 'Thymol', 'Essential oils'],
    logic: 'Thymoquinone modulates immune responses and provides cellular protection through antioxidant mechanisms.',
    uses: ['Oil supplement', 'Seed consumption', 'Topical application', 'Respiratory support'],
    icon: Star,
    color: 'text-black'
  },
  {
    name: 'Olive Oil',
    scientificBasis: 'Extra virgin olive oil contains polyphenols and monounsaturated fats with proven health benefits',
    benefits: ['Heart health', 'Anti-inflammatory', 'Brain protection', 'Antioxidant', 'Skin health'],
    activeCompounds: ['Oleic acid', 'Polyphenols', 'Vitamin E', 'Squalene'],
    logic: 'Monounsaturated fats and polyphenols work synergistically to reduce inflammation and protect against oxidative stress.',
    uses: ['Cooking oil', 'Salad dressing', 'Skin moisturizer', 'Supplement'],
    icon: Sun,
    color: 'text-yellow-600'
  }
]

const nutritionalWisdom = [
  {
    principle: 'Synergistic Effects',
    explanation: 'Natural foods contain multiple compounds that work together more effectively than isolated nutrients.',
    example: 'Honey\'s antimicrobial effect comes from the combination of hydrogen peroxide, low pH, and various enzymes.'
  },
  {
    principle: 'Bioavailability',
    explanation: 'Natural forms of nutrients are often better absorbed and utilized by the body than synthetic versions.',
    example: 'Vitamin C from natural sources comes with bioflavonoids that enhance absorption and effectiveness.'
  },
  {
    principle: 'Evolutionary Adaptation',
    explanation: 'Humans have evolved alongside these foods, developing optimal mechanisms to process their nutrients.',
    example: 'Our digestive systems are perfectly adapted to break down and utilize the complex sugars in raw honey.'
  },
  {
    principle: 'Minimal Processing',
    explanation: 'The closer a food is to its natural state, the more beneficial compounds it retains.',
    example: 'Raw honey contains live enzymes and beneficial bacteria that are destroyed by pasteurization.'
  }
]

export default function HoneyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 dark:from-amber-900/20 dark:via-yellow-900/20 dark:to-orange-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Droplets className="h-16 w-16 text-amber-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Honey & Natural Foods
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Discover the science behind nature's most powerful foods. Learn how honey, onions, 
              and other natural ingredients provide intelligent nutrition and healing properties.
            </p>
          </div>
        </div>
      </div>

      {/* Health Disclaimer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-amber-800 dark:text-amber-300 mb-2">
            Nutritional Information Disclaimer
          </h3>
          <p className="text-amber-700 dark:text-amber-300 text-sm">
            Nutritional and health information about natural foods is provided for educational purposes only.
            Individual responses may vary. This information is not intended to replace professional dietary advice.
            Consult healthcare providers for specific health conditions or dietary restrictions.
          </p>
        </div>
      </div>

      {/* Wisdom Quote */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="verse-container text-center">
          <div className="flex justify-center mb-4">
            <Brain className="h-8 w-8 text-amber-600" />
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4">
            "In honey there is healing for people - nature has encoded therapeutic wisdom
            in molecular structures that work intelligently with human biology."
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Ancient Wisdom & Modern Science
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* Nutritional Wisdom Principles */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Principles of Natural Nutrition
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {nutritionalWisdom.map((principle, index) => (
              <div key={index} className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {principle.principle}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {principle.explanation}
                </p>
                <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded">
                  <p className="text-amber-800 dark:text-amber-300 text-sm">
                    <strong>Example:</strong> {principle.example}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Natural Foods Details */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Nature's Intelligent Foods
          </h2>
          <div className="space-y-8">
            {naturalFoods.map((food, index) => {
              const Icon = food.icon
              return (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden"
                >
                  <div className="p-8">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        <Icon className={`h-8 w-8 ${food.color}`} />
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                            {food.name}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {food.scientificBasis}
                          </p>
                        </div>
                      </div>
                      <Heart className="h-6 w-6 text-red-500" />
                    </div>

                    {/* Scientific Logic */}
                    <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <h4 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2">
                        Scientific Logic
                      </h4>
                      <p className="text-blue-700 dark:text-blue-300">
                        {food.logic}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* Benefits */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Health Benefits
                        </h4>
                        <ul className="space-y-2">
                          {food.benefits.map((benefit, benefitIndex) => (
                            <li key={benefitIndex} className="flex items-center">
                              <Shield className="h-4 w-4 text-green-500 mr-2" />
                              <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Active Compounds */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Key Compounds
                        </h4>
                        <div className="space-y-2">
                          {food.activeCompounds.map((compound, compoundIndex) => (
                            <span
                              key={compoundIndex}
                              className="block px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded text-sm"
                            >
                              {compound}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Uses */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Practical Uses
                        </h4>
                        <ul className="space-y-2">
                          {food.uses.map((use, useIndex) => (
                            <li key={useIndex} className="flex items-center">
                              <Zap className="h-4 w-4 text-yellow-500 mr-2" />
                              <span className="text-gray-700 dark:text-gray-300">{use}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Quality & Sourcing */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Quality & Intelligent Sourcing
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Quality Indicators
                </h3>
                <ul className="space-y-3 text-gray-700 dark:text-gray-300">
                  <li className="flex items-start">
                    <Star className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                    <span><strong>Raw & Unprocessed:</strong> Minimal processing preserves beneficial compounds</span>
                  </li>
                  <li className="flex items-start">
                    <Leaf className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                    <span><strong>Organic Sources:</strong> Free from pesticides and chemical residues</span>
                  </li>
                  <li className="flex items-start">
                    <Sun className="h-5 w-5 text-orange-600 mr-2 mt-0.5" />
                    <span><strong>Proper Storage:</strong> Protected from light, heat, and moisture</span>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Intelligent Consumption
                </h3>
                <ul className="space-y-3 text-gray-700 dark:text-gray-300">
                  <li className="flex items-start">
                    <Brain className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                    <span><strong>Moderation:</strong> Even beneficial foods should be consumed mindfully</span>
                  </li>
                  <li className="flex items-start">
                    <Heart className="h-5 w-5 text-red-600 mr-2 mt-0.5" />
                    <span><strong>Individual Response:</strong> Pay attention to how your body responds</span>
                  </li>
                  <li className="flex items-start">
                    <Shield className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                    <span><strong>Combination Effects:</strong> Consider how foods interact with each other</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Nature's Intelligent Design
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              These natural foods represent millions of years of evolutionary wisdom, 
              providing complex nutritional profiles that work synergistically with human biology 
              for optimal health and healing.
            </p>
            <div className="flex justify-center space-x-8">
              <div className="text-center">
                <Droplets className="h-8 w-8 text-amber-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Pure & Natural</p>
              </div>
              <div className="text-center">
                <Brain className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Science-Based</p>
              </div>
              <div className="text-center">
                <Heart className="h-8 w-8 text-red-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Holistic Health</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
