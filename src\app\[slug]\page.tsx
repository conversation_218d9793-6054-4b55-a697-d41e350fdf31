import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getCachedContentBySlug, getCachedSettings } from '@/lib/cms-service';
import { CMSContentDisplay } from '@/components/cms-content-loader';
import { ScrollToTopWithProgress } from '@/components/scroll-to-top';

interface PageProps {
  params: {
    slug: string;
  };
  searchParams: {
    lang?: string;
    preview?: string;
  };
}

// Generate metadata for SEO
export async function generateMetadata({ params, searchParams }: PageProps): Promise<Metadata> {
  const language = searchParams.lang || 'en';
  const content = await getCachedContentBySlug(params.slug, language, false);
  const settings = await getCachedSettings(true);

  if (!content) {
    return {
      title: 'Page Not Found',
      description: 'The requested page could not be found.',
    };
  }

  const siteTitle = settings.site_title || 'Light Upon Light';
  const title = content.meta_title || content.title;
  const description = content.meta_description || content.excerpt || settings.site_description;
  const keywords = content.meta_keywords;

  return {
    title: `${title} | ${siteTitle}`,
    description,
    keywords,
    authors: content.author ? [{ name: content.author.full_name }] : undefined,
    openGraph: {
      title,
      description,
      type: 'article',
      publishedTime: content.published_at,
      modifiedTime: content.updated_at,
      authors: content.author ? [content.author.full_name] : undefined,
      tags: content.tags?.map(tag => tag.name),
      images: content.featured_image ? [
        {
          url: content.featured_image.file_path,
          width: content.featured_image.width,
          height: content.featured_image.height,
          alt: content.featured_image.alt_text || content.title,
        }
      ] : undefined,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: content.featured_image ? [content.featured_image.file_path] : undefined,
    },
    alternates: {
      canonical: `/${content.slug}`,
      languages: {
        'en': `/${content.slug}`,
        'sv': `/${content.slug}?lang=sv`,
      },
    },
    other: {
      'article:published_time': content.published_at || content.created_at,
      'article:modified_time': content.updated_at,
      'article:author': content.author?.full_name || '',
      'article:section': content.categories?.[0]?.name || '',
      'article:tag': content.tags?.map(tag => tag.name).join(', ') || '',
    },
  };
}

// Generate static params for static generation (optional)
export async function generateStaticParams() {
  // This would typically fetch all published content slugs
  // For now, we'll let pages be generated on-demand
  return [];
}

export default async function DynamicPage({ params, searchParams }: PageProps) {
  const language = searchParams.lang || 'en';
  const content = await getCachedContentBySlug(params.slug, language, true);

  // If content not found, show 404
  if (!content) {
    notFound();
  }

  // Check if content is published (unless in preview mode)
  if (content.status !== 'published' && !searchParams.preview) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <CMSContentDisplay
          slug={params.slug}
          language={language}
          showRelated={true}
          showComments={false}
        />
        
        {/* Enhanced scroll to top with progress */}
        <ScrollToTopWithProgress threshold={200} />
      </div>

      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Article',
            headline: content.title,
            description: content.excerpt || content.meta_description,
            image: content.featured_image ? [content.featured_image.file_path] : undefined,
            datePublished: content.published_at || content.created_at,
            dateModified: content.updated_at,
            author: {
              '@type': 'Person',
              name: content.author?.full_name || 'Unknown',
            },
            publisher: {
              '@type': 'Organization',
              name: 'Light Upon Light',
              logo: {
                '@type': 'ImageObject',
                url: '/logo.png',
              },
            },
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': `/${content.slug}`,
            },
            keywords: content.tags?.map(tag => tag.name).join(', '),
            articleSection: content.categories?.[0]?.name,
            wordCount: content.content.body?.split(' ').length || 0,
            inLanguage: language,
          }),
        }}
      />
    </div>
  );
}
