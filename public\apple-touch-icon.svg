<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with rounded corners for iOS -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="lightGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:0.7" />
    </radialGradient>
  </defs>
  
  <!-- Background with iOS rounded corners -->
  <rect width="180" height="180" rx="40" ry="40" fill="url(#bgGradient)"/>
  
  <!-- Central Light Source -->
  <circle cx="90" cy="90" r="35" fill="url(#lightGradient)"/>
  <circle cx="90" cy="90" r="25" fill="#fbbf24"/>
  <circle cx="90" cy="90" r="15" fill="#fef3c7"/>
  
  <!-- Light Rays -->
  <g stroke="#fbbf24" stroke-width="2.5" stroke-linecap="round" opacity="0.8">
    <!-- Top rays -->
    <line x1="90" y1="25" x2="90" y2="40"/>
    <line x1="125" y1="35" x2="115" y2="45"/>
    <line x1="145" y1="65" x2="135" y2="75"/>
    <line x1="155" y1="90" x2="140" y2="90"/>
    <line x1="145" y1="115" x2="135" y2="105"/>
    <line x1="125" y1="145" x2="115" y2="135"/>
    
    <!-- Bottom rays -->
    <line x1="90" y1="155" x2="90" y2="140"/>
    <line x1="55" y1="145" x2="65" y2="135"/>
    <line x1="35" y1="115" x2="45" y2="105"/>
    <line x1="25" y1="90" x2="40" y2="90"/>
    <line x1="35" y1="65" x2="45" y2="75"/>
    <line x1="55" y1="35" x2="65" y2="45"/>
  </g>
  
  <!-- Inner glow effect -->
  <circle cx="90" cy="90" r="8" fill="#fff" opacity="0.9"/>
  <circle cx="90" cy="90" r="4" fill="#fbbf24"/>
</svg>
