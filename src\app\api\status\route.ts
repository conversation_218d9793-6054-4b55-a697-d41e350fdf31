// Simple Health Check API
// Built-in status monitoring without external tools

import { NextResponse } from 'next/server'
import { createSupabaseClient } from '@/lib/supabase'

export async function GET() {
  const startTime = Date.now()
  
  try {
    // Check database connectivity
    const supabase = createSupabaseClient()
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)
      .single()

    const dbStatus = error ? 'error' : 'healthy'
    const responseTime = Date.now() - startTime

    // Check memory usage (basic)
    const memoryUsage = process.memoryUsage()
    const memoryMB = Math.round(memoryUsage.heapUsed / 1024 / 1024)

    // Overall health status
    const isHealthy = dbStatus === 'healthy' && responseTime < 5000 && memoryMB < 512

    const status = {
      status: isHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      checks: {
        database: {
          status: dbStatus,
          responseTime: `${responseTime}ms`
        },
        memory: {
          status: memoryMB < 512 ? 'healthy' : 'warning',
          usage: `${memoryMB}MB`,
          limit: '512MB'
        },
        api: {
          status: 'healthy',
          responseTime: `${responseTime}ms`
        }
      },
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    }

    return NextResponse.json(status, { 
      status: isHealthy ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })

  } catch (error) {
    console.error('Health check failed:', error)
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      checks: {
        database: { status: 'error' },
        memory: { status: 'unknown' },
        api: { status: 'error' }
      }
    }, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })
  }
}
