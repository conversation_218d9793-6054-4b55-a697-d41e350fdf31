@tailwind base;
@tailwind components;
@tailwind utilities;

/* Smooth scrolling support */
html {
  scroll-behavior: smooth;
}

/* Scroll to top button animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scroll-to-top-enter {
  animation: fadeInUp 0.3s ease-out;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode scrollbar */
@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-track {
    background: #2d3748;
  }

  ::-webkit-scrollbar-thumb {
    background: #4a5568;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #718096;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Screen Reader Only - Accessibility */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .sr-only:focus,
  .sr-only:active {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* Skip Link Styles */
  .skip-link {
    transform: translateY(-100%);
    transition: transform 0.3s;
  }

  .skip-link:focus {
    transform: translateY(0%);
  }

  /* Focus Visible Enhancement */
  .focus-visible:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  /* High Contrast Mode Support */
  @media (prefers-contrast: high) {
    .text-gray-500 {
      @apply text-gray-900 dark:text-gray-100;
    }
    .text-gray-400 {
      @apply text-gray-800 dark:text-gray-200;
    }
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Print Styles */
  @media print {
    .no-print {
      display: none !important;
    }

    body {
      background: white !important;
      color: black !important;
    }

    a[href]:after {
      content: " (" attr(href) ")";
    }

    .dark\:bg-gray-800,
    .dark\:bg-gray-900 {
      background: white !important;
    }

    .dark\:text-white,
    .dark\:text-gray-100 {
      color: black !important;
    }
  }
}

/* Screen Optimization - Responsive Design System */
:root {
  /* Default desktop values */
  --base-font-size: 16px;
  --container-padding: 32px;
  --grid-columns: 3;
  --touch-target-size: 32px;
  --border-width: 1px;
  --layout-direction: row;
  --sidebar-position: side;
  --hover-effects: enabled;
  --image-quality: standard;
}

/* Prevent horizontal scrolling */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Container width constraints */
.container {
  max-width: 100%;
  padding-left: var(--container-padding);
  padding-right: var(--container-padding);
}

/* Responsive breakpoints with screen optimization */
@media screen and (max-width: 480px) {
  :root {
    --base-font-size: 14px;
    --container-padding: 16px;
    --grid-columns: 1;
    --touch-target-size: 44px;
    --layout-direction: column;
    --sidebar-position: bottom;
  }

  /* Prevent mobile horizontal scroll */
  * {
    max-width: 100vw;
    box-sizing: border-box;
  }

  /* Mobile navigation adjustments */
  .mobile-nav {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  /* Google Translate mobile optimization */
  #google_translate_element {
    max-width: 120px;
    overflow: hidden;
  }

  #google_translate_element .goog-te-combo {
    max-width: 100px;
    font-size: 12px;
  }
}

@media screen and (min-width: 481px) and (max-width: 900px) {
  :root {
    --base-font-size: 15px;
    --container-padding: 24px;
    --grid-columns: 2;
    --touch-target-size: 40px;
  }

  /* Tablet Google Translate optimization */
  #google_translate_element {
    max-width: 140px;
  }

  #google_translate_element .goog-te-combo {
    max-width: 120px;
    font-size: 13px;
  }
}

@media screen and (min-width: 1440px) {
  :root {
    --base-font-size: 18px;
    --container-padding: 48px;
    --grid-columns: 4;
  }
}

/* High DPI screen optimization */
@media screen and (-webkit-min-device-pixel-ratio: 1.5),
       screen and (min-resolution: 144dpi) {
  :root {
    --border-width: 0.5px;
    --image-quality: high;
  }
}

/* Touch device optimization */
@media (hover: none) and (pointer: coarse) {
  :root {
    --touch-target-size: 44px;
    --hover-effects: none;
  }

  /* Remove hover effects on touch devices */
  .hover\:bg-gray-100:hover {
    background-color: inherit;
  }

  .hover\:text-blue-600:hover {
    color: inherit;
  }
}

/* Navigation responsive optimization */
.nav-container {
  width: 100%;
  max-width: 100vw;
  padding: 0 var(--container-padding);
  box-sizing: border-box;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 64px;
  flex-wrap: nowrap;
}

/* Google Translate global styling */
#google_translate_element {
  display: inline-block;
  vertical-align: top;
  max-width: 160px;
  overflow: hidden;
}

#google_translate_element .goog-te-combo {
  background-color: transparent;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  font-size: 14px;
  color: inherit;
  max-width: 100%;
}

.dark #google_translate_element .goog-te-combo {
  border-color: #4b5563;
  background-color: #374151;
  color: #f9fafb;
}

#google_translate_element .goog-te-combo:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Responsive grid system using CSS variables */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(var(--grid-columns), 1fr);
  gap: 1rem;
}

/* Touch target optimization */
.touch-target {
  min-height: var(--touch-target-size);
  min-width: var(--touch-target-size);
}

/* Islamic/Arabic styling */
.arabic-text {
  font-family: 'Amiri', 'Times New Roman', serif;
  direction: rtl;
  text-align: right;
}

.verse-container {
  @apply bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 p-6 rounded-lg border-l-4 border-blue-500;
}

.nature-gradient {
  @apply bg-gradient-to-br from-green-100 via-blue-100 to-purple-100 dark:from-green-900/30 dark:via-blue-900/30 dark:to-purple-900/30;
}
