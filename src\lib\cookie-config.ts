// Cookie Configuration for Security and Compliance
// Ensures proper cookie formatting and security settings

export interface CookieOptions {
  name: string
  value: string
  expires?: Date
  maxAge?: number
  domain?: string
  path?: string
  secure?: boolean
  httpOnly?: boolean
  sameSite?: 'strict' | 'lax' | 'none'
}

// Secure cookie defaults
export const SECURE_COOKIE_DEFAULTS = {
  secure: true, // HTTPS only
  httpOnly: true, // Prevent XSS
  sameSite: 'lax' as const, // CSRF protection
  path: '/',
}

// Cookie expiration helpers
export const COOKIE_EXPIRATION = {
  session: undefined, // Session cookie
  day: 24 * 60 * 60 * 1000, // 1 day
  week: 7 * 24 * 60 * 60 * 1000, // 1 week
  month: 30 * 24 * 60 * 60 * 1000, // 30 days
  year: 365 * 24 * 60 * 60 * 1000, // 1 year
}

// Format date for cookie expires (RFC 2822 format)
export function formatCookieDate(date: Date): string {
  return date.toUTCString()
}

// Create secure cookie string
export function createSecureCookie(options: CookieOptions): string {
  const parts: string[] = [`${options.name}=${options.value}`]
  
  if (options.expires) {
    parts.push(`Expires=${formatCookieDate(options.expires)}`)
  }
  
  if (options.maxAge) {
    parts.push(`Max-Age=${options.maxAge}`)
  }
  
  if (options.domain) {
    parts.push(`Domain=${options.domain}`)
  }
  
  if (options.path) {
    parts.push(`Path=${options.path}`)
  }
  
  if (options.secure) {
    parts.push('Secure')
  }
  
  if (options.httpOnly) {
    parts.push('HttpOnly')
  }
  
  if (options.sameSite) {
    parts.push(`SameSite=${options.sameSite}`)
  }
  
  return parts.join('; ')
}

// Predefined secure cookies
export const SECURE_COOKIES = {
  // Authentication cookie
  auth: (token: string, expires?: Date) => createSecureCookie({
    name: 'auth-token',
    value: token,
    expires: expires || new Date(Date.now() + COOKIE_EXPIRATION.week),
    ...SECURE_COOKIE_DEFAULTS,
  }),
  
  // User preferences
  preferences: (prefs: string, expires?: Date) => createSecureCookie({
    name: 'user-preferences',
    value: prefs,
    expires: expires || new Date(Date.now() + COOKIE_EXPIRATION.year),
    ...SECURE_COOKIE_DEFAULTS,
    httpOnly: false, // Allow client-side access for preferences
  }),
  
  // Language setting
  language: (lang: string, expires?: Date) => createSecureCookie({
    name: 'language',
    value: lang,
    expires: expires || new Date(Date.now() + COOKIE_EXPIRATION.year),
    ...SECURE_COOKIE_DEFAULTS,
    httpOnly: false, // Allow client-side access
  }),
  
  // Theme setting
  theme: (theme: string, expires?: Date) => createSecureCookie({
    name: 'theme',
    value: theme,
    expires: expires || new Date(Date.now() + COOKIE_EXPIRATION.year),
    ...SECURE_COOKIE_DEFAULTS,
    httpOnly: false, // Allow client-side access
  }),
  
  // GDPR consent
  gdprConsent: (consent: string, expires?: Date) => createSecureCookie({
    name: 'gdpr-consent',
    value: consent,
    expires: expires || new Date(Date.now() + COOKIE_EXPIRATION.year),
    ...SECURE_COOKIE_DEFAULTS,
    httpOnly: false, // Allow client-side access for consent management
  }),
}

// Cookie validation
export function validateCookieValue(value: string): boolean {
  // Check for valid cookie value (no control characters, semicolons, etc.)
  return !/[;\s\x00-\x1f\x7f]/.test(value)
}

// Cookie parsing helper
export function parseCookies(cookieString: string): Record<string, string> {
  const cookies: Record<string, string> = {}
  
  if (!cookieString) return cookies
  
  cookieString.split(';').forEach(cookie => {
    const [name, ...rest] = cookie.trim().split('=')
    if (name && rest.length > 0) {
      cookies[name] = rest.join('=')
    }
  })
  
  return cookies
}

// Security validation for cookie settings
export function validateCookieSecurity(options: CookieOptions): boolean {
  // Ensure secure cookies in production
  if (process.env.NODE_ENV === 'production' && !options.secure) {
    console.warn('Insecure cookie detected in production:', options.name)
    return false
  }
  
  // Validate expiration date format
  if (options.expires && isNaN(options.expires.getTime())) {
    console.error('Invalid cookie expiration date:', options.expires)
    return false
  }
  
  // Validate cookie value
  if (!validateCookieValue(options.value)) {
    console.error('Invalid cookie value:', options.value)
    return false
  }
  
  return true
}

// Helper to set cookie with proper error handling
export function setSecureCookie(options: CookieOptions): boolean {
  if (!validateCookieSecurity(options)) {
    return false
  }
  
  try {
    const cookieString = createSecureCookie(options)
    
    // In browser environment
    if (typeof document !== 'undefined') {
      document.cookie = cookieString
      return true
    }
    
    // In server environment, this would be handled by the response object
    return true
  } catch (error) {
    console.error('Failed to set cookie:', error)
    return false
  }
}
