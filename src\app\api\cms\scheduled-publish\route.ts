import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { CMSService } from '@/lib/cms-service';

// POST /api/cms/scheduled-publish - Process scheduled content for publishing
export async function POST(request: NextRequest) {
  try {
    // Verify this is an internal request or has proper authorization
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CRON_SECRET || 'your-secret-token';
    
    if (authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const supabase = createSupabaseServerClient();
    const now = new Date().toISOString();

    // Find all scheduled content that should be published now
    const { data: scheduledContent, error: fetchError } = await supabase
      .from('cms_content')
      .select('*')
      .eq('status', 'scheduled')
      .lte('scheduled_at', now);

    if (fetchError) {
      console.error('Error fetching scheduled content:', fetchError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch scheduled content' },
        { status: 500 }
      );
    }

    if (!scheduledContent || scheduledContent.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No content scheduled for publishing',
        published: 0,
      });
    }

    // Publish each piece of scheduled content
    const publishResults = [];
    for (const content of scheduledContent) {
      try {
        const { data, error } = await supabase
          .from('cms_content')
          .update({
            status: 'published',
            published_at: now,
            scheduled_at: null,
            updated_at: now,
          })
          .eq('id', content.id)
          .select()
          .single();

        if (error) {
          console.error(`Error publishing content ${content.id}:`, error);
          publishResults.push({
            id: content.id,
            title: content.title,
            success: false,
            error: error.message,
          });
        } else {
          publishResults.push({
            id: content.id,
            title: content.title,
            success: true,
            published_at: now,
          });

          // Log the publication
          console.log(`✅ Published scheduled content: ${content.title} (${content.id})`);
        }
      } catch (error) {
        console.error(`Error processing content ${content.id}:`, error);
        publishResults.push({
          id: content.id,
          title: content.title,
          success: false,
          error: 'Processing error',
        });
      }
    }

    // Clear caches after publishing
    CMSService.clearAllCaches();

    const successCount = publishResults.filter(r => r.success).length;
    const failureCount = publishResults.filter(r => !r.success).length;

    return NextResponse.json({
      success: true,
      message: `Processed ${scheduledContent.length} scheduled content items`,
      published: successCount,
      failed: failureCount,
      results: publishResults,
    });
  } catch (error) {
    console.error('Error in scheduled publish job:', error);
    return NextResponse.json(
      { success: false, error: 'Scheduled publish job failed' },
      { status: 500 }
    );
  }
}

// GET /api/cms/scheduled-publish - Get scheduled content info (for monitoring)
export async function GET(request: NextRequest) {
  try {
    // Verify this is an internal request or has proper authorization
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CRON_SECRET || 'your-secret-token';
    
    if (authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const supabase = createSupabaseServerClient();
    const now = new Date().toISOString();

    // Get scheduled content statistics
    const [
      readyToPublishResult,
      futureScheduledResult,
      recentlyPublishedResult
    ] = await Promise.all([
      // Content ready to be published now
      supabase
        .from('cms_content')
        .select('id, title, scheduled_at', { count: 'exact' })
        .eq('status', 'scheduled')
        .lte('scheduled_at', now),

      // Content scheduled for future
      supabase
        .from('cms_content')
        .select('id, title, scheduled_at', { count: 'exact' })
        .eq('status', 'scheduled')
        .gt('scheduled_at', now),

      // Recently published content (last 24 hours)
      supabase
        .from('cms_content')
        .select('id, title, published_at', { count: 'exact' })
        .eq('status', 'published')
        .gte('published_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
    ]);

    return NextResponse.json({
      success: true,
      data: {
        ready_to_publish: {
          count: readyToPublishResult.count || 0,
          items: readyToPublishResult.data || [],
        },
        future_scheduled: {
          count: futureScheduledResult.count || 0,
          items: futureScheduledResult.data || [],
        },
        recently_published: {
          count: recentlyPublishedResult.count || 0,
          items: recentlyPublishedResult.data || [],
        },
        last_check: now,
      },
    });
  } catch (error) {
    console.error('Error getting scheduled publish info:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get scheduled publish info' },
      { status: 500 }
    );
  }
}
