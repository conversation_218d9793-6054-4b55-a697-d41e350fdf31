import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';

// GET /api/cms/categories - Get categories
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const language = searchParams.get('language') || 'en';
    const includeCount = searchParams.get('include_count') === 'true';

    const supabase = createSupabaseServerClient();

    let query = supabase
      .from('cms_categories')
      .select('*')
      .eq('language', language)
      .eq('is_active', true)
      .order('sort_order');

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching categories:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch categories' },
        { status: 500 }
      );
    }

    // Build hierarchy and optionally include content count
    const categories = data || [];
    const categoryMap = new Map(categories.map(cat => [cat.id, { ...cat, children: [], content_count: 0 }]));
    const rootCategories: any[] = [];

    // Get content counts if requested
    if (includeCount) {
      const { data: contentCounts } = await supabase
        .from('cms_content_categories')
        .select(`
          category_id,
          content:cms_content!inner(status)
        `)
        .eq('content.status', 'published');

      const countMap = new Map();
      contentCounts?.forEach(item => {
        countMap.set(item.category_id, (countMap.get(item.category_id) || 0) + 1);
      });

      categories.forEach(category => {
        const cat = categoryMap.get(category.id);
        if (cat) {
          cat.content_count = countMap.get(category.id) || 0;
        }
      });
    }

    // Build hierarchy
    categories.forEach(category => {
      const cat = categoryMap.get(category.id)!;
      if (category.parent_id && categoryMap.has(category.parent_id)) {
        categoryMap.get(category.parent_id)!.children.push(cat);
      } else {
        rootCategories.push(cat);
      }
    });

    return NextResponse.json({
      success: true,
      data: rootCategories,
    });
  } catch (error) {
    console.error('Error in GET /api/cms/categories:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST /api/cms/categories - Create category (admin only)
export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServer();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || !['admin', 'moderator'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await request.json();
    
    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { success: false, error: 'Category name is required' },
        { status: 400 }
      );
    }

    // Generate slug if not provided
    if (!body.slug) {
      body.slug = body.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
    }

    // Create category
    const { data, error } = await supabase
      .from('cms_categories')
      .insert({
        ...body,
        language: body.language || 'en',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating category:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create category' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Category created successfully'
    });
  } catch (error) {
    console.error('Error in POST /api/cms/categories:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create category' },
      { status: 500 }
    );
  }
}
