-- SUPABASE BACKEND CONFIGURATION
-- Light Upon Light Platform - Production Configuration

-- =====================================================
-- 1. S<PERSON>ABASE REALTIME CONFIGURATION
-- =====================================================

-- Enable realtime for the database
ALTER DATABASE postgres SET "app.settings.realtime" = 'on';

-- Configure realtime publication with proper filters
DROP PUBLICATION IF EXISTS supabase_realtime;
CREATE PUBLICATION supabase_realtime;

-- Add tables to realtime with specific conditions
ALTER PUBLICATION supabase_realtime ADD TABLE chat_messages 
  WHERE (is_deleted = false);

ALTER PUBLICATION supabase_realtime ADD TABLE live_streams 
  WHERE (is_live = true OR is_archived = true);

ALTER PUBLICATION supabase_realtime ADD TABLE forum_posts 
  WHERE (is_deleted = false);

ALTER PUBLICATION supabase_realtime ADD TABLE forum_replies 
  WHERE (is_deleted = false);

ALTER PUBLICATION supabase_realtime ADD TABLE post_likes;

ALTER PUBLICATION supabase_realtime ADD TABLE event_registrations;

ALTER PUBLICATION supabase_realtime ADD TABLE user_achievements;

-- =====================================================
-- 2. SUPABASE AUTH CONFIGURATION
-- =====================================================

-- Configure auth settings
ALTER DATABASE postgres SET "app.settings.auth.enable_signup" = 'true';
ALTER DATABASE postgres SET "app.settings.auth.enable_manual_linking" = 'false';
ALTER DATABASE postgres SET "app.settings.auth.enable_anonymous_sign_ins" = 'false';
ALTER DATABASE postgres SET "app.settings.auth.minimum_password_length" = '8';
ALTER DATABASE postgres SET "app.settings.auth.password_requirements" = 'letters_digits';

-- =====================================================
-- 3. SUPABASE STORAGE CONFIGURATION
-- =====================================================

-- Create storage buckets for file uploads
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('avatars', 'avatars', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp']),
  ('stream-recordings', 'stream-recordings', false, 1073741824, ARRAY['video/mp4', 'video/webm']),
  ('forum-attachments', 'forum-attachments', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/gif', 'application/pdf'])
ON CONFLICT (id) DO NOTHING;

-- Storage policies for avatars
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
  FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'avatars' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "Users can update their own avatar" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'avatars' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Storage policies for stream recordings (admin only)
CREATE POLICY "Admins can view stream recordings" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'stream-recordings'
    AND EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role = 'admin'
      AND email IN ('<EMAIL>', '<EMAIL>')
    )
  );

CREATE POLICY "Admins can upload stream recordings" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'stream-recordings'
    AND EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role = 'admin'
      AND email IN ('<EMAIL>', '<EMAIL>')
    )
  );

-- Storage policies for forum attachments
CREATE POLICY "Forum attachments are publicly accessible" ON storage.objects
  FOR SELECT USING (bucket_id = 'forum-attachments');

CREATE POLICY "Authenticated users can upload forum attachments" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'forum-attachments'
    AND auth.role() = 'authenticated'
  );

-- =====================================================
-- 4. SUPABASE EDGE FUNCTIONS SETUP
-- =====================================================

-- Note: Edge functions are deployed separately, but we can prepare the database

-- Create a table to track edge function executions
CREATE TABLE IF NOT EXISTS edge_function_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  function_name TEXT NOT NULL,
  execution_time_ms INTEGER,
  status TEXT CHECK (status IN ('success', 'error', 'timeout')),
  error_message TEXT,
  request_data JSONB,
  response_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on edge function logs
ALTER TABLE edge_function_logs ENABLE ROW LEVEL SECURITY;

-- Only admins can view edge function logs
CREATE POLICY "Admins can view edge function logs" ON edge_function_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role = 'admin'
      AND email IN ('<EMAIL>', '<EMAIL>')
    )
  );

-- =====================================================
-- 5. WEBHOOK CONFIGURATION
-- =====================================================

-- Create webhook events table for external integrations
CREATE TABLE IF NOT EXISTS webhook_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT NOT NULL,
  payload JSONB NOT NULL,
  webhook_url TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'retrying')),
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  next_retry_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  sent_at TIMESTAMP WITH TIME ZONE
);

-- Enable RLS on webhook events
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;

-- Only system can manage webhook events
CREATE POLICY "System only webhook access" ON webhook_events
  FOR ALL USING (false);

-- =====================================================
-- 6. DATABASE MONITORING SETUP
-- =====================================================

-- Create monitoring table for database health
CREATE TABLE IF NOT EXISTS db_health_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name TEXT NOT NULL,
  metric_value NUMERIC,
  metric_unit TEXT,
  threshold_warning NUMERIC,
  threshold_critical NUMERIC,
  status TEXT CHECK (status IN ('healthy', 'warning', 'critical')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function to check database health
CREATE OR REPLACE FUNCTION check_database_health()
RETURNS TABLE (
  metric_name TEXT,
  current_value NUMERIC,
  status TEXT,
  message TEXT
) AS $$
BEGIN
  -- Check connection count
  RETURN QUERY
  SELECT 
    'active_connections'::TEXT,
    (SELECT count(*)::NUMERIC FROM pg_stat_activity WHERE state = 'active'),
    CASE 
      WHEN (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') > 80 THEN 'critical'
      WHEN (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') > 50 THEN 'warning'
      ELSE 'healthy'
    END,
    'Number of active database connections'::TEXT;

  -- Check database size
  RETURN QUERY
  SELECT 
    'database_size_mb'::TEXT,
    (SELECT pg_database_size(current_database())::NUMERIC / 1024 / 1024),
    CASE 
      WHEN (SELECT pg_database_size(current_database()) / 1024 / 1024) > 1000 THEN 'warning'
      WHEN (SELECT pg_database_size(current_database()) / 1024 / 1024) > 2000 THEN 'critical'
      ELSE 'healthy'
    END,
    'Database size in megabytes'::TEXT;

  -- Check table bloat
  RETURN QUERY
  SELECT 
    'table_bloat_ratio'::TEXT,
    0.0::NUMERIC, -- Simplified for now
    'healthy'::TEXT,
    'Table bloat ratio (simplified check)'::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 7. BACKUP AND RECOVERY CONFIGURATION
-- =====================================================

-- Create backup metadata table
CREATE TABLE IF NOT EXISTS backup_metadata (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  backup_type TEXT CHECK (backup_type IN ('full', 'incremental', 'schema_only')),
  backup_size_bytes BIGINT,
  backup_location TEXT,
  backup_status TEXT CHECK (backup_status IN ('in_progress', 'completed', 'failed')),
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT
);

-- Function to log backup operations
CREATE OR REPLACE FUNCTION log_backup_operation(
  backup_type TEXT,
  backup_location TEXT DEFAULT NULL,
  backup_size BIGINT DEFAULT NULL,
  status TEXT DEFAULT 'completed'
)
RETURNS UUID AS $$
DECLARE
  backup_id UUID;
BEGIN
  INSERT INTO backup_metadata (
    backup_type,
    backup_location,
    backup_size_bytes,
    backup_status,
    completed_at
  ) VALUES (
    backup_type,
    backup_location,
    backup_size,
    status,
    CASE WHEN status = 'completed' THEN NOW() ELSE NULL END
  ) RETURNING id INTO backup_id;
  
  RETURN backup_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 8. PERFORMANCE MONITORING
-- =====================================================

-- Create slow query log table
CREATE TABLE IF NOT EXISTS slow_query_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  query_text TEXT,
  execution_time_ms NUMERIC,
  rows_examined BIGINT,
  rows_returned BIGINT,
  user_id UUID REFERENCES users(id),
  database_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for performance monitoring
CREATE INDEX IF NOT EXISTS idx_slow_query_log_execution_time 
  ON slow_query_log(execution_time_ms DESC, created_at DESC);

-- Function to log slow queries (would be called by monitoring system)
CREATE OR REPLACE FUNCTION log_slow_query(
  query_text TEXT,
  execution_time_ms NUMERIC,
  rows_examined BIGINT DEFAULT NULL,
  rows_returned BIGINT DEFAULT NULL
)
RETURNS void AS $$
BEGIN
  -- Only log queries slower than 1 second
  IF execution_time_ms > 1000 THEN
    INSERT INTO slow_query_log (
      query_text,
      execution_time_ms,
      rows_examined,
      rows_returned,
      user_id,
      database_name
    ) VALUES (
      query_text,
      execution_time_ms,
      rows_examined,
      rows_returned,
      auth.uid(),
      current_database()
    );
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 9. FINAL SUPABASE OPTIMIZATIONS
-- =====================================================

-- Set optimal PostgreSQL settings for Supabase
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET track_activity_query_size = 2048;
ALTER SYSTEM SET log_min_duration_statement = 1000; -- Log queries > 1 second

-- Reload configuration
SELECT pg_reload_conf();

-- Update statistics for all tables
DO $$
DECLARE
  table_name TEXT;
BEGIN
  FOR table_name IN 
    SELECT tablename FROM pg_tables WHERE schemaname = 'public'
  LOOP
    EXECUTE 'ANALYZE ' || quote_ident(table_name);
  END LOOP;
END $$;

-- Final vacuum and analyze
VACUUM (ANALYZE, VERBOSE);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated, anon;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
