import {
  Shield,
  BookOpen,
  Heart,
  Scale,
  CheckCircle,
  Alert<PERSON>riangle,
  FileText,
  Globe,
} from "lucide-react";

const complianceAreas = [
  {
    title: "Copyright Compliance",
    description: "All content is original or from public domain sources",
    details: [
      "Original educational content created for peaceful learning",
      "Quranic verses from public domain sources",
      "Ancient wisdom quotes from public domain",
      "All interpretations and explanations are original works",
      "No copyrighted materials used without permission",
    ],

    icon: FileText,
    color: "text-blue-600",
    bgColor: "bg-blue-50 dark:bg-blue-900/20",
  },
  {
    title: "Medical Disclaimer Compliance",
    description: "Clear disclaimers for all health-related content",
    details: [
      "Educational purposes only disclaimers on all health content",
      "Clear statements that content does not replace medical advice",
      "Recommendations to consult healthcare professionals",
      "Emphasis on complementary, not replacement therapy",
      "Safety warnings for all therapeutic practices",
    ],

    icon: Shield,
    color: "text-red-600",
    bgColor: "bg-red-50 dark:bg-red-900/20",
  },
  {
    title: "Religious Content Compliance",
    description: "Respectful and educational approach to religious content",
    details: [
      "Educational and interpretive disclaimers for religious content",
      "Clear statements about perspective and interpretation",
      "Recommendations to consult qualified religious scholars",
      "Focus on universal principles and peaceful reflection",
      "Respectful presentation of Islamic teachings",
    ],

    icon: BookOpen,
    color: "text-green-600",
    bgColor: "bg-green-50 dark:bg-green-900/20",
  },
  {
    title: "GDPR & Privacy Compliance",
    description: "Full compliance with data protection regulations",
    details: [
      "Comprehensive privacy policy and GDPR compliance page",
      "Cookie consent management system with granular controls",
      "Third-party service consent (Google Translate) with explicit user approval",
      "User rights and data protection measures including data export/deletion",
      "Transparent data collection and usage policies",
      "Secure data handling and storage practices",
      "Privacy-respecting analytics with user consent",
      "Regular compliance audits and updates",
    ],

    icon: Globe,
    color: "text-purple-600",
    bgColor: "bg-purple-50 dark:bg-purple-900/20",
  },
];

const innerPeaceFocus = [
  {
    aspect: "Content Design",
    description: "All content designed to promote inner peace and reflection",
    elements: [
      "Focus on wisdom, understanding, and spiritual growth",
      "Emphasis on beneficial knowledge and character development",
      "Peaceful language and gentle guidance",
      "Encouragement of contemplation and reflection",
    ],
  },
  {
    aspect: "Educational Approach",
    description:
      "Learning materials that inspire tranquility and understanding",
    elements: [
      "Simple, accessible language for all audiences",
      "Universal principles that transcend cultural boundaries",
      "Practical applications for daily life and wellness",
      "Integration of heart and mind development",
    ],
  },
  {
    aspect: "Spiritual Development",
    description: "Content that nurtures spiritual growth and inner safety",
    elements: [
      "Guidance toward consciousness and understanding",
      "Path to straight guidance and home of peace",
      "Care for inner safety and awakening to inner life",
      "Journey toward light and spiritual enlightenment",
    ],
  },
];

export default function LegalCompliancePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-green-50 to-purple-50 dark:from-blue-900/20 dark:via-green-900/20 dark:to-purple-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Scale className="h-16 w-16 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Legal Compliance & Inner Peace Focus
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Light Upon Light is committed to full legal compliance while
              maintaining our focus on inner peace, reflection, and spiritual
              growth through beneficial knowledge.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Compliance Statement */}
        <div className="mb-12">
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <CheckCircle className="h-6 w-6 text-green-600 mr-3" />

              <h2 className="text-xl font-bold text-green-800 dark:text-green-300">
                Fully Compliant Platform
              </h2>
            </div>
            <p className="text-green-700 dark:text-green-300 mb-4">
              Our platform has been designed with comprehensive legal compliance
              at its core, ensuring all content is copyright-free, properly
              disclaimed, and focused on promoting inner peace and beneficial
              learning.
            </p>
            <p className="text-green-700 dark:text-green-300">
              Every aspect of our content and functionality has been carefully
              reviewed to meet legal requirements while maintaining our mission
              of spiritual growth and peaceful reflection.
            </p>
          </div>
        </div>

        {/* Compliance Areas */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">
            Comprehensive Legal Compliance
          </h2>
          <div className="space-y-6">
            {complianceAreas.map((area, index) => {
              const Icon = area.icon;
              return (
                <div
                  key={index}
                  className={`${area.bgColor} rounded-lg border p-6`}
                >
                  <div className="flex items-start space-x-4">
                    <Icon className={`h-6 w-6 ${area.color} mt-1`} />

                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {area.title}
                      </h3>
                      <p className="text-gray-700 dark:text-gray-300 mb-4">
                        {area.description}
                      </p>
                      <ul className="space-y-2">
                        {area.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-start">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />

                            <span className="text-sm text-gray-600 dark:text-gray-300">
                              {detail}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Inner Peace Focus */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">
            Inner Peace & Reflection Focus
          </h2>
          <div className="space-y-6">
            {innerPeaceFocus.map((focus, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6"
              >
                <div className="flex items-center mb-4">
                  <Heart className="h-6 w-6 text-rose-600 mr-3" />

                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {focus.aspect}
                  </h3>
                </div>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  {focus.description}
                </p>
                <ul className="space-y-2">
                  {focus.elements.map((element, elementIndex) => (
                    <li key={elementIndex} className="flex items-start">
                      <Heart className="h-4 w-4 text-rose-500 mr-2 mt-0.5 flex-shrink-0" />

                      <span className="text-sm text-gray-600 dark:text-gray-300">
                        {element}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Legal Documentation */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Legal Documentation
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a
              href="/terms"
              className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
            >
              <div className="flex items-center mb-3">
                <FileText className="h-5 w-5 text-blue-600 mr-2" />

                <h3 className="font-semibold text-gray-900 dark:text-white">
                  Terms of Service
                </h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Comprehensive terms including copyright, medical, and
                educational disclaimers
              </p>
            </a>
            <a
              href="/privacy"
              className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
            >
              <div className="flex items-center mb-3">
                <Shield className="h-5 w-5 text-green-600 mr-2" />

                <h3 className="font-semibold text-gray-900 dark:text-white">
                  Privacy Policy
                </h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                GDPR-compliant privacy policy with comprehensive data protection
                measures
              </p>
            </a>
            <a
              href="/gdpr"
              className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
            >
              <div className="flex items-center mb-3">
                <Globe className="h-5 w-5 text-purple-600 mr-2" />

                <h3 className="font-semibold text-gray-900 dark:text-white">
                  GDPR Compliance
                </h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Detailed GDPR compliance information and user rights
              </p>
            </a>
            <a
              href="/cookies"
              className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
            >
              <div className="flex items-center mb-3">
                <AlertTriangle className="h-5 w-5 text-orange-600 mr-2" />

                <h3 className="font-semibold text-gray-900 dark:text-white">
                  Cookie Policy
                </h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Transparent cookie usage and management information
              </p>
            </a>
          </div>
        </div>

        {/* Contact for Legal Matters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Legal Contact Information
          </h2>
          <p className="text-gray-700 dark:text-gray-300 mb-4">
            For any legal questions, compliance concerns, or to report potential
            violations:
          </p>
          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
              <Scale className="h-5 w-5" />
              <span>Legal Contact: Mohammad Abbas</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
              <Globe className="h-5 w-5" />
              <span>Email: <EMAIL></span>
            </div>
            <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
              <FileText className="h-5 w-5" />
              <span>Subject: Legal Compliance Inquiry</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
