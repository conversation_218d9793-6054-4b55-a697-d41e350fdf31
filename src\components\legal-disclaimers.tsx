// Legal Disclaimers Component
// Provides comprehensive legal protection and compliance

import { AlertTriangle, Shield, BookOpen, Heart } from 'lucide-react'

interface DisclaimerProps {
  type: 'medical' | 'religious' | 'educational' | 'general'
  compact?: boolean
  className?: string
}

export function LegalDisclaimer({ type, compact = false, className = "" }: DisclaimerProps) {
  const getDisclaimerContent = () => {
    switch (type) {
      case 'medical':
        return {
          icon: Heart,
          title: 'Medical Disclaimer',
          content: compact 
            ? 'For educational purposes only. Consult healthcare providers for medical advice.'
            : `The information provided on this website is for educational purposes only and is not intended as medical advice, diagnosis, or treatment. Always consult with a qualified healthcare provider before making any health-related decisions or changes to your treatment plan. Individual results may vary.`,
          color: 'text-red-600 bg-red-50 border-red-200'
        }
      
      case 'religious':
        return {
          icon: BookOpen,
          title: 'Religious Content Notice',
          content: compact
            ? 'Educational content. Consult qualified Islamic scholars for religious guidance.'
            : `All Quranic verses, Islamic content, and religious information are provided for educational purposes. We strive for accuracy but recommend consulting qualified Islamic scholars for authoritative religious guidance. This content should not replace proper Islamic education or scholarly consultation.`,
          color: 'text-blue-600 bg-blue-50 border-blue-200'
        }
      
      case 'educational':
        return {
          icon: BookOpen,
          title: 'Educational Purpose',
          content: compact
            ? 'Educational content only. Not professional advice.'
            : `All content is provided for educational and informational purposes only. This website does not provide professional advice and should not be used as a substitute for consultation with professional advisors. Individual experiences and results may vary.`,
          color: 'text-green-600 bg-green-50 border-green-200'
        }
      
      default:
        return {
          icon: Shield,
          title: 'General Disclaimer',
          content: compact
            ? 'Content for informational purposes. Use at your own discretion.'
            : `The information on this website is provided on an "as is" basis. While we strive for accuracy, we make no warranties about the completeness, reliability, or suitability of the information. Use of this website is at your own risk.`,
          color: 'text-gray-600 bg-gray-50 border-gray-200'
        }
    }
  }

  const disclaimer = getDisclaimerContent()
  const Icon = disclaimer.icon

  if (compact) {
    return (
      <div className={`flex items-center space-x-2 text-xs ${disclaimer.color} p-2 rounded ${className}`}>
        <Icon className="h-3 w-3 flex-shrink-0" />
        <span>{disclaimer.content}</span>
      </div>
    )
  }

  return (
    <div className={`border rounded-lg p-4 ${disclaimer.color} ${className}`}>
      <div className="flex items-start space-x-3">
        <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
        <div>
          <h4 className="font-semibold text-sm mb-2">{disclaimer.title}</h4>
          <p className="text-sm leading-relaxed">{disclaimer.content}</p>
        </div>
      </div>
    </div>
  )
}

// Copyright Attribution Component
export function CopyrightAttribution({ 
  source, 
  license, 
  author,
  className = "" 
}: {
  source: string
  license?: string
  author?: string
  className?: string
}) {
  return (
    <div className={`text-xs text-gray-500 border-t pt-2 mt-4 ${className}`}>
      <div className="flex items-center space-x-1">
        <Shield className="h-3 w-3" />
        <span>
          {author && `${author}, `}
          {source}
          {license && ` (${license})`}
        </span>
      </div>
    </div>
  )
}

// Content Source Verification
export function ContentVerification({ 
  verified = true, 
  lastChecked,
  className = "" 
}: {
  verified?: boolean
  lastChecked?: string
  className?: string
}) {
  return (
    <div className={`flex items-center space-x-2 text-xs ${className}`}>
      {verified ? (
        <>
          <Shield className="h-3 w-3 text-green-600" />
          <span className="text-green-600">Content verified</span>
        </>
      ) : (
        <>
          <AlertTriangle className="h-3 w-3 text-yellow-600" />
          <span className="text-yellow-600">Verification pending</span>
        </>
      )}
      {lastChecked && (
        <span className="text-gray-500">• Last checked: {lastChecked}</span>
      )}
    </div>
  )
}

// Page-level Legal Footer
export function PageLegalFooter({ 
  disclaimers = ['educational'], 
  showCopyright = true,
  className = ""
}: {
  disclaimers?: Array<'medical' | 'religious' | 'educational' | 'general'>
  showCopyright?: boolean
  className?: string
}) {
  return (
    <div className={`border-t bg-gray-50 p-6 mt-8 ${className}`}>
      <div className="max-w-4xl mx-auto space-y-4">
        {disclaimers.map((type, index) => (
          <LegalDisclaimer key={index} type={type} compact />
        ))}
        
        {showCopyright && (
          <div className="text-center text-xs text-gray-500 pt-4 border-t">
            <p>© 2024 Light Upon Light. All rights reserved.</p>
            <p className="mt-1">
              Original content licensed under educational use. 
              Quranic content is public domain. 
              User-generated content subject to{' '}
              <a href="/terms" className="text-blue-600 hover:underline">Terms of Service</a>.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

// DMCA Compliance Notice
export function DMCANotice({ className = "" }: { className?: string }) {
  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start space-x-3">
        <Shield className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
        <div className="text-sm">
          <h4 className="font-semibold text-blue-800 mb-2">Copyright Protection</h4>
          <p className="text-blue-700 mb-2">
            We respect intellectual property rights. If you believe content on this site 
            infringes your copyright, please contact us immediately.
          </p>
          <p className="text-blue-600">
            DMCA notices: <a href="mailto:<EMAIL>" className="underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
