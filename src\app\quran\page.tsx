import {
  <PERSON><PERSON><PERSON>,
  Star,
  Brain,
  Heart,
  <PERSON>bulb,
  Zap,
  Droplets,
  Shield,
  Leaf,
} from "lucide-react";

const quranicVerses = [
  {
    id: 1,
    arabic: "وَفِي أَنفُسِكُمْ ۚ أَفَلَا تُبْصِرُونَ",
    transliteration: "Wa fee anfusikum afala tubsiroon",
    translation: "And within yourselves. Then will you not see?",
    reference: "Quran 51:21 (Ad<PERSON>-<PERSON><PERSON><PERSON><PERSON>)",
    theme: "Scientific Self-Discovery",
    explanation:
      "This verse promotes scientific inquiry into human biology and psychology. The human body contains trillions of cells, complex neural networks, and sophisticated biological systems. Modern science continues to discover the intricate mechanisms within us - from DNA to consciousness - validating this call for internal observation and study.",
    benefits: [
      "Promotes scientific curiosity",
      "Encourages self-study",
      "Develops analytical thinking",
      "Advances medical knowledge",
    ],

    icon: Heart,
    color: "text-red-600",
  },
  {
    id: 2,
    arabic: "وَقُل رَّبِّ زِدْنِي عِلْمًا",
    transliteration: "Wa qul rabbi zidni ilman",
    translation: "And say: My Lord, increase me in knowledge.",
    reference: "Quran 20:114 (Ta-Ha)",
    theme: "Lifelong Learning Principle",
    explanation:
      "This verse establishes the fundamental principle of continuous education and intellectual growth. It recognizes that knowledge is infinite and that human progress depends on never-ending learning. This principle drives scientific advancement, innovation, and personal development across all fields of study.",
    benefits: [
      "Promotes lifelong learning",
      "Drives scientific progress",
      "Encourages intellectual humility",
      "Advances human civilization",
    ],

    icon: Brain,
    color: "text-blue-600",
  },
  {
    id: 3,
    arabic: "وَمَا أُوتِيتُم مِّنَ الْعِلْمِ إِلَّا قَلِيلًا",
    transliteration: "Wa ma ooteetum minal ilmi illa qaleela",
    translation: "And you have not been given of knowledge except a little.",
    reference: "Quran 17:85 (Al-Isra)",
    theme: "Scientific Humility Principle",
    explanation:
      "This verse establishes a fundamental principle of scientific thinking: intellectual humility. It acknowledges that human knowledge, no matter how advanced, is limited compared to the vastness of what remains unknown. This principle drives scientific inquiry, prevents dogmatism, and encourages open-minded research.",
    benefits: [
      "Promotes scientific humility",
      "Prevents intellectual arrogance",
      "Encourages research",
      "Drives innovation",
    ],

    icon: Lightbulb,
    color: "text-yellow-600",
  },
  {
    id: 4,
    arabic: "أَلَمْ نَشْرَحْ لَكَ صَدْرَكَ",
    transliteration: "Alam nashrah laka sadraka",
    translation: "Have We not expanded for you your breast?",
    reference: "Quran 94:1 (Ash-Sharh)",
    theme: "Spiritual Expansion",
    explanation:
      "This verse speaks about the expansion of the heart and mind through divine guidance. When Allah opens our hearts, we gain clarity, peace, and understanding. This spiritual expansion allows us to comprehend deeper truths and find tranquility.",
    benefits: [
      "Brings inner peace",
      "Expands spiritual understanding",
      "Provides comfort in difficulties",
    ],

    icon: Heart,
    color: "text-green-600",
  },
  {
    id: 5,
    arabic: "وَمَن يُؤْتَ الْحِكْمَةَ فَقَدْ أُوتِيَ خَيْرًا كَثِيرًا",
    transliteration: "Wa man yu'tal hikmata faqad ootiya khayran katheera",
    translation:
      "And whoever is given wisdom has certainly been given much good.",
    reference: "Quran 2:269 (Al-Baqarah)",
    theme: "Divine Wisdom",
    explanation:
      "Wisdom (Hikmah) is one of the greatest gifts from Allah. It encompasses practical knowledge, good judgment, and the ability to apply knowledge correctly. This verse emphasizes that wisdom is more valuable than material wealth.",
    benefits: [
      "Develops good judgment",
      "Guides decision-making",
      "Brings lasting benefit",
    ],

    icon: Star,
    color: "text-purple-600",
  },
  {
    id: 6,
    arabic: "وَمَا خَلَقْتُ الْجِنَّ وَالْإِنسَ إِلَّا لِيَعْبُدُونِ",
    transliteration: "Wa ma khalaqtul jinna wal insa illa liya'budoon",
    translation:
      "And I did not create the jinn and mankind except to worship Me.",
    reference: "Quran 51:56 (Adh-Dhariyat)",
    theme: "Purpose-Driven Existence",
    explanation:
      "This verse establishes that human existence has inherent purpose and meaning. From a psychological perspective, having a clear sense of purpose is linked to better mental health, longevity, and life satisfaction. The concept of worship here encompasses service, gratitude, and conscious living.",
    benefits: [
      "Provides life purpose",
      "Improves mental health",
      "Encourages service to others",
      "Promotes conscious living",
    ],

    icon: Zap,
    color: "text-orange-600",
  },
  {
    id: 7,
    arabic: "وَجَعَلْنَا مِنَ الْمَاءِ كُلَّ شَيْءٍ حَيٍّ",
    transliteration: "Wa ja'alna minal ma'i kulla shay'in hayy",
    translation: "And We made from water every living thing.",
    reference: "Quran 21:30 (Al-Anbiya)",
    theme: "Water as Life Foundation",
    explanation:
      "This verse presents a fundamental biological truth: water is essential for all life. Modern science confirms that water makes up 60-70% of the human body and is crucial for cellular processes, metabolism, and survival. This principle guides environmental conservation and health practices.",
    benefits: [
      "Promotes water conservation",
      "Encourages hydration",
      "Environmental awareness",
      "Health consciousness",
    ],

    icon: Droplets,
    color: "text-blue-500",
  },
  {
    id: 8,
    arabic:
      "وَأَنزَلْنَا الْحَدِيدَ فِيهِ بَأْسٌ شَدِيدٌ وَمَنَافِعُ لِلنَّاسِ",
    transliteration:
      "Wa anzalnal hadeeda feehi ba'sun shadeedun wa manafi'u linnas",
    translation:
      "And We sent down iron, wherein is great military might and benefits for the people.",
    reference: "Quran 57:25 (Al-Hadid)",
    theme: "Material Science & Technology",
    explanation:
      "This verse highlights iron's dual nature: strength for protection and utility for human benefit. Iron is fundamental to modern civilization - from construction to medicine (hemoglobin). The verse suggests iron came from outside Earth, which aligns with scientific theories about iron formation in stars.",
    benefits: [
      "Advances metallurgy",
      "Promotes technological development",
      "Medical applications",
      "Infrastructure development",
    ],

    icon: Shield,
    color: "text-gray-600",
  },
  {
    id: 9,
    arabic:
      "وَهُوَ الَّذِي أَرْسَلَ الرِّيَاحَ بُشْرًا بَيْنَ يَدَيْ رَحْمَتِهِ",
    transliteration:
      "Wa huwal ladhee arsalar riyaha bushran bayna yaday rahmatih",
    translation:
      "And it is He who sends the winds as good tidings before His mercy.",
    reference: "Quran 7:57 (Al-A'raf)",
    theme: "Atmospheric Science & Weather",
    explanation:
      "This verse describes the role of winds in the water cycle and weather patterns. Winds carry moisture, distribute heat, and create the conditions necessary for precipitation. Modern meteorology confirms the crucial role of atmospheric circulation in climate and weather systems.",
    benefits: [
      "Weather prediction",
      "Climate understanding",
      "Agricultural planning",
      "Renewable energy",
    ],

    icon: Zap,
    color: "text-cyan-600",
  },
  {
    id: 10,
    arabic:
      "أَلَمْ تَرَ أَنَّ اللَّهَ أَنزَلَ مِنَ السَّمَاءِ مَاءً فَأَخْرَجْنَا بِهِ ثَمَرَاتٍ مُّخْتَلِفًا أَلْوَانُهَا",
    transliteration:
      "Alam tara annal laaha anzala minas samaai maaan fa akhrajna bihi thamaraatin mukhtalifan alwaanuha",
    translation:
      "Do you not see that Allah sends down rain from the sky, and We produce thereby fruits of varying colors?",
    reference: "Quran 35:27 (Fatir)",
    theme: "Biodiversity & Genetics",
    explanation:
      "This verse points to the incredible diversity in nature resulting from the same basic elements (water, soil, sunlight). Modern genetics explains how the same fundamental building blocks create vast biodiversity through different genetic expressions and environmental adaptations.",
    benefits: [
      "Biodiversity appreciation",
      "Genetic research",
      "Agricultural diversity",
      "Ecosystem understanding",
    ],

    icon: Leaf,
    color: "text-green-600",
  },
  {
    id: 11,
    arabic:
      "وَمِنْ آيَاتِهِ خَلْقُ السَّمَاوَاتِ وَالْأَرْضِ وَاخْتِلَافُ أَلْسِنَتِكُمْ وَأَلْوَانِكُمْ",
    transliteration:
      "Wa min aayaatihi khalqus samaawaati wal ardi wakhtilaafu alsinatikum wa alwaanikum",
    translation:
      "And of His signs is the creation of the heavens and earth and the diversity of your languages and colors.",
    reference: "Quran 30:22 (Ar-Rum)",
    theme: "Human Diversity & Linguistics",
    explanation:
      "This verse celebrates human diversity in language and appearance as signs of intelligent design. Linguistic diversity demonstrates the remarkable capacity of human cognition, while physical diversity shows genetic adaptation to different environments.",
    benefits: [
      "Cultural appreciation",
      "Linguistic studies",
      "Genetic diversity understanding",
      "Social harmony",
    ],

    icon: Brain,
    color: "text-purple-600",
  },
  {
    id: 12,
    arabic:
      "وَلَقَدْ كَرَّمْنَا بَنِي آدَمَ وَحَمَلْنَاهُمْ فِي الْبَرِّ وَالْبَحْرِ",
    transliteration:
      "Wa laqad karramna banee aadama wa hamalnaahum fil barri wal bahr",
    translation:
      "And We have certainly honored the children of Adam and carried them on land and sea.",
    reference: "Quran 17:70 (Al-Isra)",
    theme: "Human Dignity & Potential",
    explanation:
      "This verse establishes the inherent dignity and potential of all humans. It emphasizes human capacity for exploration, travel, and mastery over environments. This principle supports human rights, education, and the development of human potential regardless of background.",
    benefits: [
      "Human rights foundation",
      "Educational motivation",
      "Exploration spirit",
      "Universal dignity",
    ],

    icon: Heart,
    color: "text-red-600",
  },
];

const themes = [
  {
    name: "Wisdom & Intelligence",
    count: 15,
    color: "bg-blue-100 text-blue-800",
  },
  {
    name: "Knowledge & Learning",
    count: 12,
    color: "bg-green-100 text-green-800",
  },
  { name: "Self-Reflection", count: 8, color: "bg-purple-100 text-purple-800" },
  { name: "Divine Signs", count: 10, color: "bg-yellow-100 text-yellow-800" },
  { name: "Spiritual Growth", count: 9, color: "bg-red-100 text-red-800" },
  { name: "Natural World", count: 7, color: "bg-indigo-100 text-indigo-800" },
];

export default function QuranPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/20 dark:via-indigo-900/20 dark:to-purple-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <BookOpen className="h-16 w-16 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Quranic Logic & Universal Wisdom
            </h1>
            <div className="arabic-text text-2xl md:text-3xl text-blue-600 dark:text-blue-400 mb-4">
              آيَاتُ الْحِكْمَةِ وَالْعِلْمِ
            </div>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Discover logical principles and universal wisdom from Quranic
              verses that emphasize intelligence, scientific thinking, natural
              phenomena, and rational understanding that benefits all humanity
              regardless of background.
            </p>
          </div>
        </div>
      </div>

      {/* Legal Disclaimer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-300 mb-2">
            Educational & Interpretive Disclaimer
          </h3>
          <p className="text-yellow-700 dark:text-yellow-300 text-sm mb-3">
            The interpretations and scientific correlations presented here are
            for educational and intellectual reflection purposes designed to
            promote inner peace and spiritual understanding. They represent one
            perspective among many possible understandings and should not be
            considered as definitive religious or scientific authority. Always
            consult qualified Islamic scholars and scientific experts for
            authoritative guidance.
          </p>
          <p className="text-yellow-700 dark:text-yellow-300 text-sm">
            <strong>Copyright Notice:</strong> Quranic verses are from public
            domain sources. All interpretations, explanations, and educational
            content are original works created for peaceful reflection and
            learning. This content is designed to inspire inner peace, promote
            understanding, and encourage beneficial knowledge acquisition.
          </p>
        </div>
      </div>

      {/* Introduction Verse */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="verse-container text-center">
          <div className="arabic-text text-2xl md:text-3xl mb-4 text-gray-800 dark:text-gray-200">
            إِنَّ فِي خَلْقِ السَّمَاوَاتِ وَالْأَرْضِ وَاخْتِلَافِ اللَّيْلِ
            وَالنَّهَارِ لَآيَاتٍ لِّأُولِي الْأَلْبَابِ
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-2">
            "Indeed, in the creation of the heavens and the earth and the
            alternation of night and day are signs for those of understanding."
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Quran 3:190 (Ali 'Imran)
          </p>
          <div className="mt-4 text-sm text-gray-600 dark:text-gray-400 italic">
            This verse encourages intellectual reflection and scientific
            observation as pathways to understanding.
          </div>
        </div>
      </div>

      {/* Awe-Inspiring Development Facts */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-16">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
          Amazing Facts About Divine Wisdom & Intellectual Development
        </h2>
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-8 border border-blue-200 dark:border-blue-800">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
              <div className="text-center mb-4">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl">📖</span>
                </div>
              </div>
              <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">
                Divine Logic & Universal Wisdom
              </h3>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                The Quran contains logical principles and universal wisdom that
                benefit all humanity. Its teachings about knowledge, reflection,
                and natural phenomena provide guidance for intellectual
                development, scientific thinking, and rational understanding
                that transcends cultural boundaries.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
              <div className="text-center mb-4">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl">💎</span>
                </div>
              </div>
              <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">
                Heart Confirmation Through Divine Guidance
              </h3>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                When you reflect on divine wisdom and universal principles, your
                heart confirms the truth and beauty in these teachings. This
                inner knowing brings peace, clarity, and understanding that
                guides you toward beneficial knowledge and righteous living.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
              <div className="text-center mb-4">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl">🧠</span>
                </div>
              </div>
              <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">
                Intellectual Living Through Divine Wisdom
              </h3>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Divine wisdom develops your intelligence and understanding about
                life, knowledge, and natural phenomena. You learn to think
                logically, seek beneficial knowledge, and make wise decisions
                based on universal principles that promote growth and
                well-being.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
              <div className="text-center mb-4">
                <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl">⚖️</span>
                </div>
              </div>
              <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">
                Perfect Balance & Divine Alignment
              </h3>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Divine teachings provide perfect balance between spiritual and
                intellectual development. This balanced approach helps you grow
                in wisdom, knowledge, and understanding while maintaining
                harmony between heart and mind, faith and reason, study and
                practice.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
              <div className="text-center mb-4">
                <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl">🌱</span>
                </div>
              </div>
              <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">
                Path to Inner Growth & Enlightenment
              </h3>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Each verse and teaching becomes a step on your path to inner
                growth and enlightenment. As you study and reflect on divine
                wisdom, your understanding deepens, your character improves, and
                your connection to universal truth strengthens.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
              <div className="text-center mb-4">
                <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl">✨</span>
                </div>
              </div>
              <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">
                Gateway to Divine Knowledge
              </h3>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Divine wisdom opens doorways to higher knowledge and
                understanding. Through study, reflection, and application of
                these teachings, you develop deeper appreciation for the
                infinite wisdom and intelligence that governs all existence with
                perfect knowledge.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Themes */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-12">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
          Explore by Theme
        </h2>
        <div className="flex flex-wrap justify-center gap-3">
          {themes.map((theme, index) => (
            <div
              key={index}
              className={`px-4 py-2 rounded-full ${theme.color} font-medium cursor-pointer hover:shadow-md transition-shadow`}
            >
              {theme.name} ({theme.count})
            </div>
          ))}
        </div>
      </div>

      {/* Verses */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        <div className="space-y-8">
          {quranicVerses.map((verse) => {
            const Icon = verse.icon;
            return (
              <div
                key={verse.id}
                className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden"
              >
                <div className="p-8">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <Icon className={`h-8 w-8 ${verse.color}`} />

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {verse.theme}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {verse.reference}
                        </p>
                      </div>
                    </div>
                    <Star className="h-6 w-6 text-yellow-500" />
                  </div>

                  {/* Arabic Text */}
                  <div className="text-center mb-6">
                    <div className="arabic-text text-3xl md:text-4xl font-bold text-gray-800 dark:text-gray-200 mb-4 leading-relaxed">
                      {verse.arabic}
                    </div>
                    <div className="text-lg text-gray-600 dark:text-gray-400 mb-2 italic">
                      {verse.transliteration}
                    </div>
                    <div className="text-xl font-medium text-gray-900 dark:text-white">
                      "{verse.translation}"
                    </div>
                  </div>

                  {/* Explanation */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Explanation & Wisdom
                    </h4>
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
                      {verse.explanation}
                    </p>

                    {/* Benefits */}
                    <div>
                      <h5 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                        Spiritual Benefits
                      </h5>
                      <div className="flex flex-wrap gap-2">
                        {verse.benefits.map((benefit, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-sm"
                          >
                            {benefit}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8 max-w-2xl mx-auto">
            <BookOpen className="h-12 w-12 text-blue-600 mx-auto mb-4" />

            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Continue Your Journey
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              This collection represents just a glimpse of the profound wisdom
              contained in the Holy Quran. Regular study, reflection, and
              application of these teachings leads to spiritual growth and
              enlightenment.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Brain className="h-8 w-8 text-blue-600 mx-auto mb-2" />

                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Daily Reflection
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  Contemplate one verse daily
                </p>
              </div>
              <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <Heart className="h-8 w-8 text-green-600 mx-auto mb-2" />

                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Heart Purification
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  Apply teachings in life
                </p>
              </div>
              <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <Star className="h-8 w-8 text-purple-600 mx-auto mb-2" />

                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Share Wisdom
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  Teach others what you learn
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
