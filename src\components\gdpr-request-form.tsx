'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Shield, Mail, CheckCircle, AlertTriangle, FileText, Download, Trash2, Edit, Lock, Eye } from 'lucide-react'
import { EmailService, type GDPRRequestData } from '@/lib/email-service'

interface GDPRRequestFormProps {
  isOpen: boolean
  onClose: () => void
}

export function GDPRRequestForm({ isOpen, onClose }: GDPRRequestFormProps) {
  const [formData, setFormData] = useState<GDPRRequestData>({
    name: '',
    email: '',
    requestType: 'access',
    details: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const requestTypes = [
    { 
      value: 'access', 
      label: 'Access My Data', 
      description: 'Request a copy of all personal data we hold about you',
      icon: Eye,
      article: 'Article 15'
    },
    { 
      value: 'deletion', 
      label: 'Delete My Data', 
      description: 'Request deletion of your personal data (Right to be Forgotten)',
      icon: Trash2,
      article: 'Article 17'
    },
    { 
      value: 'portability', 
      label: 'Data Portability', 
      description: 'Request your data in a portable format to transfer to another service',
      icon: Download,
      article: 'Article 20'
    },
    { 
      value: 'rectification', 
      label: 'Correct My Data', 
      description: 'Request correction of inaccurate or incomplete personal data',
      icon: Edit,
      article: 'Article 16'
    },
    { 
      value: 'restriction', 
      label: 'Restrict Processing', 
      description: 'Request limitation of processing of your personal data',
      icon: Lock,
      article: 'Article 18'
    },
    { 
      value: 'objection', 
      label: 'Object to Processing', 
      description: 'Object to processing of your personal data for specific purposes',
      icon: Shield,
      article: 'Article 21'
    }
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name || !formData.email || !formData.requestType) {
      alert('Please fill in all required fields')
      return
    }

    if (!EmailService.isValidEmail(formData.email)) {
      alert('Please enter a valid email address')
      return
    }

    setIsSubmitting(true)
    try {
      const result = await EmailService.sendGDPRRequest(formData)
      
      if (result.success) {
        setSubmitStatus('success')
        setTimeout(() => {
          setSubmitStatus('idle')
          onClose()
          setFormData({
            name: '',
            email: '',
            requestType: 'access',
            details: ''
          })
        }, 3000)
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('Error submitting GDPR request:', error)
      setSubmitStatus('error')
      setTimeout(() => setSubmitStatus('idle'), 5000)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <Shield className="h-6 w-6 text-blue-600" />
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                GDPR Data Protection Request
              </h3>
            </div>
            <Button onClick={onClose} variant="ghost" size="sm">
              ✕
            </Button>
          </div>

          {/* Status Messages */}
          {submitStatus === 'success' && (
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="text-green-800 dark:text-green-300">
                GDPR request submitted successfully! You will receive a response within 30 days.
              </span>
            </div>
          )}

          {submitStatus === 'error' && (
            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <span className="text-red-800 dark:text-red-300">
                Failed to submit request. Please try again or contact us directly.
              </span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Enter your full name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            {/* Request Type */}
            <div>
              <label className="block text-sm font-medium text-gray-900 dark:text-white mb-3">
                Type of Request *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {requestTypes.map((type) => {
                  const Icon = type.icon
                  return (
                    <label
                      key={type.value}
                      className={`relative flex items-start p-4 border rounded-lg cursor-pointer transition-colors ${
                        formData.requestType === type.value
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                    >
                      <input
                        type="radio"
                        name="requestType"
                        value={type.value}
                        checked={formData.requestType === type.value}
                        onChange={handleInputChange}
                        className="sr-only"
                      />
                      <Icon className="h-5 w-5 text-blue-600 mr-3 mt-0.5" />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {type.label}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          {type.description}
                        </div>
                        <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                          GDPR {type.article}
                        </div>
                      </div>
                    </label>
                  )
                })}
              </div>
            </div>

            {/* Additional Details */}
            <div>
              <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                Additional Details (Optional)
              </label>
              <textarea
                name="details"
                value={formData.details}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="Please provide any additional details about your request..."
              />
            </div>

            {/* Legal Notice */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <FileText className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800 dark:text-blue-300">
                  <strong>Legal Notice:</strong> This request will be processed in accordance with GDPR requirements. 
                  We will respond within 30 days and may require additional verification of your identity. 
                  Your request will be sent to our Data Protection Officer for review.
                </div>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex space-x-3">
              <Button
                type="button"
                onClick={onClose}
                variant="outline"
                className="flex-1"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || !formData.name || !formData.email}
                className="flex-1"
              >
                <Mail className="h-4 w-4 mr-2" />
                {isSubmitting ? 'Submitting...' : 'Submit Request'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
