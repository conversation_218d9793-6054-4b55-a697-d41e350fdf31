/**
 * Privacy-Respecting Analytics & GDPR Compliance
 * Comprehensive privacy-first analytics and consent management
 */

// Privacy Configuration
export const PRIVACY_CONFIG = {
  cookieConsent: {
    essential: ['session', 'auth', 'preferences'],
    functional: ['language', 'theme', 'accessibility'],
    analytics: ['page-views', 'user-behavior', 'performance'],
    marketing: ['social-media', 'advertising', 'tracking']
  },
  dataRetention: {
    analytics: 26, // months
    logs: 12, // months
    userContent: 'indefinite', // until user deletion
    sessions: 30 // days
  },
  gdprRights: [
    'access',
    'rectification',
    'erasure',
    'portability',
    'restriction',
    'objection'
  ]
} as const;

// Consent Management
export interface ConsentPreferences {
  essential: boolean;
  functional: boolean;
  analytics: boolean;
  marketing: boolean;
  timestamp: number;
  version: string;
}

export class ConsentManager {
  private static readonly CONSENT_KEY = 'light-upon-light-consent';
  private static readonly CONSENT_VERSION = '1.0';

  // Get current consent preferences
  static getConsent(): ConsentPreferences | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const stored = localStorage.getItem(this.CONSENT_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  }

  // Set consent preferences
  static setConsent(preferences: Omit<ConsentPreferences, 'timestamp' | 'version'>): void {
    if (typeof window === 'undefined') return;

    const consent: ConsentPreferences = {
      ...preferences,
      timestamp: Date.now(),
      version: this.CONSENT_VERSION
    };

    localStorage.setItem(this.CONSENT_KEY, JSON.stringify(consent));
    
    // Trigger consent change event
    window.dispatchEvent(new CustomEvent('consentChange', { detail: consent }));
  }

  // Check if consent is required
  static requiresConsent(): boolean {
    const consent = this.getConsent();
    return !consent || consent.version !== this.CONSENT_VERSION;
  }

  // Check if specific category is consented
  static hasConsent(category: keyof Omit<ConsentPreferences, 'timestamp' | 'version'>): boolean {
    const consent = this.getConsent();
    return consent ? consent[category] : false;
  }

  // Clear all consent data
  static clearConsent(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.CONSENT_KEY);
  }
}

// Privacy-Respecting Analytics
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp: number;
  sessionId: string;
  userId?: string;
  anonymous: boolean;
}

export class PrivacyAnalytics {
  private sessionId: string;
  private events: AnalyticsEvent[] = [];
  private isEnabled = false;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.checkConsent();
    
    // Listen for consent changes
    if (typeof window !== 'undefined') {
      window.addEventListener('consentChange', () => {
        this.checkConsent();
      });
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private checkConsent(): void {
    this.isEnabled = ConsentManager.hasConsent('analytics');
  }

  // Track page view
  trackPageView(path: string, title?: string): void {
    if (!this.isEnabled) return;

    this.track('page_view', {
      path: this.anonymizePath(path),
      title,
      referrer: this.anonymizeReferrer(document.referrer),
      userAgent: this.anonymizeUserAgent(navigator.userAgent),
      language: navigator.language,
      screenResolution: `${screen.width}x${screen.height}`,
      timestamp: Date.now()
    });
  }

  // Track custom event
  track(eventName: string, properties: Record<string, any> = {}): void {
    if (!this.isEnabled) return;

    const event: AnalyticsEvent = {
      name: eventName,
      properties: this.sanitizeProperties(properties),
      timestamp: Date.now(),
      sessionId: this.sessionId,
      anonymous: true
    };

    this.events.push(event);
    this.sendEvent(event);
  }

  // Track performance metrics
  trackPerformance(metrics: {
    lcp?: number;
    fid?: number;
    cls?: number;
    fcp?: number;
    ttfb?: number;
  }): void {
    if (!this.isEnabled) return;

    this.track('performance_metrics', {
      ...metrics,
      connection: (navigator as any).connection?.effectiveType,
      deviceMemory: (navigator as any).deviceMemory
    });
  }

  // Track user interaction
  trackInteraction(element: string, action: string, value?: string): void {
    if (!this.isEnabled) return;

    this.track('user_interaction', {
      element: this.sanitizeElement(element),
      action,
      value: value ? this.sanitizeValue(value) : undefined
    });
  }

  // Track error
  trackError(error: Error, context?: string): void {
    if (!this.isEnabled) return;

    this.track('error', {
      message: error.message,
      stack: this.sanitizeStack(error.stack),
      context,
      userAgent: this.anonymizeUserAgent(navigator.userAgent)
    });
  }

  private anonymizePath(path: string): string {
    // Remove query parameters and hash
    return path.split('?')[0].split('#')[0];
  }

  private anonymizeReferrer(referrer: string): string {
    if (!referrer) return '';
    
    try {
      const url = new URL(referrer);
      return url.hostname;
    } catch {
      return 'unknown';
    }
  }

  private anonymizeUserAgent(userAgent: string): string {
    // Extract only browser and OS info, remove detailed version numbers
    const browser = userAgent.match(/(Chrome|Firefox|Safari|Edge|Opera)\/[\d.]+/)?.[0] || 'Unknown';
    const os = userAgent.match(/(Windows|Mac|Linux|Android|iOS)/)?.[0] || 'Unknown';
    return `${browser} on ${os}`;
  }

  private sanitizeProperties(properties: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(properties)) {
      // Skip sensitive data
      if (this.isSensitiveKey(key)) continue;
      
      sanitized[key] = this.sanitizeValue(value);
    }
    
    return sanitized;
  }

  private isSensitiveKey(key: string): boolean {
    const sensitiveKeys = ['email', 'password', 'token', 'api_key', 'secret', 'phone', 'address'];
    return sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive));
  }

  private sanitizeValue(value: any): any {
    if (typeof value === 'string') {
      // Remove potential PII
      return value.replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[email]')
                  .replace(/\b\d{3}-\d{3}-\d{4}\b/g, '[phone]')
                  .replace(/\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/g, '[card]');
    }
    
    return value;
  }

  private sanitizeElement(element: string): string {
    // Remove IDs and classes that might contain sensitive info
    return element.replace(/id="[^"]*"/g, 'id="[id]"')
                  .replace(/class="[^"]*"/g, 'class="[class]"');
  }

  private sanitizeStack(stack?: string): string {
    if (!stack) return '';
    
    // Remove file paths and line numbers for privacy
    return stack.split('\n')
                .map(line => line.replace(/\/.*\//g, '/[path]/'))
                .slice(0, 5) // Limit stack trace length
                .join('\n');
  }

  private async sendEvent(event: AnalyticsEvent): Promise<void> {
    try {
      // Send to privacy-respecting analytics service
      await fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(event)
      });
    } catch (error) {
      console.warn('Analytics event failed to send:', error);
    }
  }

  // Get analytics summary (for admin dashboard)
  getAnalyticsSummary(): {
    totalEvents: number;
    sessionId: string;
    isEnabled: boolean;
    recentEvents: AnalyticsEvent[];
  } {
    return {
      totalEvents: this.events.length,
      sessionId: this.sessionId,
      isEnabled: this.isEnabled,
      recentEvents: this.events.slice(-10)
    };
  }
}

// GDPR Data Subject Rights
export class GDPRManager {
  // Request user data export
  static async requestDataExport(email: string): Promise<{
    success: boolean;
    message: string;
    requestId?: string;
  }> {
    try {
      const response = await fetch('/api/gdpr/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, type: 'export' })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        message: 'Failed to submit data export request'
      };
    }
  }

  // Request data deletion
  static async requestDataDeletion(email: string, reason?: string): Promise<{
    success: boolean;
    message: string;
    requestId?: string;
  }> {
    try {
      const response = await fetch('/api/gdpr/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, reason, type: 'deletion' })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        message: 'Failed to submit data deletion request'
      };
    }
  }

  // Request data rectification
  static async requestDataRectification(email: string, corrections: Record<string, any>): Promise<{
    success: boolean;
    message: string;
    requestId?: string;
  }> {
    try {
      const response = await fetch('/api/gdpr/rectify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, corrections, type: 'rectification' })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        message: 'Failed to submit data rectification request'
      };
    }
  }
}

// Cookie Management
export class CookieManager {
  // Set cookie with consent check
  static setCookie(name: string, value: string, options: {
    expires?: number;
    path?: string;
    domain?: string;
    secure?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
    category: keyof Omit<ConsentPreferences, 'timestamp' | 'version'>;
  }): boolean {
    if (!ConsentManager.hasConsent(options.category)) {
      return false;
    }

    const { category, ...cookieOptions } = options;
    let cookieString = `${name}=${encodeURIComponent(value)}`;

    if (cookieOptions.expires) {
      const date = new Date();
      date.setTime(date.getTime() + (cookieOptions.expires * 24 * 60 * 60 * 1000));
      cookieString += `; expires=${date.toUTCString()}`;
    }

    if (cookieOptions.path) cookieString += `; path=${cookieOptions.path}`;
    if (cookieOptions.domain) cookieString += `; domain=${cookieOptions.domain}`;
    if (cookieOptions.secure) cookieString += `; secure`;
    if (cookieOptions.sameSite) cookieString += `; samesite=${cookieOptions.sameSite}`;

    document.cookie = cookieString;
    return true;
  }

  // Get cookie
  static getCookie(name: string): string | null {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
    }
    
    return null;
  }

  // Delete cookie
  static deleteCookie(name: string, path?: string, domain?: string): void {
    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC`;
    if (path) cookieString += `; path=${path}`;
    if (domain) cookieString += `; domain=${domain}`;
    document.cookie = cookieString;
  }

  // Clear all non-essential cookies
  static clearNonEssentialCookies(): void {
    const consent = ConsentManager.getConsent();
    if (!consent) return;

    const cookies = document.cookie.split(';');
    
    cookies.forEach(cookie => {
      const [name] = cookie.split('=');
      const cleanName = name.trim();
      
      // Keep essential cookies
      if (PRIVACY_CONFIG.cookieConsent.essential.some(essential => 
        cleanName.includes(essential)
      )) {
        return;
      }

      // Clear based on consent
      if (!consent.functional && PRIVACY_CONFIG.cookieConsent.functional.some(func => 
        cleanName.includes(func)
      )) {
        this.deleteCookie(cleanName);
      }

      if (!consent.analytics && PRIVACY_CONFIG.cookieConsent.analytics.some(analytics => 
        cleanName.includes(analytics)
      )) {
        this.deleteCookie(cleanName);
      }

      if (!consent.marketing && PRIVACY_CONFIG.cookieConsent.marketing.some(marketing => 
        cleanName.includes(marketing)
      )) {
        this.deleteCookie(cleanName);
      }
    });
  }
}

// Initialize privacy-respecting analytics
export const analytics = new PrivacyAnalytics();
