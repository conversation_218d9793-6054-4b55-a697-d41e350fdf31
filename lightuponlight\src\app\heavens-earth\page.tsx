import { Star, Globe, Brain, Heart, <PERSON>bulb, <PERSON>ap, <PERSON>, Moon, Mountain, Waves } from 'lucide-react'

const cosmicPhenomena = [
  {
    name: 'The Expanding Universe',
    quranicReference: 'وَالسَّمَاءَ بَنَيْنَاهَا بِأَيْدٍ وَإِنَّا لَمُوسِعُونَ',
    translation: 'And the heaven We constructed with strength, and indeed, We are [its] expander.',
    reference: 'Quran 51:47 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)',
    scientificInsight: 'Modern cosmology confirms the universe has been expanding since the Big Bang 13.8 billion years ago, with galaxies moving away from each other.',
    intellectualBenefit: 'Understanding cosmic expansion develops perspective on scale, time, and our place in existence.',
    practicalApplication: 'Cultivates humility, wonder, and appreciation for the vastness of creation.',
    icon: Star,
    color: 'text-purple-600'
  },
  {
    name: 'Celestial Bodies in Orbit',
    quranicReference: 'وَكُلٌّ فِي فَلَكٍ يَسْبَحُونَ',
    translation: 'And all, in an orbit, are swimming.',
    reference: 'Quran 21:33 (<PERSON><PERSON><PERSON><PERSON><PERSON>)',
    scientificInsight: 'All celestial bodies follow precise orbital mechanics governed by gravitational forces, from electrons around nuclei to planets around stars.',
    intellectualBenefit: 'Demonstrates the importance of order, precision, and natural laws in achieving harmony.',
    practicalApplication: 'Inspires systematic thinking, planning, and understanding of cause-and-effect relationships.',
    icon: Globe,
    color: 'text-blue-600'
  },
  {
    name: 'The Water Cycle',
    quranicReference: 'وَأَنزَلْنَا مِنَ السَّمَاءِ مَاءً بِقَدَرٍ فَأَسْكَنَّاهُ فِي الْأَرْضِ',
    translation: 'And We sent down from the sky water in due proportion, and We settled it in the earth.',
    reference: 'Quran 23:18 (Al-Mu\'minun)',
    scientificInsight: 'The water cycle involves evaporation, condensation, precipitation, and collection - a perfect recycling system that sustains all life.',
    intellectualBenefit: 'Teaches the principles of conservation, recycling, and sustainable resource management.',
    practicalApplication: 'Promotes environmental consciousness and efficient resource utilization in daily life.',
    icon: Waves,
    color: 'text-cyan-600'
  },
  {
    name: 'Mountains as Stabilizers',
    quranicReference: 'وَأَلْقَىٰ فِي الْأَرْضِ رَوَاسِيَ أَن تَمِيدَ بِكُمْ',
    translation: 'And He placed on the earth firmly set mountains, lest it should shift with you.',
    reference: 'Quran 16:15 (An-Nahl)',
    scientificInsight: 'Mountains act as geological anchors, stabilizing tectonic plates and preventing excessive seismic activity.',
    intellectualBenefit: 'Illustrates the importance of strong foundations and stability in any system or endeavor.',
    practicalApplication: 'Emphasizes building solid foundations in knowledge, character, and life structures.',
    icon: Mountain,
    color: 'text-green-600'
  },
  {
    name: 'The Sun and Moon System',
    quranicReference: 'وَجَعَلْنَا اللَّيْلَ وَالنَّهَارَ آيَتَيْنِ',
    translation: 'And We have made the night and day two signs.',
    reference: 'Quran 17:12 (Al-Isra)',
    scientificInsight: 'The Earth\'s rotation creates day-night cycles essential for circadian rhythms, temperature regulation, and biological processes.',
    intellectualBenefit: 'Demonstrates the wisdom of balance, rhythm, and alternating cycles in optimal functioning.',
    practicalApplication: 'Encourages balanced lifestyle with proper rest, work cycles, and natural rhythms.',
    icon: Sun,
    color: 'text-yellow-600'
  }
]

const paradiseDescriptions = [
  {
    aspect: 'Perfect Environment',
    description: 'A realm of perfect climate, pure air, and optimal conditions for existence.',
    earthlyReflection: 'Clean environments, fresh air, and natural settings promote physical and mental health.',
    developmentPrinciple: 'Creating and maintaining clean, harmonious environments enhances human potential.',
    practicalBenefit: 'Environmental consciousness leads to better health, productivity, and well-being.'
  },
  {
    aspect: 'Flowing Rivers',
    description: 'Rivers of pure water, milk, honey, and wine flowing beneath gardens.',
    earthlyReflection: 'Clean water sources, nutritious foods, and natural abundance sustain life.',
    developmentPrinciple: 'Ensuring access to pure resources and nutrition supports optimal human development.',
    practicalBenefit: 'Proper hydration and nutrition enhance cognitive function and physical performance.'
  },
  {
    aspect: 'Eternal Youth and Vitality',
    description: 'Inhabitants maintain perfect health, strength, and youthful vigor.',
    earthlyReflection: 'Healthy lifestyle choices can maintain vitality and slow aging processes.',
    developmentPrinciple: 'Physical health is foundational to intellectual and spiritual development.',
    practicalBenefit: 'Exercise, proper nutrition, and healthy habits optimize human potential.'
  },
  {
    aspect: 'Perfect Knowledge',
    description: 'Complete understanding and wisdom without confusion or ignorance.',
    earthlyReflection: 'Continuous learning and education expand human understanding and capabilities.',
    developmentPrinciple: 'Knowledge acquisition and intellectual development are fundamental human purposes.',
    practicalBenefit: 'Education and learning enhance problem-solving abilities and life satisfaction.'
  },
  {
    aspect: 'Harmonious Community',
    description: 'Perfect social harmony with no conflict, jealousy, or negative emotions.',
    earthlyReflection: 'Healthy relationships and communities support individual and collective growth.',
    developmentPrinciple: 'Social skills and emotional intelligence are essential for human flourishing.',
    practicalBenefit: 'Strong relationships and community connections improve mental health and success.'
  }
]

const alignmentPrinciples = [
  {
    principle: 'Cosmic Perspective',
    description: 'Understanding our place in the vast universe develops humility and wonder.',
    practice: 'Regular stargazing, studying astronomy, and contemplating cosmic scales.',
    benefit: 'Reduces ego, increases gratitude, and provides perspective on daily challenges.'
  },
  {
    principle: 'Natural Rhythms',
    description: 'Aligning with natural cycles optimizes human biological and psychological functions.',
    practice: 'Following circadian rhythms, seasonal eating, and natural light exposure.',
    benefit: 'Improves sleep, energy levels, mood, and overall health.'
  },
  {
    principle: 'Environmental Harmony',
    description: 'Living in harmony with natural systems promotes sustainability and well-being.',
    practice: 'Sustainable living, environmental conservation, and nature connection.',
    benefit: 'Creates healthier environments and develops ecological consciousness.'
  },
  {
    principle: 'Universal Laws',
    description: 'Understanding and applying natural laws in human affairs creates order and success.',
    practice: 'Studying physics, mathematics, and natural principles for life applications.',
    benefit: 'Develops logical thinking and effective problem-solving abilities.'
  }
]

export default function HeavensEarthPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 dark:from-indigo-900/20 dark:via-purple-900/20 dark:to-pink-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6 space-x-4">
              <Star className="h-16 w-16 text-purple-600" />
              <Globe className="h-16 w-16 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Heavens & Earth: Cosmic Intelligence
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Explore the intelligent design of the universe through scientific insights and universal wisdom. 
              Discover how cosmic phenomena, paradise descriptions, and natural laws provide frameworks 
              for human development, intellectual growth, and optimal living.
            </p>
          </div>
        </div>
      </div>

      {/* Legal Disclaimer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-300 mb-2">
            Educational Disclaimer
          </h3>
          <p className="text-yellow-700 dark:text-yellow-300 text-sm">
            The content on this page presents scientific observations alongside traditional wisdom for educational purposes. 
            Interpretations are provided to encourage intellectual reflection and should not be considered as definitive 
            religious or scientific authority. Always consult qualified experts for specific guidance.
          </p>
        </div>
      </div>

      {/* Wisdom Quote */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="verse-container text-center">
          <div className="flex justify-center mb-4">
            <Star className="h-8 w-8 text-purple-600" />
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4">
            "The heavens and earth contain signs for those who reflect. In their perfect order and 
            intelligent design, we find principles for optimal human development and harmonious living."
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Universal Cosmic Wisdom
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* Cosmic Phenomena */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Cosmic Intelligence & Universal Laws
          </h2>
          <div className="space-y-8">
            {cosmicPhenomena.map((phenomenon, index) => {
              const Icon = phenomenon.icon
              return (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden"
                >
                  <div className="p-8">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        <Icon className={`h-8 w-8 ${phenomenon.color}`} />
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                            {phenomenon.name}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {phenomenon.reference}
                          </p>
                        </div>
                      </div>
                      <Brain className="h-6 w-6 text-blue-500" />
                    </div>

                    {/* Quranic Reference */}
                    <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="arabic-text text-xl font-bold text-blue-800 dark:text-blue-300 mb-2">
                        {phenomenon.quranicReference}
                      </div>
                      <p className="text-blue-700 dark:text-blue-300 italic">
                        "{phenomenon.translation}"
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Scientific Insight */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                          <Lightbulb className="h-5 w-5 text-yellow-500 mr-2" />
                          Scientific Insight
                        </h4>
                        <p className="text-gray-700 dark:text-gray-300">
                          {phenomenon.scientificInsight}
                        </p>
                      </div>

                      {/* Intellectual Benefit */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                          <Brain className="h-5 w-5 text-blue-500 mr-2" />
                          Intellectual Development
                        </h4>
                        <p className="text-gray-700 dark:text-gray-300">
                          {phenomenon.intellectualBenefit}
                        </p>
                      </div>
                    </div>

                    {/* Practical Application */}
                    <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <h4 className="text-lg font-semibold text-green-800 dark:text-green-300 mb-2 flex items-center">
                        <Zap className="h-5 w-5 mr-2" />
                        Practical Application
                      </h4>
                      <p className="text-green-700 dark:text-green-300">
                        {phenomenon.practicalApplication}
                      </p>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Paradise Principles */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Paradise Principles for Human Development
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {paradiseDescriptions.map((paradise, index) => (
              <div key={index} className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <Heart className="h-6 w-6 text-red-500 mr-3" />
                  {paradise.aspect}
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-1">Ideal Description:</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">{paradise.description}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-1">Earthly Reflection:</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">{paradise.earthlyReflection}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-1">Development Principle:</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">{paradise.developmentPrinciple}</p>
                  </div>
                  
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded">
                    <h4 className="font-medium text-green-800 dark:text-green-300 mb-1">Practical Benefit:</h4>
                    <p className="text-green-700 dark:text-green-300 text-sm">{paradise.practicalBenefit}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Alignment Principles */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Cosmic Alignment for Optimal Living
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {alignmentPrinciples.map((alignment, index) => (
              <div key={index} className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {alignment.principle}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {alignment.description}
                </p>
                
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-1">Practice:</h4>
                    <p className="text-gray-700 dark:text-gray-300 text-sm">{alignment.practice}</p>
                  </div>
                  
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded">
                    <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-1">Benefit:</h4>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">{alignment.benefit}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Integration Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Integrating Cosmic Wisdom
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <Star className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Cosmic Perspective
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Understanding universal scales and laws develops humility, wonder, and logical thinking.
                </p>
              </div>
              <div className="text-center">
                <Globe className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Earthly Application
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Applying cosmic principles to daily life creates harmony, efficiency, and optimal functioning.
                </p>
              </div>
              <div className="text-center">
                <Heart className="h-12 w-12 text-red-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Human Development
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Paradise principles guide optimal human development and the creation of ideal conditions.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Align with Cosmic Intelligence
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              The heavens and earth demonstrate perfect order, intelligent design, and optimal functioning. 
              By understanding and applying these cosmic principles, we can optimize our own development 
              and create harmonious, fulfilling lives.
            </p>
            <div className="flex justify-center space-x-8">
              <div className="text-center">
                <Star className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Observe & Learn</p>
              </div>
              <div className="text-center">
                <Brain className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Understand & Apply</p>
              </div>
              <div className="text-center">
                <Zap className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Optimize & Thrive</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
