import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// For client-side usage
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// For client components
export const createSupabaseClient = () => createBrowserClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: 'user' | 'moderator' | 'admin'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'user' | 'moderator' | 'admin'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'user' | 'moderator' | 'admin'
          created_at?: string
          updated_at?: string
        }
      }
      forum_categories: {
        Row: {
          id: string
          name: string
          description: string | null
          slug: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          slug: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          slug?: string
          created_at?: string
        }
      }
      forum_posts: {
        Row: {
          id: string
          title: string
          content: string
          author_id: string
          category_id: string
          created_at: string
          updated_at: string
          is_pinned: boolean
          is_locked: boolean
        }
        Insert: {
          id?: string
          title: string
          content: string
          author_id: string
          category_id: string
          created_at?: string
          updated_at?: string
          is_pinned?: boolean
          is_locked?: boolean
        }
        Update: {
          id?: string
          title?: string
          content?: string
          author_id?: string
          category_id?: string
          created_at?: string
          updated_at?: string
          is_pinned?: boolean
          is_locked?: boolean
        }
      }
      forum_replies: {
        Row: {
          id: string
          content: string
          author_id: string
          post_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          content: string
          author_id: string
          post_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          content?: string
          author_id?: string
          post_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      live_stream_sessions: {
        Row: {
          id: string
          title: string
          description: string | null
          is_active: boolean
          admin_id: string
          created_at: string
          ended_at: string | null
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          is_active?: boolean
          admin_id: string
          created_at?: string
          ended_at?: string | null
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          is_active?: boolean
          admin_id?: string
          created_at?: string
          ended_at?: string | null
        }
      }
      chat_messages: {
        Row: {
          id: string
          message: string
          user_id: string
          session_id: string
          created_at: string
          is_deleted: boolean
        }
        Insert: {
          id?: string
          message: string
          user_id: string
          session_id: string
          created_at?: string
          is_deleted?: boolean
        }
        Update: {
          id?: string
          message?: string
          user_id?: string
          session_id?: string
          created_at?: string
          is_deleted?: boolean
        }
      }
    }
  }
}
