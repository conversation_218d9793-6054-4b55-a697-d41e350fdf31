"use client";

import { useEffect, useState } from 'react';
import { Globe2, Languages, Info } from 'lucide-react';
import { Button } from './ui/button';
import { TranslationConsent } from './translation-consent';

declare global {
  interface Window {
    google: any;
    googleTranslateElementInit: () => void;
  }
}

/**
 * Google Translate Component
 * Provides automatic translation for 100+ languages with GDPR compliance
 */
export function GoogleTranslate() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [showConsent, setShowConsent] = useState(false);
  const [hasConsent, setHasConsent] = useState<boolean | null>(null);

  useEffect(() => {
    // Check for existing consent
    const consent = localStorage.getItem('google-translate-consent');
    if (consent) {
      const consentData = JSON.parse(consent);
      setHasConsent(consentData.granted);
    }

    // Check if Google Translate is loaded
    const checkGoogleTranslate = () => {
      if (typeof window !== 'undefined' && (window as any).google?.translate) {
        setIsLoaded(true);
      } else {
        // Retry after a short delay
        setTimeout(checkGoogleTranslate, 1000);
      }
    };

    checkGoogleTranslate();
  }, []);

  const toggleTranslate = () => {
    if (hasConsent === null) {
      // Show consent modal first
      setShowConsent(true);
    } else if (hasConsent) {
      // User has consented, show translate modal
      setIsVisible(!isVisible);
    } else {
      // User declined, show consent modal again
      setShowConsent(true);
    }
  };

  const handleConsent = (granted: boolean) => {
    setHasConsent(granted);
    if (granted) {
      setIsVisible(true);
    }
  };

  if (!isLoaded) {
    return null;
  }

  return (
    <>
      <div className="relative">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleTranslate}
          className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
          title="Google Translate - 100+ Languages"
        >
          <Languages className="h-4 w-4" />
          <span className="hidden sm:inline">Auto Translate</span>
        </Button>
      </div>

      {isVisible && hasConsent && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div 
            className="absolute inset-0 bg-black/50 backdrop-blur-sm" 
            onClick={() => setIsVisible(false)}
          />
          
          <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 p-6 w-full max-w-md mx-auto">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <Globe2 className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Google Translate
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Translate to 100+ languages
                  </p>
                </div>
              </div>
              <button
                onClick={() => setIsVisible(false)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                aria-label="Close translation modal"
              >
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="flex items-start space-x-3 mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-blue-800 dark:text-blue-200">
                <p className="font-medium mb-2">Automatic Page Translation</p>
                <p className="text-sm leading-relaxed">
                  Select a language below to automatically translate the entire website. 
                  This works alongside our custom translations and supports 100+ languages worldwide.
                </p>
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Choose Translation Language:
              </label>
              <div 
                id="google_translate_element"
                className="google-translate-container"
              />
            </div>

            <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
              <p className="font-medium mb-1">Privacy Notice:</p>
              <p>Translation is provided by Google Translate. By using this feature, you agree to Google's privacy policy. Translations are processed securely and no personal data is stored.</p>
            </div>
          </div>
        </div>
      )}

      <TranslationConsent
        isOpen={showConsent}
        onClose={() => setShowConsent(false)}
        onConsent={handleConsent}
      />
    </>
  );
}

export function GoogleTranslateInfo() {
  return (
    <div className="text-xs text-gray-500 space-y-1">
      <div className="font-medium">Translation Options:</div>
      <div className="space-y-1">
        <div className="flex items-center justify-between">
          <span>Custom Translations:</span>
          <span className="text-blue-500">6 Languages</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Google Translate:</span>
          <span className="text-green-500">100+ Languages</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Total Coverage:</span>
          <span className="text-purple-500">Comprehensive</span>
        </div>
      </div>
      <div className="text-xs text-gray-400 mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
        Custom translations provide the best experience for supported languages.
        Google Translate offers automatic translation for all other languages.
      </div>
    </div>
  );
}

export const TRANSLATION_INFO = {
  custom: {
    name: "Custom Translations",
    languages: ["English", "العربية", "Français", "Español", "اردو", "Svenska"],
    count: 6,
    quality: "High",
    description: "Hand-crafted translations optimized for the website content"
  },
  google: {
    name: "Google Translate",
    languages: "100+ Languages",
    count: 100,
    quality: "Automatic",
    description: "Automatic translation powered by Google's AI translation service"
  },
  combined: {
    name: "Comprehensive Translation",
    description: "Best of both worlds - custom translations for supported languages, automatic translation for everything else",
    coverage: "Global"
  }
} as const;
