"use client";

import { useEffect, useState } from 'react';
import { Globe2, Languages, Info } from 'lucide-react';
import { Button } from './ui/button';

/**
 * Google Translate Integration Component
 * Provides automatic translation for 100+ languages alongside custom translations
 * 
 * Features:
 * - Automatic translation via Google Translate
 * - 100+ language support
 * - Works alongside existing custom translations
 * - Privacy-respecting implementation
 * - Mobile-friendly interface
 */
export function GoogleTranslate() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if Google Translate is loaded
    const checkGoogleTranslate = () => {
      if (typeof window !== 'undefined' && (window as any).google?.translate) {
        setIsLoaded(true);
      } else {
        // Retry after a short delay
        setTimeout(checkGoogleTranslate, 1000);
      }
    };

    checkGoogleTranslate();
  }, []);

  const toggleTranslate = () => {
    setIsVisible(!isVisible);
  };

  if (!isLoaded) {
    return null;
  }

  return (
    <div className="relative">
      {/* Toggle Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleTranslate}
        className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        title="Google Translate - 100+ Languages"
      >
        <Languages className="h-4 w-4" />
        <span className="hidden sm:inline">Auto Translate</span>
      </Button>

      {/* Google Translate Widget */}
      {isVisible && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsVisible(false)}
          />
          
          {/* Translate Panel */}
          <div className="absolute right-0 top-full mt-2 z-20 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 min-w-[300px]">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <Globe2 className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-gray-900 dark:text-white">
                  Google Translate
                </span>
              </div>
              <button
                onClick={() => setIsVisible(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                ×
              </button>
            </div>
            
            {/* Info Message */}
            <div className="flex items-start space-x-2 mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-sm">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-blue-800 dark:text-blue-200">
                <p className="font-medium mb-1">Automatic Translation</p>
                <p>Translates the entire page to 100+ languages. Works alongside our custom translations.</p>
              </div>
            </div>

            {/* Google Translate Element */}
            <div 
              id="google_translate_element"
              className="google-translate-container"
            />
            
            {/* Custom Styles for Google Translate */}
            <style jsx>{`
              .google-translate-container :global(.goog-te-gadget) {
                font-family: inherit !important;
                font-size: 14px !important;
              }
              
              .google-translate-container :global(.goog-te-gadget-simple) {
                background-color: transparent !important;
                border: 1px solid #e5e7eb !important;
                border-radius: 6px !important;
                padding: 8px 12px !important;
                font-size: 14px !important;
                color: #374151 !important;
              }
              
              .google-translate-container :global(.goog-te-gadget-simple:hover) {
                border-color: #3b82f6 !important;
              }
              
              .google-translate-container :global(.goog-te-gadget-icon) {
                display: none !important;
              }
              
              .google-translate-container :global(.goog-te-gadget-simple .goog-te-menu-value) {
                color: #374151 !important;
              }
              
              .google-translate-container :global(.goog-te-gadget-simple .goog-te-menu-value span) {
                color: #6b7280 !important;
              }
              
              /* Dark mode styles */
              :global(.dark) .google-translate-container :global(.goog-te-gadget-simple) {
                background-color: #374151 !important;
                border-color: #4b5563 !important;
                color: #f9fafb !important;
              }
              
              :global(.dark) .google-translate-container :global(.goog-te-gadget-simple .goog-te-menu-value) {
                color: #f9fafb !important;
              }
              
              :global(.dark) .google-translate-container :global(.goog-te-gadget-simple .goog-te-menu-value span) {
                color: #d1d5db !important;
              }
            `}</style>
          </div>
        </>
      )}
    </div>
  );
}

/**
 * Google Translate Status Component
 * Shows information about translation capabilities
 */
export function GoogleTranslateInfo() {
  return (
    <div className="text-xs text-gray-500 space-y-1">
      <div className="font-medium">Translation Options:</div>
      <div className="space-y-1">
        <div className="flex items-center justify-between">
          <span>Custom Translations:</span>
          <span className="text-blue-500">6 Languages</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Google Translate:</span>
          <span className="text-green-500">100+ Languages</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Total Coverage:</span>
          <span className="text-purple-500">Comprehensive</span>
        </div>
      </div>
      <div className="text-xs text-gray-400 mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
        Custom translations provide the best experience for supported languages.
        Google Translate offers automatic translation for all other languages.
      </div>
    </div>
  );
}

/**
 * Translation Configuration
 * Information about available translation methods
 */
export const TRANSLATION_INFO = {
  custom: {
    name: "Custom Translations",
    languages: ["English", "العربية", "Français", "Español", "اردو", "Svenska"],
    count: 6,
    quality: "High",
    description: "Hand-crafted translations optimized for the website content"
  },
  google: {
    name: "Google Translate",
    languages: "100+ Languages",
    count: 100,
    quality: "Automatic",
    description: "Automatic translation powered by Google's AI translation service"
  },
  combined: {
    name: "Comprehensive Translation",
    description: "Best of both worlds - custom translations for supported languages, automatic translation for everything else",
    coverage: "Global"
  }
} as const;
