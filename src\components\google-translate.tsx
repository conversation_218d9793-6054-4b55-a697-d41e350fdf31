"use client";

import { useEffect, useRef } from "react";

declare global {
  interface Window {
    google: any;
    googleTranslateElementInit: () => void;
  }
}

/**
 * Google Translate Component — Safe Singleton
 */
export function GoogleTranslate() {
  const initializedRef = useRef(false); // ensures one-time init

  useEffect(() => {
    if (typeof window === "undefined") return;
    if (initializedRef.current) return;

    const container = document.getElementById("google_translate_element");

    // Don't proceed if container is missing or already has widget
    if (!container || container.innerHTML.trim() !== "") return;

    // Global init function
    window.googleTranslateElementInit = function () {
      try {
        if (container && container.innerHTML.trim() === "") {
          new window.google.translate.TranslateElement(
            {
              pageLanguage: "en",
              layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
            },
            "google_translate_element"
          );
        }
      } catch (error) {
        console.error("Google Translate initialization error:", error);
      }
    };

    // Load script if not already loaded
    if (!document.querySelector('script[src*="translate.google.com"]')) {
      const script = document.createElement("script");
      script.src =
        "//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit";
      script.async = true;
      script.onerror = () =>
        console.error("Failed to load Google Translate script");
      document.head.appendChild(script);
    } else if (window.google?.translate) {
      window.googleTranslateElementInit(); // If already loaded
    }

    initializedRef.current = true;

    return () => {
      delete (window as any).googleTranslateElementInit;
    };
  }, []);

  return (
    <div
      id="google_translate_element"
      className="inline-block"
      style={{ minWidth: '80px', minHeight: '32px' }}
    />
  );
}
