"use client";

import { useEffect, useState } from 'react';
import { Globe2, Languages, Check, X, ChevronDown } from 'lucide-react';
import { Button } from './ui/button';

declare global {
  interface Window {
    google: any;
    googleTranslateElementInit: () => void;
  }
}

interface Language {
  code: string;
  name: string;
  nativeName: string;
}

const POPULAR_LANGUAGES: Language[] = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'ar', name: 'Arabic', nativeName: 'العربية' },
  { code: 'es', name: 'Spanish', nativeName: 'Español' },
  { code: 'fr', name: 'French', nativeName: 'Français' },
  { code: 'de', name: 'German', nativeName: 'Deutsch' },
  { code: 'it', name: 'Italian', nativeName: 'Italiano' },
  { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
  { code: 'ru', name: 'Russian', nativeName: 'Русский' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語' },
  { code: 'ko', name: 'Korean', nativeName: '한국어' },
  { code: 'zh', name: 'Chinese', nativeName: '中文' },
  { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' },
  { code: 'ur', name: 'Urdu', nativeName: 'اردو' },
  { code: 'sv', name: 'Swedish', nativeName: 'Svenska' }
];

/**
 * Google Translate Component
 * Fetches Google's own translation with consent and language selection
 */
export function GoogleTranslate() {
  const [showConsent, setShowConsent] = useState(false);
  const [hasConsent, setHasConsent] = useState<boolean | null>(null);
  const [showLanguages, setShowLanguages] = useState(false);
  const [isTranslating, setIsTranslating] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<string>('en');

  useEffect(() => {
    // Check for existing consent
    const consent = localStorage.getItem('google-translate-consent');
    if (consent) {
      const consentData = JSON.parse(consent);
      setHasConsent(consentData.granted);
    }
  }, []);

  // Load Google Translate script when consent is given
  useEffect(() => {
    if (hasConsent && typeof window !== 'undefined') {
      if (!document.querySelector('script[src*="translate.google.com"]')) {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = '//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
        document.head.appendChild(script);

        // Initialize Google Translate
        (window as any).googleTranslateElementInit = function() {
          new (window as any).google.translate.TranslateElement({
            pageLanguage: 'auto',
            includedLanguages: POPULAR_LANGUAGES.map(lang => lang.code).join(','),
            layout: (window as any).google.translate.TranslateElement.InlineLayout.SIMPLE,
            autoDisplay: false,
            multilanguagePage: true,
            gaTrack: false,
            gaId: null
          }, 'google_translate_element');
        };
      }
    }
  }, [hasConsent]);

  const handleTranslateClick = () => {
    if (hasConsent === null) {
      setShowConsent(true);
    } else if (hasConsent) {
      setShowLanguages(!showLanguages);
    } else {
      setShowConsent(true);
    }
  };

  const handleConsent = (granted: boolean) => {
    const consentData = { granted, timestamp: Date.now(), version: '1.0' };
    localStorage.setItem('google-translate-consent', JSON.stringify(consentData));
    setHasConsent(granted);
    setShowConsent(false);

    if (granted) {
      setShowLanguages(true);
    }
  };

  const translateToLanguage = (languageCode: string) => {
    setIsTranslating(true);
    setCurrentLanguage(languageCode);

    // Use Google Translate to translate the page
    if ((window as any).google?.translate) {
      const translateElement = document.querySelector('#google_translate_element select') as HTMLSelectElement;
      if (translateElement) {
        translateElement.value = languageCode;
        translateElement.dispatchEvent(new Event('change'));
      }
    }

    setShowLanguages(false);
    setTimeout(() => setIsTranslating(false), 2000);
  };

  const resetTranslation = () => {
    setCurrentLanguage('en');
    const translateElement = document.querySelector('#google_translate_element select') as HTMLSelectElement;
    if (translateElement) {
      translateElement.value = '';
      translateElement.dispatchEvent(new Event('change'));
    }
  };

  return (
    <>
      <div className="relative">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleTranslateClick}
          className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
          title="Google Translate - 100+ Languages"
        >
          <Languages className="h-4 w-4" />
          <span className="hidden sm:inline">
            {isTranslating ? 'Translating...' : 'Translate'}
          </span>
          {hasConsent && <ChevronDown className="h-3 w-3" />}
        </Button>

        {/* Language Selection Dropdown */}
        {showLanguages && hasConsent && (
          <div className="absolute top-full right-0 mt-2 z-50 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 min-w-64 max-h-96 overflow-y-auto">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">Select Language</h3>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Choose a language to translate the entire website
              </p>
            </div>

            <div className="p-2">
              {/* Reset to Original */}
              <button
                onClick={resetTranslation}
                className="w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between text-sm"
              >
                <span>Original (English)</span>
                {currentLanguage === 'en' && <Check className="h-4 w-4 text-green-600" />}
              </button>

              <div className="border-t border-gray-200 dark:border-gray-700 my-2"></div>

              {/* Language Options */}
              {POPULAR_LANGUAGES.filter(lang => lang.code !== 'en').map((language) => (
                <button
                  key={language.code}
                  onClick={() => translateToLanguage(language.code)}
                  className="w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between text-sm"
                >
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {language.nativeName}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {language.name}
                    </div>
                  </div>
                  {currentLanguage === language.code && <Check className="h-4 w-4 text-green-600" />}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Hidden Google Translate Element */}
        {hasConsent && (
          <div id="google_translate_element" style={{ display: 'none' }} />
        )}
      </div>

      {/* Translation Status */}
      {isTranslating && (
        <div className="fixed top-4 right-4 z-50 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
          <span>Translating page...</span>
        </div>
      )}

      {/* Privacy Notice */}
      {showLanguages && hasConsent && (
        <div className="fixed bottom-4 right-4 z-40 max-w-sm">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3">
            <div className="text-xs text-gray-600 dark:text-gray-300">
              <p className="font-medium mb-1">Privacy Notice:</p>
              <p>Translation is provided by Google Translate. By using this feature, you agree to Google's privacy policy. Translations are processed securely and no personal data is stored.</p>
            </div>
          </div>
        </div>
      )}

      {/* Consent Modal */}
      {showConsent && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setShowConsent(false)} />

          <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 p-6 w-full max-w-md mx-auto">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Globe2 className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Google Translate
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Translate to 100+ languages
                </p>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                We use Google Translate to provide automatic translation of our website content.
                This service may collect and process data according to Google's privacy policy.
              </p>

              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 mb-4">
                <p className="text-xs text-blue-800 dark:text-blue-200">
                  <strong>Privacy Notice:</strong> Translation is provided by Google Translate.
                  By using this feature, you agree to Google's privacy policy. Translations are
                  processed securely and no personal data is stored.
                </p>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                onClick={() => handleConsent(false)}
                variant="outline"
                className="flex-1 flex items-center justify-center space-x-2"
              >
                <X className="h-4 w-4" />
                <span>Decline</span>
              </Button>
              <Button
                onClick={() => handleConsent(true)}
                className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700"
              >
                <Check className="h-4 w-4" />
                <span>Accept</span>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Google Translate Styling */}
      <style jsx global>{`
        .goog-te-banner-frame {
          display: none !important;
        }

        body {
          top: 0 !important;
        }

        .goog-te-banner-frame.skiptranslate {
          display: none !important;
        }

        body.goog-te-compat-mode {
          top: 0 !important;
        }
      `}</style>
    </>
  );
}

export function GoogleTranslateInfo() {
  return (
    <div className="text-xs text-gray-500 space-y-1">
      <div className="font-medium">Translation Options:</div>
      <div className="space-y-1">
        <div className="flex items-center justify-between">
          <span>Custom Translations:</span>
          <span className="text-blue-500">6 Languages</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Google Translate:</span>
          <span className="text-green-500">100+ Languages</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Total Coverage:</span>
          <span className="text-purple-500">Comprehensive</span>
        </div>
      </div>
      <div className="text-xs text-gray-400 mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
        Custom translations provide the best experience for supported languages.
        Google Translate offers automatic translation for all other languages.
      </div>
    </div>
  );
}

export const TRANSLATION_INFO = {
  custom: {
    name: "Custom Translations",
    languages: ["English", "العربية", "Français", "Español", "اردو", "Svenska"],
    count: 6,
    quality: "High",
    description: "Hand-crafted translations optimized for the website content"
  },
  google: {
    name: "Google Translate",
    languages: "100+ Languages",
    count: 100,
    quality: "Automatic",
    description: "Automatic translation powered by Google's AI translation service"
  },
  combined: {
    name: "Comprehensive Translation",
    description: "Best of both worlds - custom translations for supported languages, automatic translation for everything else",
    coverage: "Global"
  }
} as const;
