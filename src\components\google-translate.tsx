"use client";

import { useEffect, useState } from 'react';
import { Globe2, Languages, Info } from 'lucide-react';
import { Button } from './ui/button';
import { TranslationConsent } from './translation-consent';

declare global {
  interface Window {
    google: any;
    googleTranslateElementInit: () => void;
  }
}

/**
 * Google Translate Component
 * Provides automatic translation for 100+ languages with GDPR compliance
 */
export function GoogleTranslate() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [showConsent, setShowConsent] = useState(false);
  const [hasConsent, setHasConsent] = useState<boolean | null>(null);

  useEffect(() => {
    // Check for existing consent
    const consent = localStorage.getItem('google-translate-consent');
    if (consent) {
      const consentData = JSON.parse(consent);
      setHasConsent(consentData.granted);
    }

    // Check if Google Translate is loaded
    const checkGoogleTranslate = () => {
      if (typeof window !== 'undefined' && (window as any).google?.translate) {
        setIsLoaded(true);
      } else {
        // Retry after a short delay
        setTimeout(checkGoogleTranslate, 1000);
      }
    };

    checkGoogleTranslate();
  }, []);

  // Initialize Google Translate when consent is given
  useEffect(() => {
    if (hasConsent && typeof window !== 'undefined') {
      // Load Google Translate script if not already loaded
      if (!document.querySelector('script[src*="translate.google.com"]')) {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = '//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
        document.head.appendChild(script);
      }

      // Initialize Google Translate Element with automatic detection and screen optimization
      (window as any).googleTranslateElementInit = function() {
        new (window as any).google.translate.TranslateElement({
          pageLanguage: 'auto', // Automatic language detection
          includedLanguages: 'ar,en,es,fr,sv,ur,de,it,pt,ru,ja,ko,zh,hi,tr,pl,nl,da,no,fi,he,th,vi,id,ms,tl,sw,am,bn,gu,kn,ml,mr,ne,or,pa,si,ta,te,af,sq,az,be,bg,ca,hr,cs,et,ka,hu,is,ga,lv,lt,mk,mt,ro,sr,sk,sl,uk,cy,eu,gl,lb,mi,sm,gd,so,st,su,zu',
          layout: (window as any).google.translate.TranslateElement.InlineLayout.HORIZONTAL, // Screen optimized layout
          autoDisplay: true, // Automatic display optimization
          multilanguagePage: true, // Support for multilingual content
          gaTrack: false, // Privacy-respecting (no Google Analytics tracking)
          gaId: null // No tracking ID
        }, 'google_translate_element');
      };

      // Initialize if Google Translate is already loaded
      if ((window as any).google?.translate) {
        (window as any).googleTranslateElementInit();
      }
    }
  }, [hasConsent]);

  const toggleTranslate = () => {
    if (hasConsent === null) {
      // Show consent modal first
      setShowConsent(true);
    } else if (hasConsent) {
      // User has consented, show Google Translate with automatic detection
      setIsVisible(!isVisible);

      // Trigger Google Translate's automatic detection and screen optimization
      if (isVisible === false) {
        setTimeout(() => {
          // Try to trigger Google's automatic language detection
          const translateElement = document.querySelector('#google_translate_element select') as HTMLSelectElement;
          if (translateElement) {
            // Let Google automatically detect and optimize for screen
            const event = new Event('change', { bubbles: true });
            translateElement.dispatchEvent(event);
          }

          // Show automatic detection notification
          const notification = document.createElement('div');
          notification.innerHTML = `
            <div style="position: fixed; top: 20px; right: 20px; background: #4285f4; color: white; padding: 12px 16px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 10000; font-family: Arial, sans-serif; font-size: 14px;">
              🌐 Detecting page language automatically...
            </div>
          `;
          document.body.appendChild(notification);

          // Remove notification after 3 seconds
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification);
            }
          }, 3000);
        }, 500);
      }
    } else {
      // User declined, show consent modal again
      setShowConsent(true);
    }
  };

  const handleConsent = (granted: boolean) => {
    setHasConsent(granted);
    if (granted) {
      setIsVisible(true);
    }
  };

  if (!isLoaded) {
    return null;
  }

  return (
    <>
      <div className="relative">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleTranslate}
          className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
          title="Google Translate - 100+ Languages"
        >
          <Languages className="h-4 w-4" />
          <span className="hidden sm:inline">Auto Translate</span>
        </Button>

        {/* Google Translate Element - Uses Google's own popup */}
        {hasConsent && (
          <div
            id="google_translate_element"
            className="absolute top-full right-0 mt-2 z-50"
            style={{ display: isVisible ? 'block' : 'none' }}
          />
        )}
      </div>

      {/* Privacy Notice */}
      {isVisible && hasConsent && (
        <div className="fixed bottom-4 right-4 z-40 max-w-sm">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-start space-x-2">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-gray-600 dark:text-gray-300">
                <p className="font-medium mb-1">Privacy Notice:</p>
                <p>Translation is provided by Google Translate. By using this feature, you agree to Google's privacy policy. Translations are processed securely and no personal data is stored.</p>
              </div>
              <button
                onClick={() => setIsVisible(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ml-2"
                aria-label="Close privacy notice"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}

      <TranslationConsent
        isOpen={showConsent}
        onClose={() => setShowConsent(false)}
        onConsent={handleConsent}
      />

      {/* Google Translate Styling */}
      <style jsx global>{`
        /* Hide Google Translate banner */
        .goog-te-banner-frame {
          display: none !important;
        }

        /* Style the translate element */
        #google_translate_element {
          background: white;
          border-radius: 8px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          border: 1px solid #e5e7eb;
          padding: 8px;
          min-width: 200px;
        }

        /* Dark mode styles */
        .dark #google_translate_element {
          background: #374151;
          border-color: #4b5563;
        }

        /* Style the select dropdown */
        #google_translate_element select {
          background: transparent;
          border: none;
          font-size: 14px;
          color: inherit;
          width: 100%;
          padding: 4px;
        }

        /* Hide Google branding in dropdown */
        .goog-te-gadget-simple .goog-te-menu-value span:first-child {
          display: none;
        }

        /* Style the dropdown arrow */
        .goog-te-gadget-simple .goog-te-menu-value:after {
          content: '▼';
          font-size: 10px;
          margin-left: 8px;
          color: #6b7280;
        }

        /* Remove Google Translate top frame */
        body {
          top: 0 !important;
        }

        /* Hide the Google Translate iframe */
        .goog-te-banner-frame.skiptranslate {
          display: none !important;
        }

        body.goog-te-compat-mode {
          top: 0 !important;
        }
      `}</style>
    </>
  );
}

export function GoogleTranslateInfo() {
  return (
    <div className="text-xs text-gray-500 space-y-1">
      <div className="font-medium">Translation Options:</div>
      <div className="space-y-1">
        <div className="flex items-center justify-between">
          <span>Custom Translations:</span>
          <span className="text-blue-500">6 Languages</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Google Translate:</span>
          <span className="text-green-500">100+ Languages</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Total Coverage:</span>
          <span className="text-purple-500">Comprehensive</span>
        </div>
      </div>
      <div className="text-xs text-gray-400 mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
        Custom translations provide the best experience for supported languages.
        Google Translate offers automatic translation for all other languages.
      </div>
    </div>
  );
}

export const TRANSLATION_INFO = {
  custom: {
    name: "Custom Translations",
    languages: ["English", "العربية", "Français", "Español", "اردو", "Svenska"],
    count: 6,
    quality: "High",
    description: "Hand-crafted translations optimized for the website content"
  },
  google: {
    name: "Google Translate",
    languages: "100+ Languages",
    count: 100,
    quality: "Automatic",
    description: "Automatic translation powered by Google's AI translation service"
  },
  combined: {
    name: "Comprehensive Translation",
    description: "Best of both worlds - custom translations for supported languages, automatic translation for everything else",
    coverage: "Global"
  }
} as const;
