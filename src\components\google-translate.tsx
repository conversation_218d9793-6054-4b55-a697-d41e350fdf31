"use client";

import { useEffect, useRef, useState } from "react";

declare global {
  interface Window {
    google: any;
    googleTranslateElementInit?: () => void;
  }
}

/**
 * Google Translate Popup Consent Component
 */
export function GoogleTranslatePopup() {
  const initializedRef = useRef(false);
  const [showConsent, setShowConsent] = useState(false);
  const [showWidget, setShowWidget] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load and initialize Google Translate widget only once when showWidget becomes true
  useEffect(() => {
    if (!showWidget) return;
    if (typeof window === "undefined") return;
    if (initializedRef.current) return;

    setIsLoading(true);
    setError(null);

    // Wait for container to be available
    const initializeTranslate = () => {
      const container = document.getElementById("google_translate_element");
      if (!container) {
        console.warn("Google Translate container not found, retrying...");
        setTimeout(initializeTranslate, 100);
        return;
      }

      // Clear any existing content
      container.innerHTML = '';

      window.googleTranslateElementInit = () => {
        try {
          if (!window.google?.translate?.TranslateElement) {
            throw new Error("Google Translate API not available");
          }

          new window.google.translate.TranslateElement(
            {
              pageLanguage: "en",
              layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
              autoDisplay: false,
            },
            "google_translate_element"
          );

          setIsLoading(false);
          setError(null);
        } catch (error) {
          console.error("Google Translate initialization error:", error);
          setError("Failed to initialize translator");
          setIsLoading(false);
        }
      };

      // Check if script is already loaded
      const existingScript = document.querySelector('script[src*="translate.google.com/translate_a/element.js"]');

      if (!existingScript) {
        const script = document.createElement("script");
        script.src = "https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit";
        script.async = true;
        script.onload = () => {
          // Script loaded successfully
          if (!window.google?.translate) {
            setTimeout(() => window.googleTranslateElementInit?.(), 100);
          }
        };
        script.onerror = () => {
          console.error("Failed to load Google Translate script");
          setError("Failed to load translator");
          setIsLoading(false);
        };
        document.head.appendChild(script);
      } else if (window.google?.translate) {
        // If script already loaded, call init directly
        setTimeout(() => window.googleTranslateElementInit?.(), 100);
      } else {
        // Script exists but Google Translate not ready, wait and retry
        setTimeout(initializeTranslate, 500);
      }
    };

    // Start initialization with a small delay to ensure DOM is ready
    setTimeout(initializeTranslate, 50);
    initializedRef.current = true;

    // Cleanup function
    return () => {
      delete window.googleTranslateElementInit;
      setIsLoading(false);
      setError(null);
    };
  }, [showWidget]);

  const handleConsent = () => {
    setShowConsent(false);
    setShowWidget(true);
  };

  return (
    <>
      <button
        onClick={() => setShowConsent(true)}
        className="px-4 py-2 bg-blue-600 text-white rounded"
      >
        G-Translate site
      </button>

      {showConsent && (
        <div className="fixed bottom-6 right-6 bg-white border border-gray-300 p-4 rounded-lg shadow-xl z-50 max-w-sm w-full">
          <p className="text-sm text-gray-700 mb-3">
            To translate this page, we use Google Translate which may load cookies or third-party scripts.
            Do you want to continue?
          </p>
          <div className="flex justify-end gap-2">
            <button
              onClick={() => setShowConsent(false)}
              className="px-3 py-1 border border-gray-400 rounded text-sm"
            >
              Cancel
            </button>
            <button
              onClick={handleConsent}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm"
            >
              Yes, Continue
            </button>
          </div>
        </div>
      )}

      {showWidget && (
        <div className="fixed bottom-4 left-4 z-40">
          {isLoading && (
            <div className="bg-white border rounded px-3 py-2 shadow flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm text-gray-600">Loading translator...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded px-3 py-2 shadow">
              <span className="text-sm text-red-600">{error}</span>
              <button
                onClick={() => {
                  setError(null);
                  setShowWidget(false);
                  initializedRef.current = false;
                }}
                className="ml-2 text-xs text-red-500 underline"
              >
                Retry
              </button>
            </div>
          )}

          <div
            id="google_translate_element"
            className={`bg-white border rounded px-2 py-1 shadow ${isLoading || error ? 'hidden' : ''}`}
            style={{
              minWidth: 120,
              minHeight: 32,
              contain: 'layout style',
              willChange: 'transform'
            }}
          />
        </div>
      )}
    </>
  );
}
