"use client";

import { useEffect, useRef, useState } from "react";

declare global {
  interface Window {
    google: any;
    googleTranslateElementInit?: () => void;
  }
}

/**
 * Google Translate Popup Consent Component
 */
export function GoogleTranslatePopup() {
  const initializedRef = useRef(false);
  const [showConsent, setShowConsent] = useState(false);
  const [showWidget, setShowWidget] = useState(false);

  // Load and initialize Google Translate widget only once when showWidget becomes true
  useEffect(() => {
    if (!showWidget) return;
    if (typeof window === "undefined") return;
    if (initializedRef.current) return;

    const container = document.getElementById("google_translate_element");
    if (!container) {
      console.warn("Google Translate container not found");
      return;
    }

    window.googleTranslateElementInit = () => {
      try {
        new window.google.translate.TranslateElement(
          {
            pageLanguage: "en",
            layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
          },
          "google_translate_element"
        );
      } catch (error) {
        console.error("Google Translate initialization error:", error);
      }
    };

    // Check if script is already loaded
    if (!document.querySelector('script[src*="translate.google.com/translate_a/element.js"]')) {
      const script = document.createElement("script");
      script.src = "//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit";
      script.async = true;
      script.onerror = () => console.error("Failed to load Google Translate script");
      document.head.appendChild(script);
    } else if (window.google?.translate) {
      // If script already loaded, call init directly
      window.googleTranslateElementInit();
    }

    initializedRef.current = true;

    // Cleanup function: optional
    return () => {
      delete window.googleTranslateElementInit;
    };
  }, [showWidget]);

  const handleConsent = () => {
    setShowConsent(false);
    setShowWidget(true);
  };

  return (
    <>
      <button
        onClick={() => setShowConsent(true)}
        className="px-4 py-2 bg-blue-600 text-white rounded"
      >
        G-Translate site
      </button>

      {showConsent && (
        <div className="fixed bottom-6 right-6 bg-white border border-gray-300 p-4 rounded-lg shadow-xl z-50 max-w-sm w-full">
          <p className="text-sm text-gray-700 mb-3">
            To translate this page, we use Google Translate which may load cookies or third-party scripts.
            Do you want to continue?
          </p>
          <div className="flex justify-end gap-2">
            <button
              onClick={() => setShowConsent(false)}
              className="px-3 py-1 border border-gray-400 rounded text-sm"
            >
              Cancel
            </button>
            <button
              onClick={handleConsent}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm"
            >
              Yes, Continue
            </button>
          </div>
        </div>
      )}

      {showWidget && (
        <div
          id="google_translate_element"
          className="fixed bottom-4 left-4 bg-white border rounded px-2 py-1 shadow z-40"
          style={{
            minWidth: 120,
            minHeight: 32,
            contain: 'layout style',
            willChange: 'transform'
          }}
        />
      )}
    </>
  );
}
