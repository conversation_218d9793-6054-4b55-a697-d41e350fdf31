"use client";

import { useEffect } from 'react';

declare global {
  interface Window {
    google: any;
    googleTranslateElementInit: () => void;
  }
}

/**
 * Google Translate Component
 * Simple Google Translate widget integration based on HTML example
 */
export function GoogleTranslate() {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Define the Google Translate initialization function globally
      (window as any).googleTranslateElementInit = function() {
        new (window as any).google.translate.TranslateElement(
          {
            pageLanguage: 'en',
            layout: (window as any).google.translate.TranslateElement.InlineLayout.SIMPLE,
          },
          'google_translate_element'
        );
      };

      // Load the Google Translate script if not already loaded
      if (!document.querySelector('script[src*="translate.google.com"]')) {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.async = true; // Load asynchronously to not block rendering
        script.src = '//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
        document.head.appendChild(script);
      } else if ((window as any).google?.translate) {
        // If script already loaded, initialize immediately
        (window as any).googleTranslateElementInit();
      }
    }

    // Cleanup on unmount — set to undefined instead of delete to avoid errors
    return () => {
      (window as any).googleTranslateElementInit = undefined;
    };
  }, []);

  return <div id="google_translate_element"></div>;
}
