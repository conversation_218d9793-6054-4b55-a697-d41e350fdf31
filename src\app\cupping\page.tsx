"use client";

import {
  Heart,
  Droplets,
  Shield,
  Zap,
  <PERSON>ert<PERSON><PERSON>gle,
  CheckCircle,
  Activity,
  Brain,
  Moon,
  Wind,
  Thermometer,
  Target,
} from "lucide-react";
import { useCuppingTranslations } from "@/hooks/use-translations";
import { PageLegalFooter } from "@/components/legal-disclaimers";

const benefits = [
  {
    icon: Droplets,
    title: "Better Blood Flow",
    description:
      "Improves local blood flow through suction therapy, enhances microcirculation, and supports healthy blood pressure. Delivers oxygen and nutrients to tissues while promoting natural healing.",
    facts: [
      "Increases blood flow to treated areas",
      "Supports healthy circulation",
      "Enhances oxygen delivery",
      "Promotes tissue healing",
    ],
  },
  {
    icon: Shield,
    title: "Immune System Support",
    description:
      "Strengthens immune response by stimulating white blood cell activity and enhancing natural defense mechanisms. Supports the body's ability to fight infections.",
    facts: [
      "Supports immune cell activity",
      "Helps natural defenses",
      "Supports infection resistance",
      "Helps healing response",
    ],
  },
  {
    icon: Zap,
    title: "Pain Relief & Recovery",
    description:
      "Provides natural pain relief for chronic conditions through improved circulation and reduced inflammation. Accelerates muscle recovery and healing.",
    facts: [
      "Natural pain relief",
      "Reduces inflammation",
      "Supports recovery",
      "Helps mobility",
    ],
  },
  {
    icon: Heart,
    title: "Stress & Anxiety Relief",
    description:
      "Activates the parasympathetic nervous system, naturally reducing stress hormones and promoting relaxation. Supports emotional well-being.",
    facts: [
      "Reduces stress hormones",
      "Promotes relaxation",
      "Improves mood",
      "Supports emotional balance",
    ],
  },
  {
    icon: Brain,
    title: "Mental Clarity & Focus",
    description:
      "Helps thinking clearly through better brain blood flow. Supports mental alertness, focus, and overall brain health.",
    facts: [
      "Helps mental clarity",
      "Supports focus",
      "Supports brain health",
      "Helps alertness",
    ],
  },
  {
    icon: Moon,
    title: "Sleep Quality Enhancement",
    description:
      "Promotes better sleep quality by regulating the nervous system and reducing stress. Helps with insomnia and sleep disorders naturally.",
    facts: [
      "Improves sleep quality",
      "Reduces sleep onset time",
      "Promotes deep sleep",
      "Regulates sleep cycles",
    ],
  },
  {
    icon: Wind,
    title: "Respiratory Function",
    description:
      "Supports respiratory health by improving lung function and reducing symptoms of breathing difficulties. Helps with asthma and bronchitis.",
    facts: [
      "Improves lung function",
      "Reduces breathing difficulties",
      "Supports respiratory health",
      "Enhances oxygen levels",
    ],
  },
  {
    icon: Thermometer,
    title: "Detoxification Process",
    description:
      "Stimulates lymphatic drainage and supports the body's natural detoxification processes. Promotes cellular cleansing and waste removal.",
    facts: [
      "Stimulates lymphatic drainage",
      "Supports natural detox",
      "Promotes cellular cleansing",
      "Enhances waste removal",
    ],
  },
];

const procedures = [
  "Consultation and health assessment",
  "Preparation of sterile cupping equipment",
  "Identification of cupping points",
  "Application of cups using suction method",
  "Monitoring during treatment (15-20 minutes)",
  "Safe removal of cups and aftercare",
];

const precautions = [
  "Always consult with a qualified practitioner",
  "Ensure sterile equipment and proper hygiene",
  "Not recommended during pregnancy",
  "Avoid if you have bleeding disorders",
  "Do not perform on broken or infected skin",
  "Stay hydrated before and after treatment",
];

export default function CuppingPage() {
  const cuppingT = useCuppingTranslations();

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-pink-50 to-rose-50 dark:from-red-900/20 dark:via-pink-900/20 dark:to-rose-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Heart className="h-16 w-16 text-red-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              {cuppingT.title()}
            </h1>
            <div className="text-2xl md:text-3xl text-red-600 dark:text-red-400 mb-4 font-serif">
              {cuppingT.subtitle()}
            </div>
            <div className="max-w-4xl mx-auto space-y-4">
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Discover the science behind cupping therapy, an ancient healing
                practice used across cultures for thousands of years to promote
                wellness and natural healing.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Medical Disclaimer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-800 dark:text-red-300 mb-2">
            Important Medical Disclaimer
          </h3>
          <p className="text-red-700 dark:text-red-300 text-sm mb-3">
            The information about cupping therapy presented here is for
            educational purposes only and designed to promote inner peace and
            understanding of natural healing approaches. This should not replace
            professional medical advice, diagnosis, or treatment. Always consult
            with qualified healthcare providers before starting any new therapy.
            Cupping should only be performed by trained practitioners using
            sterile equipment.
          </p>
          <p className="text-red-700 dark:text-red-300 text-sm">
            <strong>Copyright Notice:</strong> All cupping therapy content,
            explanations, and educational materials are original works created
            for peaceful learning and wellness awareness. This content is
            designed to inspire understanding and promote beneficial knowledge
            acquisition for personal health consciousness and spiritual growth.
          </p>
        </div>
      </div>

      {/* Scientific Foundation */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="verse-container text-center">
          <div className="flex justify-center mb-4">
            <Heart className="h-8 w-8 text-red-600" />
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4">
            "Healing comes through natural methods: therapeutic suction,
            consuming natural honey, and controlled heat therapy - wisdom that
            has guided healers for millennia."
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Ancient Medical Wisdom
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* What is Cupping */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            What is Cupping Therapy?
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
              Cupping therapy is an ancient healing practice that involves
              placing cups on the skin to create suction. This traditional
              method has been used across cultures for thousands of years, from
              ancient Egypt and China to traditional European medicine,
              demonstrating its universal appeal and effectiveness.
            </p>
            <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
              The therapy works by drawing blood to the surface of the skin,
              improving circulation, reducing inflammation, and promoting the
              body's natural healing processes. Modern research supports many of
              the traditional claims about cupping's therapeutic benefits.
            </p>
          </div>
        </div>

        {/* Natural Healing & Wellness Information */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Natural Healing & Wellness Wisdom
          </h2>
          <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-8 border border-green-200 dark:border-green-800">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🩸</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">
                  Better Blood Flow
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Cupping helps blood move better in your body by gently pulling
                  it to the surface. When blood flows better, it brings good
                  things your body needs and takes away things it doesn't need.
                  This helps your body heal itself naturally and feel better.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌿</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">
                  Natural Cleaning
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Cupping helps pull out bad things that get stuck in your body.
                  It brings them to the surface where your body can get rid of
                  them naturally. This helps clean your body from the inside and
                  makes you feel more healthy and energetic.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💪</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">
                  Muscle Relief
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Cupping helps tight muscles relax by bringing more blood to
                  them. When muscles get more blood, they feel better and hurt
                  less. This helps your body recover faster when you're tired or
                  sore, and makes your muscles more flexible and comfortable.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🧘</span>
                  </div>
                </div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">
                  Feeling Calm & Peaceful
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Cupping is gentle and relaxing, which helps your body feel
                  calm and peaceful. When you feel relaxed, your body makes less
                  stress and more happy feelings. This helps your mind and body
                  work together better, making you feel good overall.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🔄</span>
                  </div>
                </div>
                <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">
                  Immune System Support
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  By improving circulation and reducing inflammation, cupping
                  naturally supports immune function. Better blood flow helps
                  immune cells move more efficiently throughout the body, while
                  reduced inflammation allows the immune system to focus on
                  protection and healing.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚖️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">
                  Energy Flow & Vitality
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Cupping helps restore natural energy flow by removing
                  blockages in circulation and tissue. This improved flow of
                  blood, lymph, and nutrients enhances overall vitality, mental
                  clarity, and physical energy, supporting the body's natural
                  state of health and balance.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Awe-Inspiring Development Facts */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            {cuppingT.factsTitle()}
          </h2>
          <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-8 border border-green-200 dark:border-green-800">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌟</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">
                  Divine Design of Healing
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Your body contains an amazing healing system that works
                  perfectly when supported naturally. Every cell knows exactly
                  how to repair and renew itself. This divine design shows the
                  perfect wisdom built into creation for health and wellness.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💎</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">
                  Heart Confirmation & Knowing
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  When you experience natural healing, your heart confirms the
                  truth of divine wisdom in creation. This inner knowing brings
                  peace and certainty that strengthens both your faith and your
                  understanding of how perfectly everything is designed.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🧠</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">
                  Intellectual Living Through Healing
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Understanding natural healing develops your intelligence about
                  how life works. You learn to think logically about health,
                  make wise choices, and live in alignment with natural laws
                  that bring wellness and peace to your daily life.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚖️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">
                  Perfect Balance & Alignment
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Natural healing brings your body, mind, and spirit into
                  perfect alignment. This balance reflects the divine harmony
                  found throughout creation. When you're aligned with natural
                  healing, you experience the peace that comes from living
                  according to divine wisdom.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌱</span>
                  </div>
                </div>
                <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">
                  Path to Inner Growth
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Each healing experience becomes a step on your path to inner
                  growth and understanding. As your body heals, your heart opens
                  to deeper wisdom, your mind becomes clearer, and your spirit
                  grows in gratitude and connection to the divine source of all
                  healing.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">✨</span>
                  </div>
                </div>
                <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">
                  Gateway to Enlightenment
                </h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Natural healing opens doorways to enlightenment by showing you
                  the perfect wisdom in creation. Each moment of healing becomes
                  a moment of recognition - seeing the divine hand in every
                  process of renewal, growth, and restoration in your life.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Benefits */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Scientifically Proven Health Benefits
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6"
                >
                  <div className="flex items-center mb-4">
                    <Icon className="h-8 w-8 text-red-600 mr-3" />

                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                      {benefit.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    {benefit.description}
                  </p>
                  {benefit.facts && (
                    <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3">
                      <h4 className="text-sm font-semibold text-red-800 dark:text-red-300 mb-2">
                        Clinical Evidence:
                      </h4>
                      <ul className="space-y-1">
                        {benefit.facts.map((fact, factIndex) => (
                          <li
                            key={factIndex}
                            className="text-sm text-red-700 dark:text-red-300 flex items-center"
                          >
                            <Target className="h-3 w-3 mr-2 flex-shrink-0" />

                            {fact}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Procedure */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Treatment Procedure
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Step-by-Step Process
                </h3>
                <ol className="space-y-3">
                  {procedures.map((step, index) => (
                    <li key={index} className="flex items-start">
                      <span className="flex-shrink-0 w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                        {index + 1}
                      </span>
                      <span className="text-gray-700 dark:text-gray-300">
                        {step}
                      </span>
                    </li>
                  ))}
                </ol>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Best Times for Cupping
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />

                    <span className="text-gray-700 dark:text-gray-300">
                      17th, 19th, and 21st of lunar month
                    </span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />

                    <span className="text-gray-700 dark:text-gray-300">
                      Early morning hours
                    </span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />

                    <span className="text-gray-700 dark:text-gray-300">
                      On empty stomach
                    </span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />

                    <span className="text-gray-700 dark:text-gray-300">
                      During moderate weather
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Precautions */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Important Precautions
          </h2>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-8">
            <div className="flex items-center mb-4">
              <AlertTriangle className="h-8 w-8 text-yellow-600 mr-3" />

              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Safety Guidelines
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {precautions.map((precaution, index) => (
                <div key={index} className="flex items-start">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />

                  <span className="text-gray-700 dark:text-gray-300">
                    {precaution}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Spiritual Aspect */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Spiritual Significance
          </h2>
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-8">
            <div className="text-center mb-6">
              <Heart className="h-12 w-12 text-blue-600 mx-auto mb-4" />

              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Healing Body and Soul
              </h3>
            </div>
            <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed text-center max-w-3xl mx-auto">
              In Islamic tradition, cupping is not just a physical treatment but
              also a spiritual practice. It represents trust in Allah's healing
              power and following the Sunnah of Prophet Muhammad (ﷺ). The
              practice encourages patience, gratitude, and reliance on Allah
              while taking practical steps for health.
            </p>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Seek Professional Treatment
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Always consult with qualified practitioners who follow proper
              hygiene and safety protocols. Cupping should complement, not
              replace, conventional medical treatment.
            </p>
            <div className="flex justify-center space-x-4">
              <div className="text-center">
                <Shield className="h-8 w-8 text-green-600 mx-auto mb-2" />

                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Certified Practitioners
                </p>
              </div>
              <div className="text-center">
                <Droplets className="h-8 w-8 text-blue-600 mx-auto mb-2" />

                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Sterile Equipment
                </p>
              </div>
              <div className="text-center">
                <Heart className="h-8 w-8 text-red-600 mx-auto mb-2" />

                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Holistic Care
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Legal Compliance Footer */}
      <PageLegalFooter
        disclaimers={['medical', 'educational']}
        showCopyright={true}
      />
    </div>
  );
}
