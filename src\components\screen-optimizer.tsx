"use client";

import { useEffect, useState } from 'react';
import { Monitor, Smartphone, Tablet } from 'lucide-react';

interface ScreenInfo {
  width: number;
  height: number;
  deviceType: 'mobile' | 'tablet' | 'desktop' | 'large-desktop';
  orientation: 'portrait' | 'landscape';
  pixelRatio: number;
  isTouchDevice: boolean;
}

export function ScreenOptimizer() {
  const [screenInfo, setScreenInfo] = useState<ScreenInfo | null>(null);
  const [isOptimizing, setIsOptimizing] = useState(false);

  const detectScreenInfo = (): ScreenInfo => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const pixelRatio = window.devicePixelRatio || 1;
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    const logicalWidth = width / pixelRatio;

    let deviceType: ScreenInfo['deviceType'] = 'desktop';
    if (logicalWidth <= 480) {
      deviceType = 'mobile';
    } else if (logicalWidth <= 900) {
      deviceType = 'tablet';
    } else if (logicalWidth >= 1440 && !isTouchDevice) {
      deviceType = 'large-desktop';
    }

    const orientation = width > height ? 'landscape' : 'portrait';

    return {
      width,
      height,
      deviceType,
      orientation,
      pixelRatio,
      isTouchDevice
    };
  };

  const optimizeForScreen = (info: ScreenInfo) => {
    setIsOptimizing(true);
    const root = document.documentElement;

    if (info.deviceType === 'mobile') {
      root.style.setProperty('--base-font-size', '14px');
      root.style.setProperty('--container-padding', '16px');
      root.style.setProperty('--grid-columns', '1');
    } else if (info.deviceType === 'tablet') {
      root.style.setProperty('--base-font-size', '15px');
      root.style.setProperty('--container-padding', '24px');
      root.style.setProperty('--grid-columns', '2');
    } else if (info.deviceType === 'large-desktop') {
      root.style.setProperty('--base-font-size', '18px');
      root.style.setProperty('--container-padding', '48px');
      root.style.setProperty('--grid-columns', '4');
    } else {
      root.style.setProperty('--base-font-size', '16px');
      root.style.setProperty('--container-padding', '32px');
      root.style.setProperty('--grid-columns', '3');
    }

    if (info.pixelRatio > 1.5) {
      root.style.setProperty('--image-quality', 'high');
      root.style.setProperty('--border-width', '0.5px');
    } else {
      root.style.setProperty('--image-quality', 'standard');
      root.style.setProperty('--border-width', '1px');
    }

    if (info.isTouchDevice) {
      root.style.setProperty('--touch-target-size', '44px');
      root.style.setProperty('--hover-effects', 'none');
    } else {
      root.style.setProperty('--touch-target-size', '32px');
      root.style.setProperty('--hover-effects', 'enabled');
    }

    if (info.orientation === 'portrait' && info.deviceType === 'mobile') {
      root.style.setProperty('--layout-direction', 'column');
      root.style.setProperty('--sidebar-position', 'bottom');
    } else {
      root.style.setProperty('--layout-direction', 'row');
      root.style.setProperty('--sidebar-position', 'side');
    }

    setTimeout(() => setIsOptimizing(false), 1000);
  };

  useEffect(() => {
    const handleResize = () => {
      const info = detectScreenInfo();
      setScreenInfo(info);
      optimizeForScreen(info);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, []);

  if (!screenInfo) return null;

  return (
    <>
      {isOptimizing && (
        <div className="fixed top-4 left-4 z-50 bg-blue-600 text-white px-3 py-2 rounded-lg shadow-lg flex items-center space-x-2 text-sm">
          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
          <span>Optimizing for {screenInfo.deviceType}...</span>
        </div>
      )}

      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 z-40 bg-gray-800 text-white p-3 rounded-lg text-xs space-y-1 max-w-xs">
          <div className="flex items-center space-x-2 font-medium">
            {screenInfo.deviceType === 'mobile' && <Smartphone className="w-4 h-4" />}
            {screenInfo.deviceType === 'tablet' && <Tablet className="w-4 h-4" />}
            {(screenInfo.deviceType === 'desktop' || screenInfo.deviceType === 'large-desktop') && <Monitor className="w-4 h-4" />}
            <span className="capitalize">{screenInfo.deviceType}</span>
          </div>
          <div>Resolution: {screenInfo.width} × {screenInfo.height}</div>
          <div>Orientation: {screenInfo.orientation}</div>
          <div>Pixel Ratio: {screenInfo.pixelRatio}x</div>
          <div>Touch: {screenInfo.isTouchDevice ? 'Yes' : 'No'}</div>
        </div>
      )}
    </>
  );
}

export function useScreenInfo() {
  const [screenInfo, setScreenInfo] = useState<ScreenInfo | null>(null);

  useEffect(() => {
    const detectScreenInfo = (): ScreenInfo => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const pixelRatio = window.devicePixelRatio || 1;
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const logicalWidth = width / pixelRatio;

      let deviceType: ScreenInfo['deviceType'] = 'desktop';
      if (logicalWidth <= 480) {
        deviceType = 'mobile';
      } else if (logicalWidth <= 900) {
        deviceType = 'tablet';
      } else if (logicalWidth >= 1440 && !isTouchDevice) {
        deviceType = 'large-desktop';
      }

      const orientation = width > height ? 'landscape' : 'portrait';

      return {
        width,
        height,
        deviceType,
        orientation,
        pixelRatio,
        isTouchDevice
      };
    };

    const handleResize = () => {
      setScreenInfo(detectScreenInfo());
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, []);

  return screenInfo;
}
