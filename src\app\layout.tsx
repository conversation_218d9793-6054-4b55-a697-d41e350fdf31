import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON>Consent } from "@/components/cookie-consent";
import { ScrollToTop } from "@/components/scroll-to-top";
import { config } from "@/lib/config";

const inter = Inter({ subsets: ["latin"] });
const amiri = Amiri({
  subsets: ["arabic", "latin"],
  weight: ["400", "700"],
  variable: "--font-amiri",
});

export const metadata: Metadata = {
  title: "Light Upon Light - Wisdom, Knowledge & Spiritual Growth",
  description:
    "A comprehensive educational platform for spiritual wisdom, natural healing, Quranic knowledge, and community learning. Discover the connection between nature, faith, and intellectual development.",
  keywords:
    "Islamic education, spiritual growth, natural healing, Quran, wisdom, meditation, nature therapy, community learning",
  authors: [{ name: "Light Upon Light" }],
  creator: "Light Upon Light",
  publisher: "Light Upon Light",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(config.site.url),
  alternates: {
    canonical: "/",
    languages: {
      "en-US": "/en",
      "ar-SA": "/ar",
      "fr-FR": "/fr",
      "es-ES": "/es",
      "ur-PK": "/ur",
    },
  },
  openGraph: {
    title: "Light Upon Light - Wisdom, Knowledge & Spiritual Growth",
    description:
      "A comprehensive educational platform for spiritual wisdom, natural healing, and community learning.",
    url: config.site.url,
    siteName: "Light Upon Light",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Light Upon Light - Spiritual Wisdom Platform",
      },
    ],

    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Light Upon Light - Wisdom, Knowledge & Spiritual Growth",
    description:
      "A comprehensive educational platform for spiritual wisdom, natural healing, and community learning.",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${amiri.variable}`}>
      <head>
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.svg" />
        <link rel="shortcut icon" href="/favicon.svg" />

        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#3b82f6" />


      </head>
      <body className={inter.className}>
        <Providers>
          <div className="min-h-screen flex flex-col">
            <Navigation />
            <main className="flex-1">{children}</main>
            <Footer />
          </div>
          <CookieConsent />
          <ScrollToTop />
        </Providers>
      </body>
    </html>
  );
}
