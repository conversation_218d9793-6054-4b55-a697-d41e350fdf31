import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON>Consent } from "@/components/cookie-consent";
import { ScrollToTop } from "@/components/scroll-to-top";
import { AnalyticsIntegration } from "@/components/analytics-integration";
import { ScreenOptimizer } from "@/components/screen-optimizer";
import { config } from "@/lib/config";
import Script from "next/script";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { Analytics } from "@vercel/analytics/next";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});
const amiri = <PERSON>i({
  subsets: ["arabic", "latin"],
  weight: ["400", "700"],
  variable: "--font-amiri",
});

export const metadata: Metadata = {
  title: "Light Upon Light - Wisdom, Knowledge & Spiritual Growth",
  description:
    "A comprehensive educational platform for spiritual wisdom, natural healing, Quranic knowledge, and community learning. Discover the connection between nature, faith, and intellectual development.",
  keywords:
    "Islamic education, spiritual growth, natural healing, Quran, wisdom, meditation, nature therapy, community learning",
  authors: [{ name: "Light Upon Light" }],
  creator: "Light Upon Light",
  publisher: "Light Upon Light",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(config.site.url),
  alternates: {
    canonical: "/",
    languages: {
      "en-US": "/en",
      "ar-SA": "/ar",
      "fr-FR": "/fr",
      "es-ES": "/es",
      "ur-PK": "/ur",
    },
  },
  openGraph: {
    title: "Light Upon Light - Wisdom, Knowledge & Spiritual Growth",
    description:
      "A comprehensive educational platform for spiritual wisdom, natural healing, and community learning.",
    url: config.site.url,
    siteName: "Light Upon Light",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Light Upon Light - Spiritual Wisdom Platform",
      },
    ],

    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Light Upon Light - Wisdom, Knowledge & Spiritual Growth",
    description:
      "A comprehensive educational platform for spiritual wisdom, natural healing, and community learning.",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${amiri.variable}`}>
      <head>
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.svg" />
        <link rel="shortcut icon" href="/favicon.svg" />

        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#3b82f6" />

        {/* Performance Optimizations */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://eyffbsjpeyahhqbjfybe.supabase.co" />
        <link rel="dns-prefetch" href="https://www.google-analytics.com" />
        <link rel="dns-prefetch" href="https://translate.googleapis.com" />
        <link rel="preconnect" href="https://translate.googleapis.com" />

        {/* Critical CSS - Above the fold styles */}
        <style dangerouslySetInnerHTML={{
          __html: `
            /* Critical CSS for above-the-fold content */
            .hero-section { min-height: 60vh; }
            .nav-loading { opacity: 0.7; }
            .content-loading { animation: pulse 2s infinite; }
            @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
          `
        }} />
      </head>
      <body className={inter.className}>
        <Providers>
          <div className="min-h-screen flex flex-col">
            <Navigation />
            <main className="flex-1" id="main-content">{children}</main>
            <Footer />
          </div>
          <CookieConsent />
          <ScrollToTop />
          <AnalyticsIntegration />
          <ScreenOptimizer />
        </Providers>

        {/* Performance monitoring script */}
        <Script
          id="performance-monitor"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              // Monitor Core Web Vitals
              if ('PerformanceObserver' in window) {
                // LCP
                new PerformanceObserver((list) => {
                  const entries = list.getEntries();
                  const lastEntry = entries[entries.length - 1];
                  console.log('LCP:', lastEntry.startTime);
                }).observe({entryTypes: ['largest-contentful-paint']});

                // FID
                new PerformanceObserver((list) => {
                  list.getEntries().forEach((entry) => {
                    console.log('FID:', entry.processingStart - entry.startTime);
                  });
                }).observe({entryTypes: ['first-input']});

                // CLS
                let clsValue = 0;
                new PerformanceObserver((list) => {
                  list.getEntries().forEach((entry) => {
                    if (!entry.hadRecentInput) {
                      clsValue += entry.value;
                    }
                  });
                  console.log('CLS:', clsValue);
                }).observe({entryTypes: ['layout-shift']});
              }
            `
          }}
        />

        {/* Accessibility enhancements */}
        <Script
          id="accessibility-enhancements"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              // Add skip link if not present
              if (!document.querySelector('.skip-link')) {
                const skipLink = document.createElement('a');
                skipLink.href = '#main-content';
                skipLink.textContent = 'Skip to main content';
                skipLink.className = 'skip-link sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white p-2 z-50 focus:z-50';
                document.body.insertBefore(skipLink, document.body.firstChild);
              }

              // Add ARIA live regions
              if (!document.querySelector('#aria-live-polite')) {
                const politeRegion = document.createElement('div');
                politeRegion.id = 'aria-live-polite';
                politeRegion.setAttribute('aria-live', 'polite');
                politeRegion.className = 'sr-only';
                document.body.appendChild(politeRegion);

                const assertiveRegion = document.createElement('div');
                assertiveRegion.id = 'aria-live-assertive';
                assertiveRegion.setAttribute('aria-live', 'assertive');
                assertiveRegion.className = 'sr-only';
                document.body.appendChild(assertiveRegion);
              }
            `
          }}
        />

        {/* Google Translate Integration */}
        <Script
          id="google-translate-init"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              function googleTranslateElementInit() {
                new google.translate.TranslateElement({
                  pageLanguage: 'en',
                  includedLanguages: 'ar,fr,es,ur,sv,de,it,pt,ru,zh,ja,ko,hi,tr,nl,pl,cs,da,no,fi,hu,ro,bg,hr,sk,sl,et,lv,lt,mt,ga,cy,eu,ca,gl,is,mk,sq,sr,bs,me,az,ka,hy,he,yi,fa,ps,ku,sd,bn,ne,si,my,th,lo,km,vi,tl,ms,id,jv,su,mg,ny,sn,yo,ig,ha,sw,zu,xh,st,tn,ts,ve,nr,ss,af,am,ti,om,so,rw,lg,ak,tw,ee,gaa,kri,bm,wo,ff,ful,mos,dyu,men,vai,kpe,tem,lim,gom,kok,mai,bho,new,sat,mni,lus,grt,doi,ks,sd,ur,pa,gu,or,as,ml,kn,te,ta,si,my,km,lo,th,vi,tl,ceb,hil,war,pag,pam,bik,nds,ilo,pag,akl,krj,mdh,tsg,mbb,tbw,bto,ivv,duo,hnn,pcc,bch,bcl,pag,war,hil,ceb,tgl,ilo,pam,pag,bik,akl,krj,war,hil,ceb',
                  layout: google.translate.TranslateElement.InlineLayout.HORIZONTAL,
                  autoDisplay: false,
                  multilanguagePage: true
                }, 'google_translate_element');
              }
            `
          }}
        />
        <Script
          src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"
          strategy="afterInteractive"
        />

        {/* Vercel Analytics - Privacy-respecting, no cookies */}
        <Analytics />

        {/* Vercel Speed Insights - Core Web Vitals monitoring */}
        <SpeedInsights />
      </body>
    </html>
  );
}
