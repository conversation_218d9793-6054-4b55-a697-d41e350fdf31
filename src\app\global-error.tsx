'use client'

import { useEffect } from 'react'
import { AlertTriangle, RefreshCw, Home, Mail } from 'lucide-react'
import { Button } from '@/components/ui/button'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log error to monitoring service
    console.error('Global error:', error)
    
    // In production, you would send this to your error monitoring service
    // Example: Sentry, LogRocket, etc.
    if (typeof window !== 'undefined') {
      // Log to analytics or error tracking
      try {
        // Example error tracking
        if ('gtag' in window && typeof (window as any).gtag === 'function') {
          (window as any).gtag('event', 'exception', {
            description: error.message,
            fatal: true
          })
        }
      } catch (e) {
        console.error('Error logging failed:', e)
      }
    }
  }, [error])

  const handleReload = () => {
    if (typeof window !== 'undefined') {
      window.location.reload()
    }
  }

  const handleGoHome = () => {
    if (typeof window !== 'undefined') {
      window.location.href = '/'
    }
  }

  return (
    <html>
      <body>
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center px-4">
          <div className="max-w-md w-full text-center">
            {/* Error Icon */}
            <div className="mb-8">
              <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
              <div className="w-24 h-1 bg-red-600 dark:bg-red-400 mx-auto rounded-full"></div>
            </div>

            {/* Error Message */}
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Something went wrong
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                We encountered an unexpected error. This has been logged and our team will investigate. 
                Please try refreshing the page or return to the home page.
              </p>
              
              {/* Error Details (only in development) */}
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-left">
                  <p className="text-sm font-mono text-red-600 dark:text-red-400 break-all">
                    {error.message}
                  </p>
                  {error.digest && (
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                      Error ID: {error.digest}
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <Button 
                onClick={reset}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>

              <div className="grid grid-cols-2 gap-3">
                <Button 
                  onClick={handleReload}
                  variant="outline" 
                  className="w-full"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reload Page
                </Button>
                
                <Button 
                  onClick={handleGoHome}
                  variant="outline" 
                  className="w-full"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </div>
            </div>

            {/* Contact Support */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                If this problem persists, please contact support:
              </p>
              <a 
                href="mailto:<EMAIL>?subject=Website Error Report"
                className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:underline text-sm"
              >
                <Mail className="h-4 w-4 mr-2" />
                <EMAIL>
              </a>
            </div>

            {/* Spiritual Quote for Comfort */}
            <div className="mt-8 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              <p className="text-sm text-gray-600 dark:text-gray-300 italic">
                "And it is He who created the heavens and earth in truth. 
                And the day He says, 'Be,' and it is, His word is the truth."
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                Quran 6:73
              </p>
            </div>
          </div>
        </div>
      </body>
    </html>
  )
}
