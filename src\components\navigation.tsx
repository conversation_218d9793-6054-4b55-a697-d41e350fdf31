"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useAuth } from "./providers";
import { useLanguage } from "./language-provider";
import { LanguageSelector } from "./language-selector";
import { GoogleTranslate } from "./google-translate";
import { Button } from "./ui/button";
import {
  Menu,
  X,
  LogOut,
  Settings,
  Heart,
  Leaf,
  Mountain,
  Sun,
  BookOpen,
  Users,
  Video,
  MessageCircle,
  Shield,
  ChevronDown,
} from "lucide-react";

const navigationItems = [
  { name: "Home", href: "/", icon: Sun },
  { name: "All Content", href: "/content", icon: BookOpen },
  { name: "Cupping", href: "/cupping", icon: Heart },
  { name: "Heart & Mind", href: "/heart-mind", icon: Heart },
  { name: "Plants & Healing", href: "/plants", icon: Leaf },
  { name: "Honey & Foods", href: "/honey", icon: Mountain },
  { name: "Nature Elements", href: "/nature-elements", icon: Mountain },
  { name: "Heavens & Earth", href: "/heavens-earth", icon: Sun },
  { name: "Quranic Logic", href: "/quran", icon: BookOpen },
  { name: "99 Divine Names", href: "/names-of-god", icon: BookOpen },
  { name: "Ancient Wisdom", href: "/wisdom", icon: BookOpen },
  { name: "Logic & Intelligence", href: "/logic", icon: BookOpen },
  { name: "Nature Law", href: "/nature-law", icon: Leaf },
  { name: "Therapy", href: "/therapy", icon: Heart },
  { name: "Patience & Virtue", href: "/patience", icon: Users },
  { name: "Quiz & Learning", href: "/quiz", icon: BookOpen },
  { name: "Community", href: "/community", icon: Users },
  { name: "Forum", href: "/forum", icon: MessageCircle },
  { name: "Live Stream", href: "/live", icon: Video },
];

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const { user, signOut } = useAuth();
  const { t } = useLanguage();

  // Check admin role
  useEffect(() => {
    const checkAdminRole = async () => {
      if (!user) {
        setIsAdmin(false);
        return;
      }

      try {
        const response = await fetch('/api/auth/check-role');
        const data = await response.json();
        setIsAdmin(data.success && ['admin', 'moderator'].includes(data.role));
      } catch (error) {
        console.error('Error checking admin role:', error);
        setIsAdmin(false);
      }
    };

    checkAdminRole();
  }, [user]);

  return (
    <nav className="bg-white dark:bg-gray-900 shadow-lg sticky top-0 z-50">
      <div className="nav-container">
        <div className="nav-content">
          <div className="flex items-center flex-shrink-0 min-w-0">
            <Link href="/" className="flex items-center space-x-2">
              <Sun className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600 flex-shrink-0" />
              <span className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white truncate">
                Light Upon Light
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2 xl:space-x-4 flex-1 justify-center overflow-visible">
            {navigationItems.slice(0, 6).map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center space-x-1 px-2 xl:px-3 py-2 rounded-md text-xs xl:text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors whitespace-nowrap"
                >
                  <Icon className="h-3 w-3 xl:h-4 xl:w-4 flex-shrink-0" />
                  <span className="hidden lg:inline">{item.name}</span>
                </Link>
              );
            })}

            {/* More dropdown - only show if there are more items */}
            {navigationItems.length > 6 && (
              <div className="relative">
                <button
                  type="button"
                  className="flex items-center space-x-1 px-2 xl:px-3 py-2 rounded-md text-xs xl:text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  aria-label="More navigation options"
                  onMouseEnter={() => setIsDropdownOpen(true)}
                  onMouseLeave={() => {
                    setTimeout(() => setIsDropdownOpen(false), 100);
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setIsDropdownOpen(!isDropdownOpen);
                  }}
                >
                  <span>More</span>
                  <ChevronDown className="w-3 h-3 ml-1" />
                </button>
                <div
                  className={`absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 transition-all duration-200 z-[9999] ${
                    isDropdownOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
                  }`}
                  onMouseEnter={() => setIsDropdownOpen(true)}
                  onMouseLeave={() => setIsDropdownOpen(false)}
                >
                  {navigationItems.slice(6).map((item) => {
                    const Icon = item.icon;
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors first:rounded-t-md last:rounded-b-md w-full"
                      >
                        <Icon className="h-4 w-4 flex-shrink-0" />
                        <span>{item.name}</span>
                      </Link>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {/* Auth Section - Desktop */}
          <div className="hidden lg:flex items-center space-x-4 flex-shrink-0">
            <div className="flex items-center space-x-2">
              <GoogleTranslate />
            </div>

            {/* Auth Section */}
            {user ? (
              <div className="flex items-center space-x-2">
                {isAdmin && (
                  <Link href="/admin/cms">
                    <Button variant="ghost" size="sm" className="text-purple-600 hover:text-purple-700">
                      <Shield className="h-4 w-4 mr-2" />
                      CMS Admin
                    </Button>
                  </Link>
                )}
                <Link href="/account">
                  <Button variant="ghost" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    {t("nav.account") || "Account"}
                  </Button>
                </Link>
                <Button variant="ghost" size="sm" onClick={signOut}>
                  <LogOut className="h-4 w-4 mr-2" />
                  {t("nav.signout") || "Sign Out"}
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link href="/auth/signin" className="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 border border-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800">
                  Sign In
                </Link>
                <Link href="/auth/signup" className="px-3 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md">
                  Sign Up
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button and translation */}
          <div className="lg:hidden flex items-center space-x-1 sm:space-x-2 mobile-nav flex-shrink-0">
            <div className="flex items-center space-x-1">
              <GoogleTranslate />
            </div>
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 touch-target"
              aria-label={isOpen ? "Close menu" : "Open menu"}
            >
              {isOpen ? (
                <X className="h-5 w-5 sm:h-6 sm:w-6" />
              ) : (
                <Menu className="h-5 w-5 sm:h-6 sm:w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div
          id="mobile-menu"
          className="lg:hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 max-w-full overflow-hidden"
        >
          <div className="px-2 pt-2 pb-3 space-y-1 max-h-screen overflow-y-auto">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 touch-target"
                  onClick={() => setIsOpen(false)}
                >
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  <span className="truncate">{item.name}</span>
                </Link>
              );
            })}

            <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
              {user ? (
                <>
                  {isAdmin && (
                    <Link
                      href="/admin/cms"
                      className="flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                      onClick={() => setIsOpen(false)}
                    >
                      <Shield className="h-5 w-5" />
                      <span>CMS Admin</span>
                    </Link>
                  )}
                  <Link
                    href="/account"
                    className="flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800"
                    onClick={() => setIsOpen(false)}
                  >
                    <Settings className="h-5 w-5" />
                    <span>Account Settings</span>
                  </Link>
                  <button
                    onClick={() => {
                      signOut();
                      setIsOpen(false);
                    }}
                    className="flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 w-full text-left"
                  >
                    <LogOut className="h-5 w-5" />
                    <span>Sign Out</span>
                  </button>
                </>
              ) : (
                <>
                  <Link
                    href="/auth/signin"
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800"
                    onClick={() => setIsOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link
                    href="/auth/signup"
                    className="block px-3 py-2 rounded-md text-base font-medium text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800"
                    onClick={() => setIsOpen(false)}
                  >
                    Sign Up
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}
