"use client";

import { useState } from "react";
import { useAuth } from "@/components/providers";
import { useData } from "@/components/data-provider";
import { useLanguage } from "@/components/language-provider";
import { SeeAllEvents, SeeAllAchievements } from "@/components/see-all-button";
import { Button } from "@/components/ui/button";
import {
  Users,
  BookOpen,
  Calendar,
  Award,
  TrendingUp,
  Heart,
  Brain,
  Star,
  Leaf,
  Clock,
} from "lucide-react";

// Dynamic community stats based on real data
const getCommunityStats = (
  totalUsers: number,
  learningPaths: any[],
  events: any[],
  achievements: any[],
) => [
  {
    label: "Active Members",
    value: totalUsers.toLocaleString(),
    icon: Users,
    color: "text-blue-600",
  },
  {
    label: "Learning Paths",
    value: learningPaths.length.toString(),
    icon: BookOpen,
    color: "text-green-600",
  },
  {
    label: "Live Sessions",
    value: events.length.toString(),
    icon: Calendar,
    color: "text-red-600",
  },
  {
    label: "Achievements",
    value: achievements.length.toString(),
    icon: Award,
    color: "text-purple-600",
  },
];



export default function CommunityPage() {
  const { user } = useAuth();
  const { t } = useLanguage();
  const {
    totalUsers,
    learningPaths: dataLearningPaths,
    upcomingEvents: dataUpcomingEvents,
    achievements: dataAchievements
  } = useData();
  const [activeTab, setActiveTab] = useState("overview");
  const [enrolledPaths, setEnrolledPaths] = useState<string[]>([]);

  // Use data from provider, fallback to local data if empty
  const learningPaths = dataLearningPaths.length > 0 ? dataLearningPaths : [
    {
      id: "1",
      title: "Natural Healing Fundamentals",
      description: "Master the basics of natural healing through cupping, plants, and traditional methods",
      duration: "6 weeks",
      modules: 8,
      enrolled: 234,
      difficulty: "Beginner" as const,
      instructor: "Healing Specialist",
      rating: 4.8,
      topics: ["Cupping Therapy", "Plant Medicine", "Natural Foods", "Healing Principles"],
      icon: Heart,
      color: "text-red-600",
    },
    {
      id: "2",
      title: "Intelligence & Logic Development",
      description: "Develop logical thinking, emotional intelligence, and reasoning skills",
      duration: "8 weeks",
      modules: 12,
      enrolled: 189,
      difficulty: "Intermediate" as const,
      instructor: "Logic Instructor",
      rating: 4.9,
      topics: ["Logical Thinking", "Emotional Intelligence", "Problem Solving", "Decision Making"],
      icon: Brain,
      color: "text-blue-600",
    }
  ];

  const upcomingEvents = dataUpcomingEvents.length > 0 ? dataUpcomingEvents : [
    {
      id: "1",
      title: "Live Cupping Therapy Demonstration",
      description: "Watch a professional cupping session and learn proper techniques",
      date: "2024-01-15",
      time: "19:00 UTC",
      duration: "90 minutes",
      instructor: "Healing Specialist",
      attendees: 45,
      maxAttendees: 100,
      type: "Live Demo",
      category: "Health",
      icon: Heart,
      color: "text-red-600",
    }
  ];

  const achievements = dataAchievements.length > 0 ? dataAchievements : [
    {
      id: "1",
      title: "First Steps",
      description: "Complete your first learning module",
      icon: "🌱",
      points: 10,
      rarity: "Common" as const,
      progress: 0,
      requirement: "Complete 1 module",
    }
  ];

  const handleEnrollInPath = (pathId: string) => {
    if (!user) {
      alert('Please sign in to enroll in learning paths');
      return;
    }

    if (enrolledPaths.includes(pathId)) {
      alert('You are already enrolled in this learning path');
      return;
    }

    setEnrolledPaths(prev => [...prev, pathId]);
    alert('Successfully enrolled in learning path!');
  };

  const communityStats = getCommunityStats(
    totalUsers,
    learningPaths,
    upcomingEvents,
    achievements,
  );

  const tabs = [
    {
      id: "overview",
      label: t("community.overview") || "Overview",
      icon: Users,
    },
    {
      id: "learning-paths",
      label: t("community.learningpaths") || "Learning Paths",
      icon: BookOpen,
    },
    { id: "events", label: t("community.events") || "Events", icon: Calendar },
    {
      id: "achievements",
      label: t("community.achievements") || "Achievements",
      icon: Award,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              {t("community.title") || "Community Learning Hub"}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Join our vibrant community of learners exploring natural healing,
              ancient wisdom, and intelligent living. Connect, learn, and grow
              together.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Community Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {communityStats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                      {t(
                        `community.${stat.label.toLowerCase().replace(" ", "")}`,
                      ) || stat.label}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {stat.value}
                    </p>
                  </div>
                  <Icon className={`h-8 w-8 ${stat.color}`} />
                </div>
                <div className="mt-2 flex items-center">
                  <TrendingUp className="h-4 w-4 text-green-500 mr-1" />

                  <span className="text-sm text-green-600">Growing</span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Tab Navigation */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? "border-blue-500 text-blue-600"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          <div className="p-6">
            {/* Overview Tab */}
            {activeTab === "overview" && (
              <div className="space-y-8">
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    Welcome to Our Learning Community
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    Discover structured learning paths, join live events, earn
                    achievements, and connect with fellow learners on a journey
                    of wisdom and growth.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
                    <BookOpen className="h-8 w-8 text-blue-600 mb-4" />

                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      Structured Learning
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Follow comprehensive learning paths designed by experts in
                      natural healing and wisdom traditions.
                    </p>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
                    <Calendar className="h-8 w-8 text-green-600 mb-4" />

                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      Live Events
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Participate in live workshops, Q&A sessions, and community
                      discussions with instructors.
                    </p>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
                    <Award className="h-8 w-8 text-purple-600 mb-4" />

                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      Achievement System
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Earn points and unlock achievements as you progress
                      through your learning journey.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Learning Paths Tab */}
            {activeTab === "learning-paths" && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-8">
                  Structured Learning Paths
                </h2>
                {learningPaths.map((path, index) => {
                  const Icon = path.icon;
                  return (
                    <div
                      key={index}
                      className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <Icon className={`h-8 w-8 ${path.color}`} />

                          <div>
                            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                              {path.title}
                            </h3>
                            <p className="text-gray-600 dark:text-gray-300">
                              {path.description}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {path.enrolled} enrolled
                          </div>
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-500 mr-1" />

                            <span className="text-sm text-gray-600 dark:text-gray-300">
                              {path.rating}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                          <div className="font-semibold text-gray-900 dark:text-white">
                            {path.modules}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            Modules
                          </div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                          <div className="font-semibold text-gray-900 dark:text-white">
                            {path.duration}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            Duration
                          </div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                          <div className="font-semibold text-gray-900 dark:text-white">
                            {path.difficulty}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            Difficulty
                          </div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                          <div className="font-semibold text-gray-900 dark:text-white">
                            {path.instructor}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            Instructor
                          </div>
                        </div>
                      </div>

                      <div className="mb-4">
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                          Topics Covered:
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {path.topics.map((topic, topicIndex) => (
                            <span
                              key={topicIndex}
                              className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm"
                            >
                              {topic}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="flex space-x-4">
                        <Button
                          className="flex-1"
                          onClick={() => handleEnrollInPath(path.id)}
                          disabled={enrolledPaths.includes(path.id)}
                        >
                          {enrolledPaths.includes(path.id)
                            ? "Enrolled ✓"
                            : user
                              ? "Enroll in Path"
                              : "Sign In to Enroll"
                          }
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => {
                            alert(`Learning Path Details:\n\n${path.title}\n\n${path.description}\n\nDuration: ${path.duration}\nModules: ${path.modules}\nDifficulty: ${path.difficulty}\nInstructor: ${path.instructor}\nRating: ${path.rating}/5`);
                          }}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Events Tab */}
            {activeTab === "events" && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-8">
                  Upcoming Community Events
                </h2>
                <SeeAllEvents events={upcomingEvents} />
              </div>
            )}

            {/* Achievements Tab */}
            {activeTab === "achievements" && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-8">
                  Learning Achievements
                </h2>
                <SeeAllAchievements achievements={achievements} />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
