import { Zap, Mountain, Droplets, Wind, Flame, Star, Heart, Brain, Scale, BookOpen, Lightbulb, Compass } from 'lucide-react'

const naturalLaws = [
  {
    name: 'Law of Balance',
    symbol: '⚖️',
    description: 'Everything in nature seeks equilibrium and harmony',
    examples: [
      'Day and night cycles maintain Earth\'s temperature balance',
      'Ecosystems self-regulate through predator-prey relationships',
      'Human body maintains homeostasis automatically',
      'Seasons create natural cycles of growth and rest'
    ],
    applications: [
      'Work-life balance for mental health',
      'Balanced nutrition for physical wellness',
      'Emotional regulation through mindfulness',
      'Sustainable living practices'
    ],
    wisdom: 'Balance is not something you find, it\'s something you create through conscious choices and natural alignment.',
    icon: Scale,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20'
  },
  {
    name: 'Law of Growth',
    symbol: '🌱',
    description: 'All living things naturally tend toward development and expansion',
    examples: [
      'Plants grow toward light sources automatically',
      'Children develop skills through natural curiosity',
      'Healing occurs naturally when conditions are right',
      'Knowledge expands through questioning and learning'
    ],
    applications: [
      'Personal development through continuous learning',
      'Spiritual growth through reflection and practice',
      'Skill development through consistent effort',
      'Character building through challenges'
    ],
    wisdom: 'Growth is the natural state of life. When we align with this law, development becomes effortless and joyful.',
    icon: Lightbulb,
    color: 'text-green-600',
    bgColor: 'bg-green-50 dark:bg-green-900/20'
  },
  {
    name: 'Law of Cycles',
    symbol: '🔄',
    description: 'Nature operates in recurring patterns and rhythms',
    examples: [
      'Seasonal changes create natural renewal cycles',
      'Water cycle maintains Earth\'s freshwater supply',
      'Sleep cycles restore body and mind',
      'Breathing cycles sustain life continuously'
    ],
    applications: [
      'Rest and activity cycles for optimal performance',
      'Learning cycles with practice and reflection',
      'Emotional cycles with expression and processing',
      'Life cycles with growth, contribution, and renewal'
    ],
    wisdom: 'Understanding natural cycles helps us flow with life rather than fighting against its rhythms.',
    icon: Compass,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20'
  },
  {
    name: 'Law of Cause and Effect',
    symbol: '🎯',
    description: 'Every action produces corresponding results in natural systems',
    examples: [
      'Healthy eating leads to better physical condition',
      'Consistent practice develops skills and abilities',
      'Positive actions create beneficial outcomes',
      'Environmental care preserves natural resources'
    ],
    applications: [
      'Intentional choices for desired outcomes',
      'Understanding consequences before acting',
      'Taking responsibility for personal results',
      'Creating positive change through right action'
    ],
    wisdom: 'When we understand cause and effect, we become conscious creators of our experience.',
    icon: Brain,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20'
  },
  {
    name: 'Law of Adaptation',
    symbol: '🦋',
    description: 'Life naturally adjusts to changing conditions for survival and growth',
    examples: [
      'Plants adapt to different climates and soils',
      'Animals develop skills for their environments',
      'Humans learn new abilities when needed',
      'Ecosystems evolve with changing conditions'
    ],
    applications: [
      'Flexibility in facing life challenges',
      'Learning new skills for changing circumstances',
      'Emotional resilience through adaptation',
      'Creative problem-solving approaches'
    ],
    wisdom: 'Adaptation is not just survival - it\'s the art of thriving in any circumstance.',
    icon: Heart,
    color: 'text-pink-600',
    bgColor: 'bg-pink-50 dark:bg-pink-900/20'
  },
  {
    name: 'Law of Unity',
    symbol: '🌐',
    description: 'All elements in nature are interconnected and interdependent',
    examples: [
      'Forest ecosystems support all their inhabitants',
      'Human communities thrive through cooperation',
      'Body systems work together for health',
      'Natural elements combine to create life'
    ],
    applications: [
      'Building supportive relationships',
      'Understanding our connection to nature',
      'Collaborative approaches to challenges',
      'Holistic thinking about problems and solutions'
    ],
    wisdom: 'Recognizing our unity with all life transforms how we see ourselves and our purpose.',
    icon: BookOpen,
    color: 'text-teal-600',
    bgColor: 'bg-teal-50 dark:bg-teal-900/20'
  }
]

export default function NatureLawPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 dark:from-green-900/20 dark:via-blue-900/20 dark:to-purple-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Scale className="h-16 w-16 text-green-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Natural Laws & Universal Principles
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Discover the fundamental laws that govern nature and learn how to align your life
              with these universal principles for harmony, growth, and inner peace.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Wisdom Quote */}
        <div className="verse-container text-center mb-12">
          <div className="flex justify-center mb-4">
            <Zap className="h-8 w-8 text-yellow-600" />
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4">
            "The laws of nature are the thoughts of the divine mind. When we understand and align
            with these laws, we find harmony, peace, and the path to true wisdom."
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Universal Natural Philosophy
          </p>
        </div>

        {/* Awe-Inspiring Development Facts */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Amazing Facts About Natural Laws & Divine Intelligence
          </h2>
          <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-8 border border-green-200 dark:border-green-800">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌍</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">Divine Order in Natural Laws</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Every natural law reflects perfect divine intelligence and order. From the smallest atom to the
                  largest galaxy, everything follows precise laws that maintain harmony and balance. This divine
                  order shows the incredible wisdom and planning in creation.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💎</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">Heart Confirmation Through Natural Understanding</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  When you understand natural laws, your heart confirms the truth of divine wisdom in creation.
                  This inner knowing brings deep peace and understanding of how perfectly everything is designed
                  to work together in harmony and balance.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🧠</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">Intellectual Living Through Natural Wisdom</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Understanding natural laws develops your intelligence about how life works. You learn to think
                  logically about cause and effect, make wise decisions based on natural principles, and live
                  in harmony with the intelligent design of creation.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚖️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">Perfect Balance & Natural Alignment</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Natural laws create perfect balance and harmony throughout creation. When you align your life
                  with these laws, you experience the same balance and peace. This alignment reflects the divine
                  harmony that governs all existence.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌱</span>
                  </div>
                </div>
                <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">Path to Inner Growth & Natural Development</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Each natural law teaches lessons about growth, patience, and development. As you learn from
                  nature's wisdom, your own inner growth accelerates, bringing you closer to understanding the
                  divine principles that govern all healthy development.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">✨</span>
                  </div>
                </div>
                <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">Gateway to Natural Enlightenment</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Natural laws are gateways to enlightenment, revealing the infinite wisdom and intelligence in
                  creation. Through studying and following these laws, you develop deeper appreciation for the
                  divine intelligence that governs everything with perfect wisdom.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Natural Laws */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Fundamental Natural Laws
          </h2>
          <div className="space-y-8">
            {naturalLaws.map((law, index) => {
              const Icon = law.icon
              return (
                <div key={index} className={`${law.bgColor} rounded-lg border p-8`}>
                  <div className="flex items-start space-x-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-lg">
                        <span className="text-3xl">{law.symbol}</span>
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-4">
                        <Icon className={`h-8 w-8 ${law.color}`} />
                        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                          {law.name}
                        </h3>
                      </div>
                      <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
                        {law.description}
                      </p>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                            Natural Examples
                          </h4>
                          <ul className="space-y-2">
                            {law.examples.map((example, exampleIndex) => (
                              <li key={exampleIndex} className="flex items-start">
                                <span className="text-green-500 mr-2 mt-1">•</span>
                                <span className="text-gray-600 dark:text-gray-300 text-sm">{example}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                            Life Applications
                          </h4>
                          <ul className="space-y-2">
                            {law.applications.map((application, appIndex) => (
                              <li key={appIndex} className="flex items-start">
                                <span className="text-blue-500 mr-2 mt-1">•</span>
                                <span className="text-gray-600 dark:text-gray-300 text-sm">{application}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="mt-6 p-4 bg-white dark:bg-gray-800 rounded-lg border-l-4 border-yellow-500">
                        <p className="text-gray-700 dark:text-gray-300 italic">
                          <strong>Wisdom:</strong> {law.wisdom}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-2xl mx-auto">
            <Scale className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Live in Harmony with Natural Laws
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Understanding and applying natural laws brings peace, balance, and wisdom to your life.
              Start by observing these principles in nature and gradually aligning your choices with
              their guidance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <Heart className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <h4 className="font-semibold text-green-800 dark:text-green-300">Inner Peace</h4>
                <p className="text-sm text-green-700 dark:text-green-300">Find harmony through natural alignment</p>
              </div>
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Brain className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <h4 className="font-semibold text-blue-800 dark:text-blue-300">Wisdom</h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">Develop understanding through observation</p>
              </div>
              <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <Lightbulb className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <h4 className="font-semibold text-purple-800 dark:text-purple-300">Growth</h4>
                <p className="text-sm text-purple-700 dark:text-purple-300">Evolve through natural principles</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}