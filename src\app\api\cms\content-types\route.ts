import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';

// GET /api/cms/content-types - Get content types
export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient();

    const { data, error } = await supabase
      .from('cms_content_types')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching content types:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch content types' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || [],
    });
  } catch (error) {
    console.error('Error in GET /api/cms/content-types:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch content types' },
      { status: 500 }
    );
  }
}

// POST /api/cms/content-types - Create content type (admin only)
export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!userData || userData.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await request.json();
    
    // Validate required fields
    if (!body.id || !body.name || !body.schema) {
      return NextResponse.json(
        { success: false, error: 'Content type ID, name, and schema are required' },
        { status: 400 }
      );
    }

    // Create content type
    const { data, error } = await supabase
      .from('cms_content_types')
      .insert({
        ...body,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating content type:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create content type' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Content type created successfully'
    });
  } catch (error) {
    console.error('Error in POST /api/cms/content-types:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create content type' },
      { status: 500 }
    );
  }
}

