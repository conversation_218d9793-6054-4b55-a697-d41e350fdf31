import { Leaf, Droplets, Shield, Zap, Heart, Brain, <PERSON>, Moon } from 'lucide-react'

const healingPlants = [
  {
    name: '<PERSON><PERSON>',
    scientificName: 'Aloe barbadensis',
    benefits: ['Skin healing', 'Anti-inflammatory', 'Digestive support', 'Wound healing'],
    activeCompounds: ['Aloin', 'Polysaccharides', 'Vitamins A, C, E'],
    uses: ['Topical application for burns', 'Digestive health supplement', 'Skin moisturizer'],
    icon: Leaf,
    color: 'text-green-600',
    logic: 'Contains over 75 active compounds that work synergistically to promote healing and reduce inflammation.'
  },
  {
    name: 'Turmeric',
    scientificName: 'Curcuma longa',
    benefits: ['Anti-inflammatory', 'Antioxidant', 'Pain relief', 'Immune support'],
    activeCompounds: ['Curcumin', 'Turmerone', 'Zingiberene'],
    uses: ['Cooking spice', 'Anti-inflammatory supplement', 'Topical paste for wounds'],
    icon: Sun,
    color: 'text-yellow-600',
    logic: 'Curcumin blocks inflammatory pathways at the molecular level, providing natural pain relief without side effects.'
  },
  {
    name: '<PERSON>',
    scientificName: 'Zingiber officinale',
    benefits: ['Digestive aid', 'Anti-nausea', 'Anti-inflammatory', 'Circulation boost'],
    activeCompounds: ['Gingerol', 'Shogaol', 'Zingerone'],
    uses: ['Tea for nausea', 'Cooking ingredient', 'Digestive supplement'],
    icon: Zap,
    color: 'text-orange-600',
    logic: 'Gingerol compounds stimulate digestive enzymes and reduce inflammation in the gastrointestinal tract.'
  },
  {
    name: 'Lavender',
    scientificName: 'Lavandula angustifolia',
    benefits: ['Stress relief', 'Sleep improvement', 'Antimicrobial', 'Skin healing'],
    activeCompounds: ['Linalool', 'Linalyl acetate', 'Camphor'],
    uses: ['Aromatherapy', 'Sleep aid', 'Topical antiseptic'],
    icon: Moon,
    color: 'text-purple-600',
    logic: 'Linalool affects the nervous system by modulating neurotransmitter activity, promoting relaxation and sleep.'
  },
  {
    name: 'Echinacea',
    scientificName: 'Echinacea purpurea',
    benefits: ['Immune support', 'Anti-viral', 'Wound healing', 'Anti-inflammatory'],
    activeCompounds: ['Alkamides', 'Polysaccharides', 'Caffeic acid derivatives'],
    uses: ['Immune system supplement', 'Cold prevention', 'Topical wound treatment'],
    icon: Shield,
    color: 'text-blue-600',
    logic: 'Polysaccharides stimulate macrophage activity, enhancing the body\'s natural immune response.'
  },
  {
    name: 'Ginkgo',
    scientificName: 'Ginkgo biloba',
    benefits: ['Cognitive support', 'Circulation improvement', 'Antioxidant', 'Memory enhancement'],
    activeCompounds: ['Flavonoids', 'Terpenoids', 'Ginkgolides'],
    uses: ['Memory supplement', 'Circulation support', 'Cognitive enhancement'],
    icon: Brain,
    color: 'text-indigo-600',
    logic: 'Flavonoids improve blood flow to the brain while terpenoids protect neurons from oxidative damage.'
  },
  {
    name: 'Eucalyptus',
    scientificName: 'Eucalyptus globulus',
    benefits: ['Respiratory support', 'Antimicrobial', 'Anti-inflammatory', 'Decongestant'],
    activeCompounds: ['Eucalyptol (1,8-cineole)', 'Alpha-pinene', 'Limonene', 'Tannins'],
    uses: ['Steam inhalation for congestion', 'Topical antiseptic', 'Aromatherapy for breathing'],
    icon: Droplets,
    color: 'text-teal-600',
    logic: 'Eucalyptol acts as a natural expectorant and antimicrobial agent, clearing respiratory passages while reducing inflammation in airways.'
  }
]

const plantCategories = [
  {
    category: 'Digestive Health',
    plants: ['Ginger', 'Peppermint', 'Fennel', 'Chamomile'],
    description: 'Plants that support digestive function and gut health through natural compounds.'
  },
  {
    category: 'Respiratory Health',
    plants: ['Eucalyptus', 'Thyme', 'Oregano', 'Pine Needle'],
    description: 'Plants that support breathing, clear airways, and promote respiratory wellness.'
  },
  {
    category: 'Immune Support',
    plants: ['Echinacea', 'Elderberry', 'Astragalus', 'Reishi Mushroom'],
    description: 'Natural immune system boosters that help the body defend against illness.'
  },
  {
    category: 'Stress & Sleep',
    plants: ['Lavender', 'Valerian', 'Passionflower', 'Lemon Balm'],
    description: 'Calming plants that promote relaxation and improve sleep quality.'
  },
  {
    category: 'Cognitive Function',
    plants: ['Ginkgo', 'Gotu Kola', 'Bacopa', 'Rosemary'],
    description: 'Plants that support brain health, memory, and cognitive performance.'
  }
]

export default function PlantsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 dark:from-green-900/20 dark:via-blue-900/20 dark:to-purple-900/20">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Leaf className="h-16 w-16 text-green-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Healing Plants & Natural Medicine
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Explore the science behind medicinal plants and their therapeutic compounds. 
              Understand how nature provides intelligent solutions for health and wellness.
            </p>
          </div>
        </div>
      </div>

      {/* Medical Disclaimer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-orange-800 dark:text-orange-300 mb-2">
            Important Health Disclaimer
          </h3>
          <p className="text-orange-700 dark:text-orange-300 text-sm">
            Plant medicine information is provided for educational purposes only. This content is not intended to
            diagnose, treat, cure, or prevent any disease. Always consult qualified healthcare professionals
            before using plants medicinally, especially if you have health conditions or take medications.
          </p>
        </div>
      </div>

      {/* Scientific Foundation */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="verse-container text-center">
          <div className="flex justify-center mb-4">
            <Brain className="h-8 w-8 text-green-600" />
          </div>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4">
            "In every plant, there is wisdom encoded in molecular structures that interact
            intelligently with human biology to promote healing and balance."
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            - Modern Phytochemistry Research
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* Plant Categories */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Categories of Healing Plants
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {plantCategories.map((category, index) => (
              <div key={index} className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {category.category}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {category.description}
                </p>
                <div className="flex flex-wrap gap-2">
                  {category.plants.map((plant, plantIndex) => (
                    <span
                      key={plantIndex}
                      className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-sm"
                    >
                      {plant}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Awe-Inspiring Development Facts */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Amazing Facts About Plant Intelligence & Natural Healing
          </h2>
          <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-8 border border-green-200 dark:border-green-800">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌿</span>
                  </div>
                </div>
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3 text-center">Divine Plant Intelligence</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Every plant contains thousands of perfectly designed chemical compounds that work together in harmony.
                  This incredible intelligence shows the divine wisdom in creation, where each molecule serves a specific
                  purpose for healing and nourishment.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">💎</span>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 text-center">Heart Confirmation Through Nature</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  When you experience healing through plants, your heart confirms the truth of divine wisdom in nature.
                  This inner knowing brings deep gratitude and understanding of how perfectly everything is created
                  for our benefit and well-being.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🧠</span>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-800 dark:text-purple-300 mb-3 text-center">Intellectual Living Through Plant Wisdom</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Understanding how plants heal develops your intelligence about natural laws and divine design. You
                  learn to think logically about health, make wise choices, and live in harmony with the natural
                  systems that support all life.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚖️</span>
                  </div>
                </div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-3 text-center">Perfect Balance & Natural Alignment</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Plants demonstrate perfect balance in their chemical composition, showing how divine wisdom creates
                  harmony in nature. When you align with natural healing, you experience the same balance in your
                  body, mind, and spirit that reflects divine order.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌱</span>
                  </div>
                </div>
                <h3 className="font-semibold text-teal-800 dark:text-teal-300 mb-3 text-center">Path to Inner Growth & Natural Development</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Each plant teaches lessons about growth, patience, and natural development. As you learn from nature's
                  wisdom, your own inner growth accelerates, bringing you closer to understanding the divine principles
                  that govern all healthy development.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="w-12 h-12 bg-rose-100 dark:bg-rose-800 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl">✨</span>
                  </div>
                </div>
                <h3 className="font-semibold text-rose-800 dark:text-rose-300 mb-3 text-center">Gateway to Natural Enlightenment</h3>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Plants are gateways to enlightenment, revealing the infinite wisdom and mercy in creation. Through
                  studying and using plants mindfully, you develop deeper appreciation for the divine intelligence
                  that provides everything needed for health and happiness.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Plant Information */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Featured Healing Plants
          </h2>
          <div className="space-y-8">
            {healingPlants.map((plant, index) => {
              const Icon = plant.icon
              return (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden"
                >
                  <div className="p-8">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        <Icon className={`h-8 w-8 ${plant.color}`} />
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                            {plant.name}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                            {plant.scientificName}
                          </p>
                        </div>
                      </div>
                      <Leaf className="h-6 w-6 text-green-500" />
                    </div>

                    {/* Logic & Science */}
                    <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <h4 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2">
                        Scientific Logic
                      </h4>
                      <p className="text-blue-700 dark:text-blue-300">
                        {plant.logic}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* Benefits */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Health Benefits
                        </h4>
                        <ul className="space-y-2">
                          {plant.benefits.map((benefit, benefitIndex) => (
                            <li key={benefitIndex} className="flex items-center">
                              <Heart className="h-4 w-4 text-red-500 mr-2" />
                              <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Active Compounds */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Active Compounds
                        </h4>
                        <div className="space-y-2">
                          {plant.activeCompounds.map((compound, compoundIndex) => (
                            <span
                              key={compoundIndex}
                              className="block px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded text-sm"
                            >
                              {compound}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Uses */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Common Uses
                        </h4>
                        <ul className="space-y-2">
                          {plant.uses.map((use, useIndex) => (
                            <li key={useIndex} className="flex items-center">
                              <Droplets className="h-4 w-4 text-blue-500 mr-2" />
                              <span className="text-gray-700 dark:text-gray-300">{use}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Safety & Logic */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Intelligent Use of Plant Medicine
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Scientific Approach
                </h3>
                <ul className="space-y-3 text-gray-700 dark:text-gray-300">
                  <li className="flex items-start">
                    <Brain className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                    <span>Research the active compounds and their mechanisms</span>
                  </li>
                  <li className="flex items-start">
                    <Shield className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                    <span>Understand proper dosages and preparation methods</span>
                  </li>
                  <li className="flex items-start">
                    <Heart className="h-5 w-5 text-red-600 mr-2 mt-0.5" />
                    <span>Consider individual health conditions and medications</span>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Logical Principles
                </h3>
                <ul className="space-y-3 text-gray-700 dark:text-gray-300">
                  <li className="flex items-start">
                    <Zap className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                    <span>Start with small amounts to test individual response</span>
                  </li>
                  <li className="flex items-start">
                    <Leaf className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                    <span>Source plants from reputable, organic suppliers</span>
                  </li>
                  <li className="flex items-start">
                    <Sun className="h-5 w-5 text-orange-600 mr-2 mt-0.5" />
                    <span>Consult healthcare providers for serious conditions</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Nature's Intelligent Design
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Plants have evolved sophisticated chemical compounds over millions of years. 
              By understanding their science, we can harness nature's wisdom for optimal health and wellness.
            </p>
            <div className="flex justify-center space-x-8">
              <div className="text-center">
                <Brain className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Evidence-Based</p>
              </div>
              <div className="text-center">
                <Leaf className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Natural Solutions</p>
              </div>
              <div className="text-center">
                <Heart className="h-8 w-8 text-red-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">Holistic Wellness</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
