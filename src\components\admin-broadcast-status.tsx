'use client'

// Admin Broadcast Status Component
// Shows broadcasting <NAME_EMAIL>

import { useState, useEffect } from 'react'
import { useAuth } from './providers'
import { PlatformService } from '@/lib/platform-service'
import { useActiveRole } from './role-switcher'
import { Video, Shield, Users, CheckCircle, XCircle, Smartphone, Monitor } from 'lucide-react'

export function AdminBroadcastStatus() {
  const { user } = useAuth()
  const { activeRole } = useActiveRole()
  const [userRole, setUserRole] = useState<string>('user')
  const [canBroadcast, setCanBroadcast] = useState(false)
  const [deviceType, setDeviceType] = useState<'mobile' | 'desktop'>('desktop')
  const [cameraSupported, setCameraSupported] = useState(false)
  const [micSupported, setMicSupported] = useState(false)

  useEffect(() => {
    if (user?.email) {
      checkUserPermissions()
    }
    checkDeviceCapabilities()
  }, [user, activeRole]) // eslint-disable-line react-hooks/exhaustive-deps

  const checkUserPermissions = async () => {
    if (!user?.email) return

    try {
      const role = await PlatformService.getUserRole(user.email)

      // Get permissions based on active <NAME_EMAIL>
      const permissions = user.email === '<EMAIL>'
        ? PlatformService.getContextPermissions(user.email, activeRole)
        : PlatformService.getPermissions(role)

      setUserRole(activeRole === 'guest_admin' ? 'guest_admin' : role)
      setCanBroadcast(permissions.canStartStream)
    } catch (error) {
      console.error('Error checking permissions:', error)
    }
  }

  const checkDeviceCapabilities = async () => {
    // Detect device type
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    setDeviceType(isMobile ? 'mobile' : 'desktop')

    // Check media device support
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      try {
        // Check camera
        const videoStream = await navigator.mediaDevices.getUserMedia({ video: true })
        setCameraSupported(true)
        videoStream.getTracks().forEach(track => track.stop())

        // Check microphone
        const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true })
        setMicSupported(true)
        audioStream.getTracks().forEach(track => track.stop())
      } catch (error) {
        console.log('Media devices check:', error)
      }
    }
  }

  if (!user || user.email !== '<EMAIL>') {
    return null
  }

  return (
    <div className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 border border-blue-800 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Shield className="h-5 w-5 text-blue-400" />
          <h3 className="text-lg font-semibold text-blue-300">Admin Broadcasting Status</h3>
        </div>
        <div className="flex items-center space-x-2">
          {deviceType === 'mobile' ? (
            <Smartphone className="h-5 w-5 text-green-400" />
          ) : (
            <Monitor className="h-5 w-5 text-green-400" />
          )}
          <span className="text-sm text-gray-300 capitalize">{deviceType}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* User Role Status */}
        <div className="bg-gray-800/50 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">Role</span>
            {userRole === 'admin' ? (
              <CheckCircle className="h-4 w-4 text-green-400" />
            ) : (
              <XCircle className="h-4 w-4 text-red-400" />
            )}
          </div>
          <div className="text-lg font-semibold text-white capitalize">{userRole}</div>
          <div className="text-xs text-gray-400">
            {userRole === 'admin' ? 'Full admin access' :
             userRole === 'guest_admin' ? 'Stream moderation only' : 'Limited access'}
          </div>
        </div>

        {/* Broadcasting Permission */}
        <div className="bg-gray-800/50 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">Broadcasting</span>
            {canBroadcast ? (
              <CheckCircle className="h-4 w-4 text-green-400" />
            ) : (
              <XCircle className="h-4 w-4 text-red-400" />
            )}
          </div>
          <div className="text-lg font-semibold text-white">
            {canBroadcast ? 'Enabled' : 'Disabled'}
          </div>
          <div className="text-xs text-gray-400">
            {canBroadcast ? 'Can start live streams' :
             userRole === 'guest_admin' ? 'Moderation only' : 'Cannot broadcast'}
          </div>
        </div>

        {/* Device Capabilities */}
        <div className="bg-gray-800/50 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">Media Access</span>
            {cameraSupported && micSupported ? (
              <CheckCircle className="h-4 w-4 text-green-400" />
            ) : (
              <XCircle className="h-4 w-4 text-red-400" />
            )}
          </div>
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <Video className="h-3 w-3" />
              <span className="text-xs text-gray-300">
                Camera: {cameraSupported ? 'Available' : 'Not available'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-3 w-3" />
              <span className="text-xs text-gray-300">
                Microphone: {micSupported ? 'Available' : 'Not available'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Broadcasting Instructions */}
      {canBroadcast && cameraSupported && micSupported && (
        <div className="mt-4 p-3 bg-green-900/20 border border-green-800 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <CheckCircle className="h-4 w-4 text-green-400" />
            <span className="text-sm font-semibold text-green-300">Ready to Broadcast</span>
          </div>
          <p className="text-xs text-green-200">
            You can start live streaming on both {deviceType} and desktop devices.
            Camera and microphone access is available for broadcasting.
          </p>
        </div>
      )}

      {/* Guest Admin Instructions */}
      {userRole === 'guest_admin' && (
        <div className="mt-4 p-3 bg-blue-900/20 border border-blue-800 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <Shield className="h-4 w-4 text-blue-400" />
            <span className="text-sm font-semibold text-blue-300">Stream Moderation Access</span>
          </div>
          <p className="text-xs text-blue-200">
            You have moderation privileges for live streams: delete messages, timeout users,
            pin messages, and mark questions. Broadcasting is restricted to the main admin.
          </p>
        </div>
      )}

      {/* Troubleshooting */}
      {(!cameraSupported || !micSupported) && (
        <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-800 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <XCircle className="h-4 w-4 text-yellow-400" />
            <span className="text-sm font-semibold text-yellow-300">Media Access Issues</span>
          </div>
          <p className="text-xs text-yellow-200">
            {!cameraSupported && 'Camera access not available. '}
            {!micSupported && 'Microphone access not available. '}
            Please check browser permissions and device settings.
          </p>
        </div>
      )}
    </div>
  )
}
