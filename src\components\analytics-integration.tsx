"use client";

import React, { useEffect } from 'react';
import { ConsentManager, PrivacyAnalytics } from '@/lib/privacy-analytics';

/**
 * Analytics Integration Component
 * Manages privacy-respecting analytics with user consent
 * 
 * Features:
 * - Vercel Analytics: Privacy-first page views (no cookies)
 * - Vercel Speed Insights: Core Web Vitals monitoring
 * - Custom privacy analytics with consent management
 * - GDPR compliant data collection
 */
export function AnalyticsIntegration() {
  useEffect(() => {
    // Initialize privacy analytics based on user consent
    const consent = ConsentManager.getConsent();
    
    if (consent?.analytics) {
      // Initialize custom privacy analytics
      const analytics = new PrivacyAnalytics();
      
      // Track page view with privacy protection
      analytics.track('page_view', {
        path: window.location.pathname,
        referrer: document.referrer ? new URL(document.referrer).hostname : 'direct',
        timestamp: Date.now()
      });

      // Track performance metrics (complement Vercel Speed Insights)
      let trackPerformance: (() => void) | null = null;

      if ('performance' in window) {
        // Track custom performance metrics
        trackPerformance = () => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          if (navigation) {
            analytics.track('performance', {
              page_load_time: navigation.loadEventEnd - navigation.fetchStart,
              dom_content_loaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
              first_paint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
              connection_type: (navigator as any).connection?.effectiveType || 'unknown'
            });
          }
        };

        // Track after page load
        if (document.readyState === 'complete') {
          trackPerformance();
        } else {
          window.addEventListener('load', trackPerformance);
        }
      }

      // Track user engagement (privacy-safe)
      let engagementStartTime = Date.now();
      let isActive = true;

      const trackEngagement = () => {
        if (isActive) {
          const engagementTime = Date.now() - engagementStartTime;
          if (engagementTime > 10000) { // Only track if user spent more than 10 seconds
            analytics.track('engagement', {
              time_on_page: Math.round(engagementTime / 1000),
              page_path: window.location.pathname
            });
          }
        }
      };

      // Track when user becomes inactive
      const handleVisibilityChange = () => {
        if (document.hidden) {
          isActive = false;
          trackEngagement();
        } else {
          isActive = true;
          engagementStartTime = Date.now();
        }
      };

      // Track before page unload
      const handleBeforeUnload = () => {
        trackEngagement();
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('beforeunload', handleBeforeUnload);

      // Cleanup
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('beforeunload', handleBeforeUnload);
        if (trackPerformance) {
          window.removeEventListener('load', trackPerformance);
        }
      };
    }
  }, []);

  return null; // This component doesn't render anything
}

/**
 * Analytics Status Component
 * Shows current analytics status and privacy settings
 */
export function AnalyticsStatus() {
  const [consent, setConsent] = React.useState<any>(null);
  const [mounted, setMounted] = React.useState(false);

  useEffect(() => {
    setMounted(true);
    setConsent(ConsentManager.getConsent());
  }, []);

  if (!mounted) return null;

  return (
    <div className="text-xs text-gray-500 space-y-1">
      <div className="font-medium">Analytics Status:</div>
      <div className="space-y-1">
        <div className="flex items-center justify-between">
          <span>Vercel Analytics:</span>
          <span className="text-green-500">✓ Active (Privacy-first)</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Speed Insights:</span>
          <span className="text-green-500">✓ Active (Performance only)</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Custom Analytics:</span>
          <span className={consent?.analytics ? "text-green-500" : "text-gray-400"}>
            {consent?.analytics ? "✓ Active (Consented)" : "○ Disabled (No consent)"}
          </span>
        </div>
      </div>
      <div className="text-xs text-gray-400 mt-2">
        All analytics are GDPR compliant and privacy-respecting
      </div>
    </div>
  );
}

/**
 * Analytics Configuration
 * Information about the analytics setup
 */
export const ANALYTICS_INFO = {
  vercel: {
    analytics: {
      name: "Vercel Analytics",
      description: "Privacy-first page view tracking without cookies",
      dataCollected: ["Page views", "Referrer domains", "Country (approximate)"],
      privacy: "No personal data, no cookies, GDPR compliant",
      retention: "90 days"
    },
    speedInsights: {
      name: "Vercel Speed Insights",
      description: "Core Web Vitals and performance monitoring",
      dataCollected: ["LCP", "FID", "CLS", "FCP", "TTFB", "Page load times"],
      privacy: "Performance metrics only, no personal data",
      retention: "30 days"
    }
  },
  custom: {
    name: "Custom Privacy Analytics",
    description: "Consent-based analytics with data anonymization",
    dataCollected: ["Anonymized page views", "Performance metrics", "User engagement"],
    privacy: "Requires explicit consent, data anonymized, PII removed",
    retention: "26 months (configurable)"
  }
} as const;
